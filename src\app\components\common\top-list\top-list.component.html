<div class="top-list-container">
  <div class="top-list-header">
    <div class="top-list-tab" [ngClass]="{ 'top-list-tab-active': topType === 0 }" (click)="getTopComic(0)">
      TOP NGÀY
    </div>
    <div class="top-list-tab" [ngClass]="{ 'top-list-tab-active': topType === 1 }" (click)="getTopComic(1)">
      TOP TUẦN
    </div>
    <div class="top-list-tab" [ngClass]="{ 'top-list-tab-active': topType === 2 }" (click)="getTopComic(2)">
      TOP THÁNG
    </div>
  </div>

  <div class="top-list-content no-scrollbar">
    <ul class="top-list">
      <li *ngFor="let topComic of listTopComics; let i = index" class="top-list-item">
        <div class="top-list-item-content" [attr.data-id]="topComic.id">
          <a class="top-list-item-image" [title]="topComic.title"
            [routerLink]="['/truyen-tranh', topComic.url + '-' + topComic.id]">
            <div class="top-list-item-rank">
              {{ i + 1 }}
            </div>

            <img loading="lazy" class="top-list-item-cover" [src]="topComic.coverImage" [alt]="topComic.title"
              onerror="this.src='/option2.png'" />

          </a>

          <div class="top-list-item-info">
            <a [routerLink]="[
                '/truyen-tranh',
                topComic.url + '-' + topComic.id
              ]" class="top-list-item-title">
              <h3 class="text-center xl:text-left line-clamp-2">
                {{ topComic.title }}
              </h3>
            </a>

            <div class="top-list-item-view">
              <svg class="top-list-item-view-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>

              <p class="top-list-item-view-count">
                {{ topComic.viewCount | numeral : "0.0a" }}
              </p>
            </div>
            <p class="top-list-item-chapter hidden lg:block">
              <a [routerLink]="[
                  '/truyen-tranh',
                  topComic.url + '-' + topComic.id
                ]" [title]="topComic?.chapters?.[0]?.title">
                {{ topComic.chapters?.[0]?.title?.split(":")?.[0] }}
              </a>
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</div>