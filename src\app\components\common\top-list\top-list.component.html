<!-- Modern Top List Component - Comic Website Design -->
<div class="top-list-container">
  <!-- En<PERSON><PERSON> <PERSON><PERSON> with Tabs -->
  <div class="top-list-header">
    <div class="header-title-section !hidden">
      <svg class="header-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <polygon
          points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"
        />
      </svg>
      <h2 class="header-title">Bảng xếp hạng</h2>
    </div>

    <div class="tab-navigation">
      <button
        class="tab-button"
        [class.tab-active]="topType === 0"
        (click)="getTopComic(0)"
        type="button"
      >
        <svg class="tab-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10" />
          <polyline points="12,6 12,12 16,14" />
        </svg>
        <span class="tab-text">Ngày</span>
      </button>

      <button
        class="tab-button"
        [class.tab-active]="topType === 1"
        (click)="getTopComic(1)"
        type="button"
      >
        <svg class="tab-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
          <line x1="16" y1="2" x2="16" y2="6" />
          <line x1="8" y1="2" x2="8" y2="6" />
          <line x1="3" y1="10" x2="21" y2="10" />
        </svg>
        <span class="tab-text">Tuần</span>
      </button>

      <button
        class="tab-button"
        [class.tab-active]="topType === 2"
        (click)="getTopComic(2)"
        type="button"
      >
        <svg class="tab-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
          <line x1="16" y1="2" x2="16" y2="6" />
          <line x1="8" y1="2" x2="8" y2="6" />
          <line x1="3" y1="10" x2="21" y2="10" />
          <path d="M8 14h.01M12 14h.01M16 14h.01M8 18h.01M12 18h.01M16 18h.01" />
        </svg>
        <span class="tab-text">Tháng</span>
      </button>
    </div>
  </div>

  <!-- Enhanced Content Area -->
  <div class="top-list-content">
    <!-- Empty State -->
    <div *ngIf="!listTopComics || listTopComics.length === 0" class="empty-state">
      <div class="empty-state-content">
        <svg class="empty-state-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
        </svg>
        <h3 class="empty-state-title">Chưa có dữ liệu</h3>
        <p class="empty-state-description">Danh sách top truyện đang được cập nhật</p>
      </div>
    </div>

    <!-- Top Comics List -->
    <div *ngIf="listTopComics && listTopComics.length > 0" class="top-comics-list">
      <div
        *ngFor="let topComic of listTopComics; let i = index; trackBy: trackByComicId"
        class="top-comic-item"
      >
        <div class="comic-item-content">
          <!-- Rank Badge -->
          <div class="rank-badge" [class]="getRankClass(i + 1)">
            <span class="rank-number">{{ i + 1 }}</span>
          </div>

          <!-- Comic Image -->
          <div class="comic-image-container">
            <a
              [routerLink]="['/truyen-tranh', topComic.url + '-' + topComic.id]"
              class="comic-image-link"
              [title]="topComic.title"
            >
              <img
                loading="lazy"
                class="comic-image"
                [src]="topComic.coverImage"
                [alt]="topComic.title"
                onerror="this.src='/option2.png'"
              />
              <div class="image-overlay"></div>
            </a>
          </div>

          <!-- Comic Info -->
          <div class="comic-info">
            <div class="comic-main-info">
              <!-- Title -->
              <h3 class="comic-title">
                <a
                  [routerLink]="['/truyen-tranh', topComic.url + '-' + topComic.id]"
                  class="comic-title-link"
                  [title]="topComic.title"
                  >{{ topComic.title }}</a
                >
              </h3>

              <!-- Chapter (Desktop only) -->
              <div class="comic-chapter" *ngIf="topComic.chapters && topComic.chapters.length > 0">
                <svg class="chapter-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                  <polyline points="14,2 14,8 20,8" />
                  <line x1="16" y1="13" x2="8" y2="13" />
                  <line x1="16" y1="17" x2="8" y2="17" />
                  <polyline points="10,9 9,9 8,9" />
                </svg>
                <a
                  [routerLink]="['/truyen-tranh', topComic.url + '-' + topComic.id]"
                  class="comic-chapter-link"
                  [title]="topComic.chapters[0].title"
                  >Chapter {{ topComic.chapters[0].slug }}</a
                >
              </div>
            </div>

            <!-- Comic Stats -->
            <div class="comic-stats">
              <div class="view-count">
                <svg class="view-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                <span class="view-count-text">{{ topComic.viewCount | numeral : '0.0a' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
