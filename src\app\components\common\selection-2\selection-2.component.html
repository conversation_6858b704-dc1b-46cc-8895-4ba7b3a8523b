<div
  (appClickOutside)="closeDropdown()"
  class="relative inline-flex w-full items-center text-black dark:text-white"
  [ngClass]="size"
>
  <button
    [title]="this.selectedIdx >= 0 ? options[this.selectedIdx].label : 'None'"
    class="w-full flex justify-between px-2 py-1 rounded-md transition bg-gray-100 dark:bg-neutral-800 outline outline-1 focus:outline-primary focus:text-primary-100 font-medium"
    [ngClass]="{
      'outline outline-primary-100': isDropdownOpen,
      'hover:outline hover:outline-primary-100': !isDropdownOpen
    }"
    (click)="toggleDropdown()"
  >
    <ng-container [ngTemplateOutlet]="customBtn || selectButton" />
  </button>
  <ul
    [ngClass]="{ hidden: !isDropdownOpen }"
    class="scrollbar-style-1 flex absolute flex-col mt-1 border w-full max-h-96 overflow-y-auto z-50 left-0 rounded-md shadow-md py-1 top-full bg-white dark:bg-neutral-800 dark:border-gray-500"
  >
    <li
      *ngFor="let option of options; let i = index"
      (click)="selectOption(option)"
      [ngClass]="{
        ' bg-primary-100 text-white': i === selectedIdx,
        'hover:bg-gray-200 hover:text-secondary-100 dark:hover:bg-neutral-700 dark:hover:text-white':
          i !== selectedIdx
      }"
      class="grid grid-cols-[0.75rem_1fr] py-1 cursor-pointer transition-colors mx-1 rounded-md my-0.5"
    >
      <div
        class="h-3 w-[2px] rounded-full my-auto transition-colors"
        [ngClass]="{
          'bg-primary-100': i === selectedIdx,
          'bg-transparent': i !== selectedIdx
        }"
      ></div>
      <span class="truncate">
        <label class="custom-radio flex items-center cursor-pointer">
          <input
            type="radio"
            [checked]="i === selectedIdx"
            class="hidden flex-shrink-0"
            aria-hidden="true"
          />
          <span
            class="custom-radio-indicator w-3 h-3 border rounded-full mr-2 transition-colors"
            [ngClass]="{
              'border-white bg-primary-100': i === selectedIdx,
              'border-gray-400': i !== selectedIdx
            }"
          ></span>
          {{ option.label }}
        </label>
      </span>
    </li>
  </ul>
</div>

<ng-template #selectButton>
  <span class="text-left truncate font-semibold">
    {{ this.selectedIdx >= 0 ? options[this.selectedIdx].label : 'None' }}
  </span>
  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 my-auto" viewBox="0 0 24 24">
    <path
      fill="none"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="m8 9 4-4 4 4m0 6-4 4-4-4"
    ></path>
  </svg>
</ng-template>

<ng-content />
