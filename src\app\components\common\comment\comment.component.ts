import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  QueryList,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
} from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Comment, IUser } from '@schema';
import {
  animate,
  keyframes,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule, DOCUMENT } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { EmojiPipe } from '@pines/emoji.pipe';
import { AccountService } from '@services/account.service';
import { PopupService } from '@services/popup.service';
import { ThemeService } from '@services/theme.service';
import { ToastService, ToastType } from '@services/toast.service';
import { PaginationComponent } from '../pagination/pagination.component';
import { EmojiComponent } from '../../lazy/emoji/emoji.component';
import { OptimizedBaseComponent } from '../base/optimized-base.component';
import { Subject, debounceTime } from 'rxjs';

@Component({
  selector: 'app-comment',
  templateUrl: './comment.component.html',
  styleUrl: './comment.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('fadeInAnimation', [
      state('in', style({ opacity: 1 })),
      transition(':enter', [
        animate('300ms ease-in', keyframes([
          style({ opacity: 0, transform: 'translateY(10px)', offset: 0 }),
          style({ opacity: 0.5, transform: 'translateY(5px)', offset: 0.5 }),
          style({ opacity: 1, transform: 'translateY(0)', offset: 1 }),
        ])),
      ]),
    ]),
  ],
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    EmojiComponent,
    FormsModule,
    EmojiPipe,
    DateAgoPipe,
    PaginationComponent,
    ReactiveFormsModule,
    ClickOutsideDirective
  ],
})
export class CommentComponent extends OptimizedBaseComponent implements OnInit, OnChanges, OnDestroy {
  // Form controls
  formComment!: FormGroup;
  formReply!: FormGroup;

  // Input properties
  @Input() comicId!: number;
  @Input() chapterID!: number;

  @ViewChild('emojiPickerContainer' , { read: ViewContainerRef }) emojiPickerContainer!: ViewContainerRef;

  // Component state
  listComments: Comment[] = [];
  lengthComments = 0;
  replyId = -1;
  isViewReply = false;
  isLogin = false;
  beginState = true;

  // Pagination
  currentPage = 1;
  totalPages = 0;

  // UI state
  @ViewChildren('ViewReplyEle') viewReplyElements!: QueryList<ElementRef>;
  activeEmojiPicker = false;
  activeEmojiPicker2 = false;
  isTyping = false;

  // Constants and user data
  user?: IUser;
  levelInfoUser!: { percent: number; level: string; nextLevel: string };

  // Performance optimizations
  private scrollSubject = new Subject<Event>();
  private readonly COMMENTS_PER_PAGE = 10;
  private readonly SCROLL_DEBOUNCE_TIME = 100;

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private formBuilder: FormBuilder,
    private accountService: AccountService,
    public themeService: ThemeService,
    private myElement: ElementRef,
    private toastService: ToastService,
    private popupService: PopupService,
    @Inject(DOCUMENT) private document: Document
  ) {
    super(cdr, platformId);

    this.initializeForms();
    this.setupScrollHandler();
  }

  // Computed properties for better performance
  get hasComments(): boolean {
    return this.listComments.length > 0;
  }

  get showPagination(): boolean {
    return this.totalPages > 1;
  }

  get commentsCount(): number {
    return this.lengthComments;
  }

  // TrackBy functions for ngFor optimization
  trackByCommentId = (index: number, comment: Comment): number => {
    return comment.id;
  };

  trackByReplyId = (index: number, reply: Comment): number => {
    return reply.id;
  };

  private initializeForms(): void {
    this.formComment = this.formBuilder.group({
      content: ['', Validators.required],
    });

    this.formReply = this.formBuilder.group({
      content: ['', Validators.required],
      replyfromUser: [-1, Validators.required],
    });
  }

  private setupScrollHandler(): void {
    if (this.isBrowser) {
      this.scrollSubject
        .pipe(
          debounceTime(this.SCROLL_DEBOUNCE_TIME),
          this.takeUntilDestroy()
        )
        .subscribe(() => this.handleScroll());
    }
  }
  ngOnInit(): void {
    this.isLogin = this.accountService.isAuthenticated();
    this.user = this.accountService.GetUser();
  }

  override ngOnDestroy(): void {
    if (this.isBrowser) {
      this.document.removeEventListener('scroll', this.windowScrollHandler);
    }
    super.ngOnDestroy();
  }

  ngOnChanges(): void {
    this.setupScrollListener();
  }

  clickOutSite(): void {
    this.activeEmojiPicker = false;
  }

  private handleScroll(): void {
    const screenBottomOffset =
      (this.document?.defaultView?.scrollY || 0) +
      (this.document?.defaultView?.innerHeight || 0);

    if (
      screenBottomOffset >= this.myElement.nativeElement.offsetTop &&
      this.beginState
    ) {
      this.beginState = false;
      this.refreshComments();
      this.removeScrollListener();
    }
  }

  private windowScrollHandler = (): void => {
    this.scrollSubject.next(new Event('scroll'));
  };

  private setupScrollListener(): void {
    if (this.isBrowser) {
      this.removeScrollListener();
      this.document.addEventListener('scroll', this.windowScrollHandler);
    }
  }

  private removeScrollListener(): void {
    if (this.isBrowser) {
      this.document.removeEventListener('scroll', this.windowScrollHandler);
    }
  }

  refreshComments(): void {
    this.listComments = [];
    this.lengthComments = 0;

    this.accountService
      .GetCommentsByComicId(this.comicId, this.currentPage, this.COMMENTS_PER_PAGE)
      .pipe(this.takeUntilDestroy())
      .subscribe((res: any) => {
        this.totalPages = res.data.totalpage;
        this.listComments = res.data.comments;

        this.calculateCommentsLength();
        this.cd.markForCheck();
      });
  }

  private calculateCommentsLength(): void {
    this.lengthComments = this.listComments.reduce((total, comment) => {
      return total + 1 + (comment.replies?.length || 0);
    }, 0);
  }

  onSubmit(form: FormGroup, replyfromCmt: number | undefined = undefined) {
    if (!form.valid) return;
    const userId = form.value.replyfromUser ?? null;
    this.accountService
      .AddComment(this.chapterID, form.value.content, userId, replyfromCmt)
      .subscribe((res: any) => {
        if (res.status === 1) {
          res.data.avatar = this.user?.avatar;

          if (!replyfromCmt) {
            this.listComments.unshift(res.data);
          } else {
            const parentComment = this.listComments.find(
              (comment: any) => comment.id === replyfromCmt
            );
            if (parentComment) {
              if (parentComment.replies) parentComment.replies.push(res.data);
              else parentComment.replies = [res.data];
            }
          }
          this.lengthComments += 1;
          this.cd.markForCheck();

        }
      });
    form.reset();
    this.activeEmojiPicker = false;
  }
  replyCmt(comment: Comment, hide: boolean) {    
    if (!this.isLogin) {
      this.toastService.show(
        ToastType.Error,
        'Vui lòng đăng nhập để phản hồi bình luận'
      );
      return;
    }
    this.formReply.reset();
    const commentId = comment.id;
    this.isViewReply = false;
    let content = '';
    if (comment.userID !== this.user?.id) {
      content = `@${comment.userName} `;
    }
    this.formReply.setValue({ content, replyfromUser: comment.id });

    if (!hide) {
      this.replyId = commentId;
      const element = this.viewReplyElements.find(
        (el: ElementRef) =>
          el.nativeElement.getAttribute('reply-block') === commentId.toString()
      );
      element?.nativeElement.classList.remove('h-0');
    } else {
      const element = this.viewReplyElements.find(
        (el: ElementRef) =>
          el.nativeElement.querySelector(`#id${commentId}`) !== null
      );
      this.replyId = parseInt(element?.nativeElement.getAttribute('reply-block') || '-1');
      element?.nativeElement.classList.remove('h-0');
      
    }
  }

  ViewReplyCmt(commentId: number): void {
    if (this.replyId === commentId) {
      this.isViewReply = !this.isViewReply;
    }

    const element = this.viewReplyElements.find(
      (el: ElementRef) =>
        el.nativeElement.getAttribute('reply-block') === commentId.toString()
    );
    element?.nativeElement.classList.toggle('h-0');
  }
  ViewInfoUser(userID: number) {
    this.popupService.showUserInfo({ userID });
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll(event: Event) {
    if ((event.target as Document).defaultView?.scrollY === 0) {
      this.beginState = true;
    }
  }
  onInput(form: FormGroup): void {
    this.isTyping = form.get('content')?.value.length > 0;
  }


  toggleEmojiPicker(): void {
    this.activeEmojiPicker = !this.activeEmojiPicker;
  }

  addEmoji(event: any, form: FormGroup): void {
    const currentContent = form.get('content')?.value || '';
    const emojiTag = `<e>${event.name}</e> `;
    form.patchValue({ content: currentContent + emojiTag });

    this.onSubmit(form);
  }
  OnChangePage(page: number) {
    this.currentPage = page;
    this.refreshComments();
  }
}
