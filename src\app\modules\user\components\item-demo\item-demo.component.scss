
.item-demo-container {
  @apply max-w-7xl mx-auto p-4 space-y-8;
}

// Demo Header
.demo-header {
  @apply text-center py-8 bg-gradient-to-r from-blue-500 to-purple-600;
  @apply text-white rounded-xl shadow-lg;
}

.demo-title {
  @apply text-3xl md:text-4xl font-bold mb-2;
}

.demo-subtitle {
  @apply text-lg md:text-xl opacity-90;
}

// Demo Controls
.demo-controls {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6;
  @apply border border-gray-200 dark:border-gray-700;
}

.control-section {
  @apply space-y-6;
}

.control-title {
  @apply text-xl font-bold text-gray-900 dark:text-gray-100 mb-4;
}

.control-group {
  @apply space-y-3;
}

.control-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.control-row {
  @apply flex flex-wrap gap-3 items-center;
}

.control-select, .control-input {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
  @apply rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.control-select {
  @apply min-w-[200px];
}

.control-input {
  @apply w-24;
}

.control-btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;

  &--primary {
    @apply bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500;
  }

  &--secondary {
    @apply bg-green-500 text-white hover:bg-green-600 focus:ring-green-500;
  }

  &--warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500;
  }

  &--danger {
    @apply bg-red-500 text-white hover:bg-red-600 focus:ring-red-500;
  }
}

// Info Section
.info-section {
  @apply mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg;
  @apply border border-blue-200 dark:border-blue-700;
}

.info-title {
  @apply font-semibold text-blue-900 dark:text-blue-100 mb-3;
}

.info-list {
  @apply space-y-2 text-sm text-blue-800 dark:text-blue-200;
}

// Demo Sections
.demo-section {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6;
  @apply border border-gray-200 dark:border-gray-700;
}

.section-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6;
}

// Features Section
.features-section {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6;
  @apply border border-gray-200 dark:border-gray-700;
}

.features-title {
  @apply text-xl font-bold text-gray-900 dark:text-gray-100 mb-6;
}

.features-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.feature-card {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-4;
  @apply border border-gray-200 dark:border-gray-600;
}

.feature-title {
  @apply font-semibold text-gray-900 dark:text-gray-100 mb-3;
}

.feature-list {
  @apply space-y-1 text-sm text-gray-600 dark:text-gray-400;
}

// Mock Data Section
.mock-data-section {
  @apply bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20;
  @apply rounded-xl shadow-lg p-6 border border-purple-200 dark:border-purple-700;
}

.mock-title {
  @apply text-xl font-bold text-purple-900 dark:text-purple-100 mb-4;
}

.mock-info {
  @apply space-y-4;
}

.mock-description {
  @apply text-purple-800 dark:text-purple-200;
  
  code {
    @apply bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded text-sm;
    @apply font-mono text-purple-900 dark:text-purple-100;
  }
}

.mock-stats {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.stat-item {
  @apply text-center p-3 bg-white dark:bg-gray-800 rounded-lg;
  @apply border border-purple-200 dark:border-purple-700;
}

.stat-number {
  @apply block text-2xl font-bold text-purple-600 dark:text-purple-400;
}

.stat-label {
  @apply block text-sm text-purple-700 dark:text-purple-300;
}

// Responsive adjustments
@screen sm {
  .item-demo-container {
    @apply p-6;
  }
  
  .demo-title {
    @apply text-4xl;
  }
  
  .control-row {
    @apply flex-nowrap;
  }
}

@screen md {
  .features-grid {
    @apply grid-cols-2;
  }
  
  .mock-stats {
    @apply grid-cols-4;
  }
}

@screen lg {
  .features-grid {
    @apply grid-cols-4;
  }
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .demo-header {
    @apply from-blue-600 to-purple-700;
  }
}
