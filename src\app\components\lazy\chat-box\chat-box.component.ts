import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { EmojiComponent } from '@components/lazy/emoji/emoji.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { ChatbotService } from '@services/chatbot.service';
import { ToastService, ToastType } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';
@Component({
  imports: [CommonModule, EmojiComponent, ClickOutsideDirective,],
  selector: 'app-chat-box',
  templateUrl: './chat-box.component.html',
  styleUrl: './chat-box.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ChatBoxComponent implements OnInit, AfterViewInit, IPopupComponent {
  hideChatChange = new EventEmitter<boolean>();
  isShowChatBox = true;
  isShowChatMenu = false;
  activeEmojiPicker = false;
  @ViewChild('chatBox') chatBoxRef?: ElementRef;
  @ViewChild('chatMenu') chatMenuRef?: ElementRef;
  loginUser: IUser | undefined = undefined
  contactList = [
    {
      userId: 2,
      name: 'AI Chat bot',
      path: '/option1.png',
      time: '12:09 PM',
      preview: 'Tính năng đã ra mắt',
      messages: [
        {
          fromUserId: 0,
          toUserId: 2,
          text: 'Xin chào bạn. Moi có thể giúp gì cho bạn?',
          time: '',
        },

      ],
      active: false,
    },
    {
      userId: 1,
      name: 'Chat thế giới',
      path: '/option3.png',
      time: '2:09 PM',
      preview: 'Tính năng đang phát triển...',
      messages: [
        {
          fromUserId: 0,
          toUserId: 1,
          text: 'Xin chào, tôi đang sống ở Hà Nội',
          time: '',
        },
        {
          fromUserId: 0,
          toUserId: 1,
          text: 'Còn bạn đến từ đâu?',
          time: '',
        },
        {
          fromUserId: 1,
          toUserId: 0,
          text: 'Tính năng đang tạm ngưng để phát triển',
          time: '',
        },
        {
          fromUserId: 1,
          toUserId: 0,
          text: 'Vui lòng thử lại sau',
          time: '',
        },
        {
          fromUserId: 0,
          toUserId: 1,
          text: 'Oke bạn',
          time: '',
        },
        {
          fromUserId: 0,
          toUserId: 1,
          text: 'Tôi sẽ chờ',
          time: '',
        },
        {
          fromUserId: 0,
          toUserId: 1,
          text: 'Cảm ơn vì tất cả.',
          time: '',
        },
      ],
      active: true,
    },
  ];
  searchUser = '';
  textMessage: string | null = '';
  selectedUser: any = null;
  constructor(
    private cdref: ChangeDetectorRef,
    private chabotService: ChatbotService,
    private accountService: AccountService,
    private toastService: ToastService,
  ) {
  
  }
  show(object: any): Promise<any> {
    this.isShowChatBox = true;
    this.cdref.detectChanges();
    return new Promise((resolve) => {
      resolve({});
    });
  }
  ngOnInit(): void {
    this.loginUser = this.accountService.GetUser()
    this.selectUser(this.contactList[0]);
  }
  ngAfterViewInit() {
    this.scrollToBottom();
  }
  searchUsers() {
    return this.contactList.filter((d: { name: string }) => {
      return d.name.toLowerCase().includes(this.searchUser);
    });
  }

  selectUser(user: any) {
    this.selectedUser = user;
    this.isShowChatMenu = false;
    this.scrollToBottom();
  }

  toggleEmojiPicker() {
    this.activeEmojiPicker = !this.activeEmojiPicker;
  }

  sendMessage() {

    const messager: any = this.contactList.find(
      (d: { userId: any }) => d.userId === this.selectedUser.userId
    );
    messager.messages.push({
      fromUserId: this.selectedUser.userId,
      toUserId: 0,
      text: this.textMessage,
      time: 'Just now',
    });
    if (this.textMessage) {
      const eventSource = this.chabotService.Chat(
        this.textMessage,
        false,
        5
      );
      if (!eventSource) {
        this.toastService.show(ToastType.Error, 'Vui lòng đăng nhập để sử dụng ChatBot!')
        return
      }
      let isFirstChunk = true;
      eventSource.onmessage = (event: any) => {
        try {

          const parsedData = JSON.parse(event.data);
          if (parsedData.text === '[DONE]') {
            eventSource.close();
          } else if (parsedData.text) {
            const partialText =
              this.processTemplate(parsedData.text);

            if (isFirstChunk) {
              messager.messages.push({
                fromUserId: 0,
                toUserId: 2,
                text: partialText,
                time: 'Just now',
              });
              isFirstChunk = false;
            } else {
              messager.messages[messager.messages.length - 1].text += partialText;
            }

            this.cdref.detectChanges(); // Cập nhật giao diện
            this.scrollToBottom(); // Cuộn xuống cuối
          }
        } catch (e) {
          console.error('Failed to parse event data:', e);
        }


      };
      eventSource.onerror = (error: any) => {
        console.error('EventSource error:', error);
        eventSource.close(); // Close EventSource on error
      };
      this.textMessage = '';
      this.cdref.detectChanges();
      this.scrollToBottom();
    }
  }
  processTemplate(text: string) {
    // Loại bỏ hoặc thay thế ký tự template
    return text
      .replace(/\*\*(.*?)\*\*/g, '<b>$1</b>') // Bold (**text** -> <b>text</b>)
      .replace(/\*(.*?)\*/g, '<i>$1</i>') // Italic (*text* -> <i>text</i>)
      .replace(/\{(.*?)\}/g, '($1)') // Curly braces ({text} -> (text))
      .replace(/_/g, ' '); // Underscore (_ -> space)
  }
  closeChat() {
    this.hideChatChange.emit(true);
  }
  setVisible(isVisible: boolean) {
    this.isShowChatBox = isVisible;
  }

  msgChange(event: any) {
    this.textMessage = event.target.value;
  }

  addEmoji(event: any): void {
    return;
  }

  scrollToBottom() {
    // const chatBox = document.getElementById('chatBox');
    const chatBox = this.chatBoxRef?.nativeElement;
    chatBox?.scrollTo({
      top: chatBox.scrollHeight,
      left: 0,
      behavior: 'smooth',
    });
  }
}
