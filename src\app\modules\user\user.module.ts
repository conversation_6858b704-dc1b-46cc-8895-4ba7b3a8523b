import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EyeIconComponent } from '@components/common/eye-icon/eye-icon.component';
import { QuestSchedulerService } from './services/quest-scheduler.service';
import { UserRoutingModule } from './user.routing';

@NgModule({
  imports: [UserRoutingModule, CommonModule, ReactiveFormsModule, FormsModule, EyeIconComponent],
  providers: [QuestSchedulerService]
})
export class UserModule { }
