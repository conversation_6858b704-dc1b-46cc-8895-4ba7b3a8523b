import { Injectable } from '@angular/core';
import globalConfig from 'GlobalConfig';

import { HttpClient } from '@angular/common/http';
import { EventSourcePolyfill } from "event-source-polyfill";

import { AccountService } from './account.service';
@Injectable({
    providedIn: 'root',
})
export class ChatbotService {
    constructor(private httpClient: HttpClient, private auth: AccountService) { }
    Chat(query: string, use_context = true, k = 5): EventSourcePolyfill | undefined {
        if (!this.auth.isAuthenticated()) return;
        const params = new URLSearchParams({ query: query, use_context: String(use_context), k: String(k) });
        const url = `${globalConfig.CHATBOT_HOST}/chatbot?${params}`;


        return new EventSourcePolyfill(url, {

            headers: {
                'Authorization': `Bearer ${this.auth.getAuthorizationToken()}`
            }
        }); // Tạo EventSource

    }

}
