<div class="comic-content">
  <app-breadcrumb
    class="breadcrumb"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Truyện tranh', url: '/tim-truyen' },
      { label: comic.title, url: '' }
    ]"
  >
  </app-breadcrumb>
  <div class="comic-bg">
    <div class="comic-bg-image" [style.background-image]="'url(' + comic.coverImage + ')'">
      <div class="comic-bg-overlay"></div>
    </div>
  </div>
  <!-- Dark overlay -->
  <article class="comic-detail">
    <div class="comic-image-section">
      <div class="comic-image-container">
        <div class="comic-bg-img">
          <img
            class="comic-cover-image"
            [src]="comic.coverImage"
            [alt]="comic.title"
            onerror="this.src='/option2.png'"
          />
        </div>
      </div>

      <div class="follow-button-container" [ngClass]="{ invisible: !isBrowser }">
        <button
          *ngIf="!isComicFollowed; else NotFollowing"
          type="button"
          [ngClass]="followButtonClass"
          (click)="Follow(true)"
        >
          <svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 512 512">
            <path
              fill="#ffffff"
              d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM232 344V280H168c-13.3 0-24-10.7-24-24s10.7-24 24-24h64V168c0-13.3 10.7-24 24-24s24 10.7 24 24v64h64c13.3 0 24 10.7 24 24s-10.7 24-24 24H280v64c0 13.3-10.7 24-24 24s-24-10.7-24-24z"
            />
          </svg>
          Theo dõi
        </button>
        <ng-template #NotFollowing>
          <button
            *ngIf="isComicFollowed"
            type="button"
            [ngClass]="followButtonClass"
            (click)="Follow(false)"
          >
            <svg
              class="h-5 w-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
              <polyline points="22 4 12 14.01 9 11.01" />
            </svg>
            Đang theo dõi
          </button>
        </ng-template>
      </div>
    </div>

    <div class="comic-detail-info">
      <h1 class="comic-detail-title">
        {{ comic.title }}
      </h1>
      <time [dateTime]="comic.updateAt | date : 'yyyy-MM-dd'" class="comic-update">
        Cập nhật lúc: {{ comic.updateAt | date : 'yyyy-MM-dd' }}
      </time>
      <div class="comic-other-title">
        {{ comic.otherName?.replaceAll(';', ' - ') }}
      </div>
      <p *ngIf="comic.author" class="comic-author">
        {{ comic.author }}
      </p>

      <div class="star-rating-container">
        <div
          *ngFor="let star of stars; let i = index; trackBy: trackByStarIndex"
          class="star-rating-item"
          (click)="rateStar(star)"
        >
          <svg
            class="star-icon star-empty"
            xmlns="http://www.w3.org/2000/svg"
            height="14"
            width="15.75"
            viewBox="0 0 576 512"
          >
            <path
              d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"
            />
          </svg>
          <div class="star-fill-overlay" [ngStyle]="{ 'width.%': getStarWidth(star) }">
            <svg
              class="star-icon star-filled"
              xmlns="http://www.w3.org/2000/svg"
              height="14"
              width="15.75"
              viewBox="0 0 576 512"
            >
              <path
                d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"
              />
            </svg>
          </div>
        </div>
      </div>
      <div class="comic-info-list">
        <ul class="list-main-info">
          <li class="info-item">
            <p class="info-label">Chapters:</p>
            <div class="info-value">
              <svg class="info-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
              <p class="info-number">
                {{ comic.numChapter }}
              </p>
            </div>
          </li>
          <li class="info-item">
            <p class="info-label">Lượt xem:</p>
            <div class="info-value">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="info-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
              >
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              <p class="info-number">
                {{ comic.viewCount | numeral }}
              </p>
            </div>
          </li>
          <li>
            <p class="rating text text-xs dark:text-gray-400">Xếp hạng:</p>
            <div class="flex space-x-1 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="none"
                viewBox="0 0 24 24"
                class="icon small text-icon-contrast text-undefined"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"
                ></path>
              </svg>
              <p class="info-number">
                {{ comic.rating }}
              </p>
            </div>
          </li>

          <li>
            <p class="status col-xs-4 text-xs dark:text-gray-400">Tình trạng:</p>
            <div class="flex space-x-2 items-center font-semibold">
              @if (comic.status === 0) {
              <span class="relative flex h-2 w-2 justify-center items-center">
                <span
                  class="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-65"
                ></span>
                <span class="relative inline-flex rounded-full h-2 w-2 bg-cyan-400"></span>
              </span>
              <div class="status-text text-sm">Đang tiến hành</div>
              } @else {
              <span class="relative flex h-2 w-2 justify-center items-center">
                <span
                  class="animate-ping absolute inline-flex h-full w-full rounded-full bg-lime-500 opacity-65"
                ></span>
                <!-- <span class="relative inline-flex rounded-full h-2 w-2"> -->
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="12"
                  width="12"
                  viewBox="0 0 512 512"
                >
                  <path
                    fill="#2debb2"
                    d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"
                  />
                </svg>
                <!-- </span> -->
              </span>

              <div class="status-text text-sm">Đã hoàn thành</div>
              }
            </div>
          </li>
        </ul>
        <div class="list-genre">
          <a
            *ngFor="let category of comic.genres; trackBy: trackByGenreId"
            class="genre-item"
            [routerLink]="['/tim-truyen']"
            [queryParams]="{ genre: category.id }"
            >{{ category.title }}</a
          >
        </div>
      </div>

      <div class="comic-description-section">
        <p
          [innerHTML]="comic.description | fillDescription : comic.id : comic!.title : comic!.url"
          [ngClass]="descriptionClass"
          class="comic-description"
        ></p>
        <button class="btn-viewmore" (click)="toggleDescription()">
          <span>{{isOpen ? 'Thu gọn' : 'Xem thêm'}}</span>
        </button>
      </div>

      <div class="read-action-container">
        <ng-container *ngIf="canShowReadFromBeginning; else reading">
          <a
            class="btn-recent"
            [routerLink]="['/truyen-tranh', comic.url, allchapters[allchapters.length - 1].id]"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="read-action-icon" viewBox="0 0 448 512">
              <path
                fill="#ffffff"
                d="M96 0C43 0 0 43 0 96V416c0 53 43 96 96 96H384h32c17.7 0 32-14.3 32-32s-14.3-32-32-32V384c17.7 0 32-14.3 32-32V32c0-17.7-14.3-32-32-32H384 96zm0 384H352v64H96c-17.7 0-32-14.3-32-32s14.3-32 32-32zm32-240c0-8.8 7.2-16 16-16H336c8.8 0 16 7.2 16 16s-7.2 16-16 16H144c-8.8 0-16-7.2-16-16zm16 48H336c8.8 0 16 7.2 16 16s-7.2 16-16 16H144c-8.8 0-16-7.2-16-16s7.2-16 16-16z"
              />
            </svg>
            <p class="read-action-text">Đọc từ đầu</p>
          </a>
        </ng-container>
        <a
          *ngIf="lastChapter"
          [routerLink]="['/truyen-tranh', comic.url, lastChapter!.id]"
          class="btn-new"
        >
          <p class="read-action-text">Đọc mới nhất</p>
        </a>
        <ng-template #reading>
          <a
            *ngIf="comicHistory"
            class="btn-recent"
            [routerLink]="['/truyen-tranh', comicHistory.url, latestHistoryChapter?.id]"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="16"
              width="16"
              viewBox="0 0 448 512"
              class="w-4 h-4"
            >
              <path
                fill="#ffffff"
                d="M96 0C43 0 0 43 0 96V416c0 53 43 96 96 96H384h32c17.7 0 32-14.3 32-32s-14.3-32-32-32V384c17.7 0 32-14.3 32-32V32c0-17.7-14.3-32-32-32H384 96zm0 384H352v64H96c-17.7 0-32-14.3-32-32s14.3-32 32-32zm32-240c0-8.8 7.2-16 16-16H336c8.8 0 16 7.2 16 16s-7.2 16-16 16H144c-8.8 0-16-7.2-16-16zm16 48H336c8.8 0 16 7.2 16 16s-7.2 16-16 16H144c-8.8 0-16-7.2-16-16s7.2-16 16-16z"
              />
            </svg>
            <p class="font-semibold text-sm">Đọc tiếp chương {{ getHistoryChapter() }}</p>
          </a>
        </ng-template>
      </div>
      <!-- <div class="h-2/3"></div> -->
    </div>
  </article>
  <!-- </div> -->
</div>

<div class="grid grid-cols-4 gap-4 md:container mx-auto">
  <div class="col-span-4 xl:col-span-3 m-2 dark:text-white">
    <app-chapter-list
      class="max-h-96 flex flex-col"
      [allchapters]="allchapters"
      [comic]="comic"
      [isChapterLoading]="isChapterLoading"
    ></app-chapter-list>

    <app-grid-comic
      [listComics]="similarComics | slice : 0 : 12"
      [title]="'Truyện tương tự'"
      [_class]="
        'grid gap-[12px] grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 mx-3 lg:mx-auto'
      "
    >
      <svg class="size-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5"></circle>
          <path
            d="M10.5766 8.20419C11.2099 7.06806 11.5266 6.5 12 6.5C12.4734 6.5 12.7901 7.06806 13.4234 8.20419L13.5873 8.49812C13.7672 8.82097 13.8572 8.98239 13.9975 9.0889C14.1378 9.19541 14.3126 9.23495 14.6621 9.31402L14.9802 9.38601C16.2101 9.66428 16.825 9.80341 16.9713 10.2739C17.1176 10.7443 16.6984 11.2345 15.86 12.215L15.643 12.4686C15.4048 12.7472 15.2857 12.8865 15.2321 13.0589C15.1785 13.2312 15.1965 13.4171 15.2325 13.7888L15.2653 14.1272C15.3921 15.4353 15.4554 16.0894 15.0724 16.3801C14.6894 16.6709 14.1137 16.4058 12.9622 15.8756L12.6643 15.7384C12.337 15.5878 12.1734 15.5124 12 15.5124C11.8266 15.5124 11.663 15.5878 11.3357 15.7384L11.0378 15.8756C9.88634 16.4058 9.31059 16.6709 8.92757 16.3801C8.54456 16.0894 8.60794 15.4353 8.7347 14.1272L8.76749 13.7888C8.80351 13.4171 8.82152 13.2312 8.76793 13.0589C8.71434 12.8865 8.59521 12.7472 8.35696 12.4686L8.14005 12.215C7.30162 11.2345 6.88241 10.7443 7.02871 10.2739C7.17501 9.80341 7.78993 9.66428 9.01977 9.38601L9.33794 9.31402C9.68743 9.23495 9.86217 9.19541 10.0025 9.0889C10.1428 8.98239 10.2328 8.82097 10.4127 8.49812L10.5766 8.20419Z"
            stroke="currentColor"
            stroke-width="1.5"
          ></path>
        </g>
      </svg>
    </app-grid-comic>

    @if (comic && isBrowser) {
    <app-comment [comicId]="comic.id" [chapterID]="comic.chapters![0].id"></app-comment>
    }
  </div>

  <div class="col-span-4 xl:col-span-1 my-4">
    <app-top-list></app-top-list>
  </div>
</div>

<!-- </ng-container> -->
