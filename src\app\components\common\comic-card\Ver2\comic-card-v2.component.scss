// Comic Card V2 Container
.comic-card-v2 {
  @apply relative w-full flex bg-gray-100 dark:bg-neutral-800 rounded-xl h-36 overflow-hidden dark:text-gray-100;
}

// Image Container
.comic-image-container {
  @apply h-full w-[134px] p-2 overflow-hidden flex;
}

.comic-image-v2 {
  @apply shadow-md object-cover h-full rounded-md hover:brightness-75;
}

// Content Section
.comic-content {
  @apply flex flex-col pl-2 pr-4 py-1 w-full;
}

// Header Section
.comic-header {
  @apply w-full flex justify-between;
}

.comic-title-link {
  @apply font-bold uppercase my-0.5 text-sm dark:text-gray-100;
}

.comic-title-v2 {
  @apply line-clamp-1;
}

.comic-update-time {
  @apply text-xs my-1 text-gray-400 flex-shrink-0;
}

// Genre Tags
.genre-tags {
  @apply flex space-x-1;
}

.genre-tag {
  @apply text-pretty;
}

.genre-tag-primary {
  .genre-tag-text {
    @apply text-nowrap bg-primary-100 cursor-pointer text-xs font-bold rounded shadow-sm px-2 uppercase text-white;
  }
}

.genre-tag-secondary {
  .genre-tag-text {
    @apply text-nowrap bg-white text-[0.7rem] dark:bg-neutral-700 dark:text-white cursor-pointer font-semibold rounded shadow-sm px-2 uppercase text-black;
  }
}

// Stats Section
.comic-stats {
  @apply flex flex-col-reverse lg:flex-row gap-1 lg:gap-3;
}

.stats-row {
  @apply text-sm text-center flex gap-2 items-center;
}

.rating-stat {
  @apply flex gap-1 items-center text-yellow-500;
}


.bookmark-stat {
  @apply flex gap-1 items-center;
}


.view-stat {
  @apply uppercase flex gap-1 items-center;
}


// Status Section
.status-container {
  @apply text-sm text-center flex gap-2 items-center;
}

.status-ongoing {
  @apply flex gap-2 items-center;
}

.status-indicator.ongoing {
  @apply animate-ping h-1 w-1 rounded-full bg-sky-400 opacity-75;
}

.status-completed {
  @apply flex gap-2 items-center;
}

.status-icon.completed {
  @apply animate-ping opacity-75;
}

.status-text {
  @apply text-sm;
}

// Description Section
.comic-description {
  @apply mt-1;
}

.description-text {
  @apply line-clamp-3 text-sm;
}

// Placeholder Styles
.placeholder-container {
  @apply relative w-full flex rounded-xl h-44 overflow-hidden shadow-sm;
}

.placeholder-image-section {
  @apply w-1/3 h-full animate-pulse bg-gray-300 dark:bg-neutral-800 relative rounded-md flex justify-center items-center;
}

.placeholder-icon {
  @apply w-10 h-10 text-gray-200 dark:text-gray-600;
}

.placeholder-content-section {
  @apply relative w-full flex bg-slate-100 dark:bg-neutral-700 rounded-xl h-44 overflow-hidden animate-pulse;
}

.placeholder-image-area {
  @apply flex-shrink-0 h-full p-2 overflow-hidden;
}

.placeholder-image-block {
  @apply bg-gray-200 dark:bg-neutral-800 w-full h-full rounded-md;
}

.placeholder-text-area {
  @apply flex flex-col pl-2 pr-4 py-1 w-full;
}

.placeholder-title {
  @apply bg-gray-200 dark:bg-neutral-800 h-6 my-1 rounded-md;
}

.placeholder-tags {
  @apply flex space-x-1;
}

.placeholder-tag {
  @apply bg-gray-200 dark:bg-neutral-800 h-4 w-16 rounded-md;
}

.placeholder-description {
  @apply bg-gray-200 dark:bg-neutral-800 h-20 w-full mt-2 rounded-md;
}