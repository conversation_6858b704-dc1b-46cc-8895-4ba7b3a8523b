<div class="pagination-container">
  <div class="pagination-nav">
    <button
      type="button"
      (click)="OnChangePage((currentPage - 1).toString())"
      class="pagination-prev"
    >
      <span class="sr-only">Previous</span>
      <svg
        class="pagination-icon"
        viewBox="0 0 20 20"
        fill="currentColor"
        aria-hidden="true"
      >
        <path
          fill-rule="evenodd"
          d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
          clip-rule="evenodd"
        />
      </svg>
    </button>

    <button
      type="button"
      *ngFor="let page of pages"
      (click)="OnChangePage(page)"
      aria-current="page"
      class="pagination-page"
      [ngClass]="{
        active: currentPage.toString() === page,
     
      }"
    >
      {{ page }}
    </button>

    <button
      type="button"
      (click)="OnChangePage((currentPage + 1).toString())"
      class="pagination-next"
    >
      <span class="sr-only">Next</span>
      <svg
        class="pagination-icon"
        viewBox="0 0 20 20"
        fill="currentColor"
        aria-hidden="true"
      >
        <path
          fill-rule="evenodd"
          d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
          clip-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div *ngIf="showSearch" class="pagination-search">
    <span class="pagination-search-label"> Đến trang</span>
    <input
      type="number"
      name="quantity"
      min="1"
      max="100"
      #searchInput
      class="pagination-search-input"
      placeholder="{{ currentPage }}"
      required
    />

    <button
      (click)="OnChangePage(searchInput.value)"
      class="pagination-search-button"
    >
      <svg
        class="pagination-search-icon"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" />
        <line x1="5" y1="12" x2="19" y2="12" />
        <line x1="13" y1="18" x2="19" y2="12" />
        <line x1="13" y1="6" x2="19" y2="12" />
      </svg>
    </button>
  </div>
</div>
