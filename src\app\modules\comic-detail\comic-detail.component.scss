// Comic Content Container
.comic-content {
    @apply h-full w-full flex flex-col relative border-spacing-3 overflow-hidden text-secondary-100 dark:text-white;
}

// Background Elements
.comic-bg {
    @apply absolute inset-0 h-full border-spacing-2 z-[1] blur-sm;
}

.comic-bg-image {
    @apply w-full h-full bg-cover bg-black bg-no-repeat bg-top;
}

.comic-bg-overlay {
    @apply absolute inset-0 bg-black opacity-20;
}

.comic-bg-loading {
    @apply w-full h-full animate-pulse bg-gray-400;
}

// Breadcrumb
.breadcrumb {
    @apply md:container mx-auto z-10 my-2 text-gray-100 hover:text-white;
}

// Comic Detail Section
.comic-detail {
    @apply lg:flex xl:flex rounded-t-3xl z-[2] mx-auto md:container md:w-3/4 w-full lg:w-full mt-8 lg:mt-5 backdrop-blur-sm bg-gradient-to-t from-white to-white/80 dark:from-dark-bg dark:to-dark-bg/50;
}

// Image Section
.comic-image-section {
    @apply lg:ml-10;
}

.comic-image-container {
    @apply flex justify-center relative h-[280px] lg:h-auto;
}

.comic-bg-img {
    @apply w-[200px] h-[300px] flex shadow-md rounded absolute lg:relative bottom-8 translate-y-4 lg:translate-y-0 lg:bottom-5 border-gray-200 border-2 overflow-hidden;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 50%;
        transform: skewX(-25deg);
        height: 100%;
        background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
        animation: none;
    }

    &:hover::before {
        animation: slideOnHover 0.5s ease-in-out forwards;
    }

    @keyframes slideOnHover {
        0% {
            left: -100%;
        }

        100% {
            left: 100%;
        }
    }
}

.comic-cover-image {
    @apply flex justify-center object-cover w-full;
}

// Follow Button Section
.follow-button-container {
    @apply mb-3 flex justify-center;
}

.btn-follow {
    @apply py-1 px-4 gap-1 bg-cyan-600 text-white rounded hover:bg-cyan-500 flex items-center transition-colors duration-150;

    &:focus {
        @apply ring-2 ring-cyan-300 ring-opacity-50;
    }
}

.btn-unfollow {
    @apply py-1 px-4 border-red-500 border text-red-500 rounded hover:opacity-75 flex space-x-1 gap-1 items-center transition-all duration-150;

    &:focus {
        @apply ring-2 ring-red-300 ring-opacity-50;
    }
}

// Comic Info Section
.comic-detail-info {
    @apply flex flex-col justify-center items-center lg:items-start mx-4 my-3 mb-5;
}

.comic-detail-title {
    @apply font-bold uppercase text-xl lg:text-2xl text-pretty;
}

.comic-update {
    @apply w-0 h-0 invisible;
}

.comic-other-title {
    @apply text-gray-700 dark:text-gray-400 capitalize hyphens-auto line-clamp-1;
}

.comic-author {
    @apply uppercase font-light dark:text-gray-300;
}

// Star Rating
.star-rating-container {
    @apply flex items-center col-span-3;
}

.star-rating-item {
    @apply relative cursor-pointer;
}

.star-icon {
    @apply fill-current transition-colors duration-150;

    &.star-empty {
        @apply text-gray-300;
    }

    &.star-filled {
        @apply text-yellow-500;
    }
}

.star-fill-overlay {
    @apply absolute top-0 left-0 h-full overflow-hidden;
}

// Comic Info List
.comic-info-list {
    @apply space-y-2 text-sm mt-2;
}

.list-main-info {
    @apply flex flex-wrap justify-center lg:justify-start gap-5 text-sm;
}

.info-item {
    @apply flex flex-col items-center lg:items-start;
}

.info-label {
    @apply text-xs dark:text-gray-400;
}

.info-value {
    @apply flex space-x-1 items-center;
}

.info-icon {
    @apply h-5 w-5;
}

.info-number {
    @apply font-semibold;
}

// Status Indicators
.status-container {
    @apply flex space-x-2 items-center font-semibold;
}

.status-indicator {
    @apply relative flex h-2 w-2 justify-center items-center;
}

.status-ping {
    @apply animate-ping absolute inline-flex h-full w-full rounded-full opacity-65;

    &.status-ping-completed {
        @apply bg-lime-500;
    }
}

.status-dot {
    @apply relative inline-flex rounded-full h-2 w-2;

    &.status-dot-ongoing {
        @apply bg-cyan-400;
    }
}

.status-check-icon {
    @apply h-3 w-3;
}

.status-text {
    @apply text-sm;
}

.status-ongoing {
    .status-ping {
        @apply bg-sky-400;
    }
}

.status-completed {
    .status-ping {
        @apply bg-lime-500;
    }
}

// Genre List
.list-genre {
    @apply flex justify-center xl:justify-normal flex-wrap space-x-2;
}

.genre-item {
    @apply transition-colors duration-100 uppercase font-semibold hover:text-white mt-1 px-2 text-gray-600 dark:text-gray-100 dark:border-gray-500 hover:bg-primary-100 rounded border-gray-300 border border-dashed;

    &:focus {
        @apply ring-2 ring-primary-100 ring-opacity-50;
    }
}

// Description Section
.comic-description-section {
    @apply lg:text-left lg:w-full mx-3 lg:m-0;
}

.comic-description {
    @apply mt-2 text-sm mr-4 transition-all duration-300;

    &.description-collapsed {
        @apply line-clamp-3;
    }

    &.description-expanded {
        @apply line-clamp-none;
    }
}

.btn-viewmore {
    @apply text-sm font-semibold dark:text-gray-500 hover:text-gray-700 transition-colors duration-150;

    &:focus {
        @apply ring-2 ring-gray-300 ring-opacity-50;
    }
}

// Read Action Section
.read-action-container {
    @apply flex space-x-2 mt-3;
}

.btn-recent {
    @apply bg-primary-100 rounded px-4 hover:opacity-90 text-white flex items-center space-x-2 transition-all duration-150;

    &:focus {
        @apply ring-2 ring-primary-200 ring-opacity-50;
    }
}

.btn-new {
    @apply px-4 p-2 border dark:text-white dark:bg-neutral-800 border-gray-600 cursor-pointer rounded text-black font-semibold text-sm hover:text-white hover:bg-secondary-100 transition-all duration-150;

    &:focus {
        @apply ring-2 ring-opacity-50;
    }
}

.read-action-icon {
    @apply w-4 h-4;
}

.read-action-text {
    @apply font-semibold text-sm;
}


.star-rating-item {
    transition: transform 0.2s ease-out;

    &:hover {
        transform: scale(1.2);
    }
}
