using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ComicAPI.Classes;
using Microsoft.Extensions.Options;

namespace ComicAPI.Services
{
    public class UrlService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly AppSetting _config;
        public string ImgHost => _imgHost;
        private readonly string _imgHost = "https://cdn.anhtruyen.com";
        private readonly string _staticHost = "https://cdn1.anhtruyen.com";
        private readonly string _baseUrl = "https://metruyenmoi.com";
        public string Host => GetHost();


        public UrlService(IWebHostEnvironment environment, IOptions<AppSetting> options, IHttpContextAccessor httpContextAccessor)
        {
            _environment = environment;
            _config = options.Value;
            // _host = _config.Host ?? _host;
            _imgHost = _config.ImgHost ?? _imgHost;
            _httpContextAccessor = httpContextAccessor;

            // httpContext.HttpContext.Request.Headers.TryGetValue("X-Real-Host", out var host);
        }
        private string GetHost()
        {
            var context = _httpContextAccessor.HttpContext;

            if (context == null) return _baseUrl;

            if (context.Request.Headers.TryGetValue("X-Real-Host", out var realHost))
            {
                if (realHost.Contains("localhost") || realHost.Contains("127.0.0.1") || realHost.Contains("0.0.0.0"))
                {
                    return _baseUrl;
                }
                return $"https://{realHost.ToString()}";
            }
            if (context.Request.Host.HasValue)
            {
                if (context.Request.Host.Host.Contains("localhost") || context.Request.Host.Host.Contains("127.0.0.1") || context.Request.Host.Host.Contains("0.0.0.0"))
                {
                    return _baseUrl;
                }
                return $"https://{context.Request.Host.Value}";
            }
            return _baseUrl;
        }
        public string GetComicCoverImagePath(string? Image)
        {
            return $"{_staticHost}/coverimg/{Image}";
        }
        public string GetUserImagePath(string? Image)
        {
            return ServiceUtilily.AddTimestampToUrl($"{Host}/AvatarImg/{Image}");
        }
        public string GetPathSaveUserImage()
        {
            return Path.Combine(_environment.ContentRootPath, "wwwroot/AvatarImg"); ;
        }
        public string GetConfirmEmailPath(int UserId, string Code)
        {
            return $"{Host}/auth/confirm-email?UserId={UserId}&Code={Code}";
        }
    }
}