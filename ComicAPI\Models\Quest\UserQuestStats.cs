using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Quest
{
    public class UserQuestStats
    {
        [Key, Column("userid")]
        public int UserID { get; set; }

        [Column("totalcompleted")]
        public int TotalCompleted { get; set; } = 0;

        [Column("streakdays")]
        public int StreakDays { get; set; } = 0;

        [Column("weeklycompleted")]
        public int WeeklyCompleted { get; set; } = 0;

        [Column("monthlycompleted")]
        public int MonthlyCompleted { get; set; } = 0;

        [Column("totalrewardsearned")]
        public int TotalRewardsEarned { get; set; } = 0;

        [Column("level")]
        public int Level { get; set; } = 1;

        [Column("experience")]
        public int Experience { get; set; } = 0;

        [Column("nextlevelexp")]
        public int NextLevelExp { get; set; } = 100;

        [Column("lastlogindate")]
        public DateTime? LastLoginDate { get; set; }

        [Column("updateat")]
        public DateTime UpdateAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User User { get; set; } = null!;
    }
}
