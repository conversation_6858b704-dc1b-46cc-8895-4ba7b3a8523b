namespace ComicApp.DTOs.Item
{
    public class ItemTemplateDTO
    {
        public int ID { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Icon { get; set; }
        public string Category { get; set; } = string.Empty;
        public string Rarity { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class UserInventoryDTO
    {
        public int ID { get; set; }
        public int UserID { get; set; }
        public int ItemTemplateID { get; set; }
        public int Quantity { get; set; }
        public DateTime ObtainedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? Metadata { get; set; }
        public ItemTemplateDTO ItemTemplate { get; set; } = new();
    }

    public class UserEquippedItemDTO
    {
        public int UserID { get; set; }
        public int ItemTemplateID { get; set; }
        public string SlotType { get; set; } = string.Empty;
        public DateTime EquippedAt { get; set; }
        public ItemTemplateDTO ItemTemplate { get; set; } = new();
    }

    public class InventoryPageDTO
    {
        public List<UserInventoryDTO> Items { get; set; } = new();
        public int TotalItems { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
    }

    public class UseItemRequest
    {
        public int ItemTemplateID { get; set; }
        public int Quantity { get; set; } = 1;
    }

    public class EquipItemRequest
    {
        public int ItemTemplateID { get; set; }
        public string SlotType { get; set; } = string.Empty;
    }

    public class UnequipItemRequest
    {
        public string SlotType { get; set; } = string.Empty;
    }

    public class GiveItemRequest
    {
        public int UserID { get; set; }
        public int ItemTemplateID { get; set; }
        public int Quantity { get; set; } = 1;
        public string? Source { get; set; }
        public int? SourceID { get; set; }
    }

    public class ItemOperationResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserInventoryDTO? UpdatedItem { get; set; }
    }
}
