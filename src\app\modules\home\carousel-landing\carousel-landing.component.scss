
// Desktop Carousel Container
.desktop-carousel-container {
  @apply w-full h-full absolute pl-0 pr-0 overflow-hidden lg:flex hidden;
}

// Navigation Controls
.nav-control-left {
  @apply bg-dark-bg/40 w-1/4 absolute z-10 h-full cursor-pointer transition-all duration-200;

  &:hover {
    @apply bg-dark-bg/60;

    .nav-icon-left {
      @apply opacity-80;
    }
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100 ring-opacity-50;
  }
}

.nav-control-right {
  @apply bg-dark-bg/40 w-1/4 absolute z-10 h-full cursor-pointer right-0 transition-all duration-200;

  &:hover {
    @apply bg-dark-bg/60;

    .nav-icon-right {
      @apply opacity-80;
    }
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100 ring-opacity-50;
  }
}

.nav-icon-left {
  @apply absolute text-white opacity-10 h-20 w-20 top-1/2 -translate-y-1/2 left-[50%] transition-opacity duration-200;
}

.nav-icon-right {
  @apply absolute text-white opacity-10 h-20 w-20 top-1/2 -translate-y-1/2 right-[50%] transition-opacity duration-200;
}

// Comic Details Overlay
.comic-details-overlay {
  @apply transition-all text-white absolute left-1/4 w-2/4 h-full z-10 bg-black/80 overflow-hidden pointer-events-none;
}

.comic-details-content {
  @apply flex flex-col pl-3 pr-4 py-2 mb-2;
}

.comic-header {
  @apply w-full flex justify-between gap-2;
}

.comic-title {
  @apply font-bold uppercase my-1 text-base;
}

.comic-status-container {
  @apply text-sm text-center flex gap-2 items-center;
}

.status-indicator-ongoing {
  @apply animate-ping h-1 w-1 rounded-full bg-sky-400 opacity-75;
}

.status-indicator-completed {
  @apply animate-ping opacity-75;
}

// Genre Tags
.genre-tags-container {
  @apply flex space-x-1 w-full flex-wrap;
}


.primary-genre-tag {
  @apply bg-primary-100 cursor-pointer text-xs font-bold rounded shadow-sm px-2 uppercase text-white;
}

.secondary-genre-tag {
  @apply bg-neutral-600 text-white text-[0.7rem] cursor-pointer font-semibold rounded shadow-sm px-2 uppercase;
}

// Comic Stats
.comic-stats-container {
  @apply flex gap-3;
}

.rating-stat {
  @apply text-sm text-center flex gap-1 items-center text-yellow-500;
}

.bookmark-stat {
  @apply text-sm text-center flex gap-1 items-center;
}

.view-count-stat {
  @apply text-sm text-center uppercase flex gap-1 items-center;
}

.comic-description {
  @apply mt-2;
}

.description-text {
  @apply text-sm;
}

// Carousel Items
.carousel-item-desktop {
  @apply transition-[left] duration-700 block absolute h-full top-0 ease-out w-1/3;
}

.loading-container {
  @apply w-full h-full flex justify-center items-center;
}

// Mobile Carousel
.mobile-carousel-container {
  @apply absolute w-full h-full flex overflow-hidden bg-dark-bg lg:hidden;
}

.mobile-nav-left {
  @apply top-1/2 -translate-y-1/2 left-2 rounded-xl overflow-hidden bg-dark-bg/40 w-12 h-12 absolute z-10 cursor-pointer transition-all duration-200;

  &:hover {
    @apply bg-dark-bg/60 scale-110;

    .mobile-nav-icon-left {
      @apply opacity-80;
    }
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100 ring-opacity-50;
  }

  &:active {
    @apply scale-95;
  }
}

.mobile-nav-right {
  @apply top-1/2 -translate-y-1/2 right-2 rounded-xl overflow-hidden bg-dark-bg/40 w-12 h-12 absolute z-10 cursor-pointer transition-all duration-200;

  &:hover {
    @apply bg-dark-bg/60 scale-110;

    .mobile-nav-icon-right {
      @apply opacity-80;
    }
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100 ring-opacity-50;
  }

  &:active {
    @apply scale-95;
  }
}

.mobile-nav-icon-left {
  @apply absolute text-white opacity-40 h-10 w-10 top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 transition-opacity duration-200;
}

.mobile-nav-icon-right {
  @apply absolute text-white opacity-40 h-10 w-10 top-1/2 -translate-y-1/2 right-1/2 translate-x-1/2 transition-opacity duration-200;
}

.carousel-item-mobile {
  @apply transition-[left] duration-700 h-full block absolute top-0 ease-out py-1 px-0.5;
}

.mobile-comic-image {
  @apply h-full w-full object-cover;
}

// Swiper Component
.swiper-container {
  @apply absolute z-10 -bottom-4 md:-bottom-5 right-1/2 translate-x-1/2;
}

// Performance Optimizations
.carousel-item-hidden {
  @apply invisible;
}

// Stat Icons
.stat-icon {
  @apply w-4 h-4;
}

