using ComicApp.DTOs.Quest;
using ComicApp.Reposibility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ComicApp.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class QuestController : ControllerBase
    {
        IUserService _userService;

        private readonly IQuestRepository _questRepository;
        private readonly ILogger<QuestController> _logger;

        public QuestController(IQuestRepository questRepository, ILogger<QuestController> logger, IUserService userService)
        {
            _userService = userService;
            _questRepository = questRepository;
            _logger = logger;
        }

        /// <summary>
        /// Get daily quests for the current user
        /// </summary>
        [HttpGet("daily")]
        public async Task<ActionResult<List<DailyQuestDTO>>> GetDailyQuests()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var quests = await _questRepository.GetDailyQuestsAsync(userId.Value);
                return Ok(quests);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get weekly quests for the current user
        /// </summary>
        [HttpGet("weekly")]
        public async Task<ActionResult<List<WeeklyQuestDTO>>> GetWeeklyQuests()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var quests = await _questRepository.GetWeeklyQuestsAsync(userId.Value);
                return Ok(quests);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting weekly quests");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get user quest statistics
        /// </summary>
        [HttpGet("user-stats")]
        public async Task<ActionResult<UserQuestStatsDTO>> GetUserStats()
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var stats = await _questRepository.GetUserQuestStatsAsync(userId.Value);
                if (stats == null)
                    return NotFound("User stats not found");

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user stats");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Claim reward for a completed quest
        /// </summary>
        [HttpPost("{questId}/claim")]
        public async Task<ActionResult<ClaimRewardResponse>> ClaimReward(string questId)
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.ClaimQuestRewardAsync(userId.Value, questId);
                
                if (!result.Success)
                    return BadRequest(result);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error claiming reward for quest {QuestId}", questId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update quest progress manually (for testing purposes)
        /// </summary>
        [HttpPut("{questId}/progress")]
        public async Task<ActionResult<bool>> UpdateQuestProgress(string questId, [FromBody] UpdateQuestProgressRequest request)
        {
            try
            {
                var userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.UpdateQuestProgressAsync(userId.Value, questId, request.Progress);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating quest progress for quest {QuestId}", questId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Initialize quest stats for a user (typically called on first login)
        /// </summary>
        [HttpPost("initialize")]
        public async Task<ActionResult<bool>> InitializeUserQuests()
        {
            try
            {
                int ?userId = _userService.CurrentUser?.ID;
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _questRepository.InitializeUserQuestStatsAsync(userId.Value);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing user quests");
                return StatusCode(500, "Internal server error");
            }
        }

    }
}
