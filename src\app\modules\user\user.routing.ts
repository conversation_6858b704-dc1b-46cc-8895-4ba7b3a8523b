import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/user-profile-container/user-profile-container.component').then(m => m.UserProfileContainerComponent),
    children: [
      {
        path: '',
        redirectTo: 'profile',
        pathMatch: 'full'
      },
      {
        path: 'profile',
        loadComponent: () => import('./components/user-header/user-header.component').then(m => m.UserHeaderComponent)
      },
      {
        path: 'favorites',
        loadComponent: () => import('./components/favorite-comics/favorite-comics.component').then(m => m.FavoriteComicsComponent)
      },
      {
        path: 'history',
        loadComponent: () => import('./components/reading-history/reading-history.component').then(m => m.ReadingHistoryComponent)
      },
      {
        path: 'stats',
        loadComponent: () => import('./components/user-statistics/user-statistics.component').then(m => m.UserStatisticsComponent)
      },
      {
        path: 'settings',
        loadComponent: () => import('./components/user-preferences/user-preferences.component').then(m => m.UserPreferencesComponent)
      },
      {
        path: 'reviews',
        loadComponent: () => import('./components/user-reviews/user-reviews.component').then(m => m.UserReviewsComponent)
      },
      {
        path: 'lists',
        loadComponent: () => import('./components/reading-lists/reading-lists.component').then(m => m.ReadingListsComponent)
      },
      {
        path: 'achievements',
        loadComponent: () => import('./components/user-achievements/user-achievements.component').then(m => m.UserAchievementsComponent)
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserRoutingModule {}
