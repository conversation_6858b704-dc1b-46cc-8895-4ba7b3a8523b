using ComicApp.Data;
using ComicApp.DTOs.Item;
using ComicApp.Models.Item;
using Microsoft.EntityFrameworkCore;

namespace ComicApp.Reposibility
{
    public class ItemRepository : IItemRepository
    {
        private readonly ComicDbContext _dbContext;
        private readonly ILogger<ItemRepository> _logger;

        public ItemRepository(ComicDbContext dbContext, ILogger<ItemRepository> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<ItemTemplateDTO>> GetItemTemplatesAsync(string? category = null)
        {
            try
            {
                var query = _dbContext.ItemTemplates.Where(t => t.IsActive);

                if (!string.IsNullOrEmpty(category))
                {
                    query = query.Where(t => t.Category == category);
                }

                return await query
                    .Select(t => new ItemTemplateDTO
                    {
                        ID = t.ID,
                        Name = t.Name,
                        Description = t.Description,
                        Icon = t.Icon,
                        Category = t.Category,
                        Rarity = t.Rarity,
                        IsActive = t.IsActive
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item templates");
                return new List<ItemTemplateDTO>();
            }
        }

        public async Task<ItemTemplateDTO?> GetItemTemplateAsync(int templateId)
        {
            try
            {
                return await _dbContext.ItemTemplates
                    .Where(t => t.ID == templateId && t.IsActive)
                    .Select(t => new ItemTemplateDTO
                    {
                        ID = t.ID,
                        Name = t.Name,
                        Description = t.Description,
                        Icon = t.Icon,
                        Category = t.Category,
                        Rarity = t.Rarity,
                        IsActive = t.IsActive
                    })
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item template {TemplateId}", templateId);
                return null;
            }
        }

        public async Task<List<ItemTemplateDTO>> GetItemTemplatesByRarityAsync(string rarity)
        {
            try
            {
                return await _dbContext.ItemTemplates
                    .Where(t => t.Rarity == rarity && t.IsActive)
                    .Select(t => new ItemTemplateDTO
                    {
                        ID = t.ID,
                        Name = t.Name,
                        Description = t.Description,
                        Icon = t.Icon,
                        Category = t.Category,
                        Rarity = t.Rarity,
                        IsActive = t.IsActive
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item templates by rarity {Rarity}", rarity);
                return new List<ItemTemplateDTO>();
            }
        }

        public async Task<InventoryPageDTO> GetUserInventoryAsync(int userId, int page = 1, int pageSize = 20, string? category = null)
        {
            try
            {
                var query = _dbContext.UserInventories
                    .Include(i => i.ItemTemplate)
                    .Where(i => i.UserID == userId && i.Quantity > 0);

                if (!string.IsNullOrEmpty(category))
                {
                    query = query.Where(i => i.ItemTemplate.Category == category);
                }

                var totalItems = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

                var items = await query
                    .OrderBy(i => i.ItemTemplate.Category)
                    .ThenBy(i => i.ItemTemplate.Name)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(i => new UserInventoryDTO
                    {
                        ID = i.ID,
                        UserID = i.UserID,
                        ItemTemplateID = i.ItemTemplateID,
                        Quantity = i.Quantity,
                        ObtainedAt = i.ObtainedAt,
                        ExpiresAt = i.ExpiresAt,
                        Metadata = i.Metadata,
                        ItemTemplate = new ItemTemplateDTO
                        {
                            ID = i.ItemTemplate.ID,
                            Name = i.ItemTemplate.Name,
                            Description = i.ItemTemplate.Description,
                            Icon = i.ItemTemplate.Icon,
                            Category = i.ItemTemplate.Category,
                            Rarity = i.ItemTemplate.Rarity,
                            IsActive = i.ItemTemplate.IsActive
                        }
                    })
                    .ToListAsync();

                return new InventoryPageDTO
                {
                    Items = items,
                    TotalItems = totalItems,
                    CurrentPage = page,
                    TotalPages = totalPages
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user inventory for user {UserId}", userId);
                return new InventoryPageDTO();
            }
        }

        public async Task<UserInventoryDTO?> GetUserInventoryItemAsync(int userId, int itemTemplateId)
        {
            try
            {
                return await _dbContext.UserInventories
                    .Include(i => i.ItemTemplate)
                    .Where(i => i.UserID == userId && i.ItemTemplateID == itemTemplateId)
                    .Select(i => new UserInventoryDTO
                    {
                        ID = i.ID,
                        UserID = i.UserID,
                        ItemTemplateID = i.ItemTemplateID,
                        Quantity = i.Quantity,
                        ObtainedAt = i.ObtainedAt,
                        ExpiresAt = i.ExpiresAt,
                        Metadata = i.Metadata,
                        ItemTemplate = new ItemTemplateDTO
                        {
                            ID = i.ItemTemplate.ID,
                            Name = i.ItemTemplate.Name,
                            Description = i.ItemTemplate.Description,
                            Icon = i.ItemTemplate.Icon,
                            Category = i.ItemTemplate.Category,
                            Rarity = i.ItemTemplate.Rarity,
                            IsActive = i.ItemTemplate.IsActive
                        }
                    })
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user inventory item for user {UserId}, item {ItemTemplateId}", userId, itemTemplateId);
                return null;
            }
        }

        public async Task<List<UserInventoryDTO>> GetUserInventoryByCategoryAsync(int userId, string category)
        {
            try
            {
                return await _dbContext.UserInventories
                    .Include(i => i.ItemTemplate)
                    .Where(i => i.UserID == userId && i.ItemTemplate.Category == category && i.Quantity > 0)
                    .Select(i => new UserInventoryDTO
                    {
                        ID = i.ID,
                        UserID = i.UserID,
                        ItemTemplateID = i.ItemTemplateID,
                        Quantity = i.Quantity,
                        ObtainedAt = i.ObtainedAt,
                        ExpiresAt = i.ExpiresAt,
                        Metadata = i.Metadata,
                        ItemTemplate = new ItemTemplateDTO
                        {
                            ID = i.ItemTemplate.ID,
                            Name = i.ItemTemplate.Name,
                            Description = i.ItemTemplate.Description,
                            Icon = i.ItemTemplate.Icon,
                            Category = i.ItemTemplate.Category,
                            Rarity = i.ItemTemplate.Rarity,
                            IsActive = i.ItemTemplate.IsActive
                        }
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user inventory by category for user {UserId}, category {Category}", userId, category);
                return new List<UserInventoryDTO>();
            }
        }

        public async Task<ItemOperationResponse> GiveItemToUserAsync(int userId, int itemTemplateId, int quantity = 1, string? source = null, int? sourceId = null)
        {
            try
            {
                var itemTemplate = await _dbContext.ItemTemplates
                    .FirstOrDefaultAsync(t => t.ID == itemTemplateId && t.IsActive);

                if (itemTemplate == null)
                    return new ItemOperationResponse { Success = false, Message = "Item template not found" };

                // Check if user already has this item
                var existingItem = await _dbContext.UserInventories
                    .FirstOrDefaultAsync(i => i.UserID == userId && i.ItemTemplateID == itemTemplateId);

                if (existingItem != null)
                {
                    // Add to existing quantity (unlimited stacking)
                    existingItem.Quantity += quantity;
                    _dbContext.UserInventories.Update(existingItem);
                }
                else
                {
                    // Create new inventory item
                    var newItem = new UserInventory
                    {
                        UserID = userId,
                        ItemTemplateID = itemTemplateId,
                        Quantity = quantity,
                        ObtainedAt = DateTime.UtcNow
                    };
                    _dbContext.UserInventories.Add(newItem);
                }

                await _dbContext.SaveChangesAsync();

                var updatedItem = await GetUserInventoryItemAsync(userId, itemTemplateId);
                return new ItemOperationResponse
                {
                    Success = true,
                    Message = $"Successfully gave {quantity} {itemTemplate.Name} to user",
                    UpdatedItem = updatedItem
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error giving item to user {UserId}, item {ItemTemplateId}", userId, itemTemplateId);
                return new ItemOperationResponse { Success = false, Message = "Error giving item to user" };
            }
        }

        public async Task<ItemOperationResponse> UseItemAsync(int userId, int itemTemplateId, int quantity = 1)
        {
            try
            {
                var item = await _dbContext.UserInventories
                    .Include(i => i.ItemTemplate)
                    .FirstOrDefaultAsync(i => i.UserID == userId && i.ItemTemplateID == itemTemplateId);

                if (item == null)
                    return new ItemOperationResponse { Success = false, Message = "Item not found in inventory" };

                if (item.Quantity < quantity)
                    return new ItemOperationResponse { Success = false, Message = $"Not enough items. You have {item.Quantity}, need {quantity}" };

                // Reduce quantity
                item.Quantity -= quantity;

                if (item.Quantity <= 0)
                {
                    // Remove item if quantity reaches 0
                    _dbContext.UserInventories.Remove(item);
                }
                else
                {
                    _dbContext.UserInventories.Update(item);
                }

                await _dbContext.SaveChangesAsync();

                return new ItemOperationResponse
                {
                    Success = true,
                    Message = $"Successfully used {quantity} {item.ItemTemplate.Name}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error using item for user {UserId}, item {ItemTemplateId}", userId, itemTemplateId);
                return new ItemOperationResponse { Success = false, Message = "Error using item" };
            }
        }

        // Implement other methods...
        public Task<ItemOperationResponse> EquipItemAsync(int userId, int itemTemplateId, string slotType)
        {
            return Task.FromResult(new ItemOperationResponse { Success = false, Message = "Not implemented" });
        }

        public Task<ItemOperationResponse> UnequipItemAsync(int userId, string slotType)
        {
            return Task.FromResult(new ItemOperationResponse { Success = false, Message = "Not implemented" });
        }

        public Task<List<UserEquippedItemDTO>> GetUserEquippedItemsAsync(int userId)
        {
            return Task.FromResult(new List<UserEquippedItemDTO>());
        }

        public Task<UserEquippedItemDTO?> GetUserEquippedItemBySlotAsync(int userId, string slotType)
        {
            return Task.FromResult<UserEquippedItemDTO?>(null);
        }

        public async Task<bool> HasItemAsync(int userId, int itemTemplateId, int quantity = 1)
        {
            try
            {
                var item = await _dbContext.UserInventories
                    .FirstOrDefaultAsync(i => i.UserID == userId && i.ItemTemplateID == itemTemplateId);
                
                return item != null && item.Quantity >= quantity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user has item");
                return false;
            }
        }

        public async Task<int> GetItemQuantityAsync(int userId, int itemTemplateId)
        {
            try
            {
                var item = await _dbContext.UserInventories
                    .FirstOrDefaultAsync(i => i.UserID == userId && i.ItemTemplateID == itemTemplateId);
                
                return item?.Quantity ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item quantity");
                return 0;
            }
        }

        public async Task<bool> CleanupExpiredItemsAsync()
        {
            try
            {
                var expiredItems = await _dbContext.UserInventories
                    .Where(i => i.ExpiresAt.HasValue && i.ExpiresAt.Value < DateTime.UtcNow)
                    .ToListAsync();

                _dbContext.UserInventories.RemoveRange(expiredItems);
                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up expired items");
                return false;
            }
        }

        public async Task<Dictionary<string, int>> GetUserCurrencyAsync(int userId)
        {
            try
            {
                var currencyItems = await _dbContext.UserInventories
                    .Include(i => i.ItemTemplate)
                    .Where(i => i.UserID == userId && i.ItemTemplate.Category == "currency")
                    .Select(i => new { i.ItemTemplate.Name, i.Quantity })
                    .ToListAsync();

                return currencyItems.ToDictionary(c => c.Name, c => c.Quantity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user currency");
                return new Dictionary<string, int>();
            }
        }
    }
}
