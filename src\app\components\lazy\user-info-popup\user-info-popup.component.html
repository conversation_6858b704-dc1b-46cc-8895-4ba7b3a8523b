<ng-container *ngIf="isVisible">
  <div class="user-info-modal" (appClickOutside)="isVisible = false">
    @if (!isLoadInfoUser) {
    <div class="user-info-loading">
      <div class="loading-spinner">
        <span class="animate-spin">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <mask id="path-1-inside-1_1881_16183" fill="white">
              <path
                d="M15.328 23.5293C17.8047 22.8144 19.9853 21.321 21.547 19.2701C23.1087 17.2193 23.9686 14.72 23.9992 12.1424C24.0297 9.56481 23.2295 7.04587 21.7169 4.95853C20.2043 2.8712 18.0597 1.32643 15.6007 0.552947C13.1417 -0.220538 10.499 -0.181621 8.0638 0.663935C5.62864 1.50949 3.53049 3.11674 2.07999 5.24771C0.629495 7.37868 -0.096238 9.92009 0.0102418 12.4957C0.116722 15.0713 1.04975 17.5441 2.6712 19.5481L4.96712 17.6904C3.74474 16.1796 3.04133 14.3154 2.96106 12.3737C2.88079 10.432 3.42791 8.51604 4.52142 6.90953C5.61493 5.30301 7.19671 4.09133 9.03255 3.45387C10.8684 2.81642 12.8607 2.78708 14.7145 3.3702C16.5683 3.95332 18.1851 5.1179 19.3254 6.69152C20.4658 8.26514 21.0691 10.1641 21.046 12.1074C21.023 14.0506 20.3748 15.9347 19.1974 17.4809C18.02 19.027 16.3761 20.1528 14.5089 20.6918L15.328 23.5293Z"
              ></path>
            </mask>
            <path
              d="M15.328 23.5293C17.8047 22.8144 19.9853 21.321 21.547 19.2701C23.1087 17.2193 23.9686 14.72 23.9992 12.1424C24.0297 9.56481 23.2295 7.04587 21.7169 4.95853C20.2043 2.8712 18.0597 1.32643 15.6007 0.552947C13.1417 -0.220538 10.499 -0.181621 8.0638 0.663935C5.62864 1.50949 3.53049 3.11674 2.07999 5.24771C0.629495 7.37868 -0.096238 9.92009 0.0102418 12.4957C0.116722 15.0713 1.04975 17.5441 2.6712 19.5481L4.96712 17.6904C3.74474 16.1796 3.04133 14.3154 2.96106 12.3737C2.88079 10.432 3.42791 8.51604 4.52142 6.90953C5.61493 5.30301 7.19671 4.09133 9.03255 3.45387C10.8684 2.81642 12.8607 2.78708 14.7145 3.3702C16.5683 3.95332 18.1851 5.1179 19.3254 6.69152C20.4658 8.26514 21.0691 10.1641 21.046 12.1074C21.023 14.0506 20.3748 15.9347 19.1974 17.4809C18.02 19.027 16.3761 20.1528 14.5089 20.6918L15.328 23.5293Z"
              stroke="black"
              class="stroke-white"
              stroke-width="14"
              mask="url(#path-1-inside-1_1881_16183)"
            ></path>
          </svg>
        </span>
        <p>Loading...</p>
      </div>
    </div>
    } @else {
    <div class="user-info-header">
      <div class="user-info-title">
        <img
          loading="lazy"
          src="/favicon.png"
          class="h-10 mr-1"
          alt="favicon-icon"
        />
        [Thông tin]
      </div>
      <div
        class="user-info-close"
        (click)="this.setVisible(false); isLoadInfoUser = false"
      >
        <svg
          class="h-6 w-6 text-gray-400 dark:text-gray-500"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <line x1="18" y1="6" x2="6" y2="18" />
          <line x1="6" y1="6" x2="18" y2="18" />
        </svg>
      </div>
    </div>

    <div class="user-info-content">
      <div class="user-info-avatar">
        <div class="relative">
          <img
            loading="lazy"
            [src]="UserInfo?.avatar"
            class="rounded-xl w-24 h-36 object-cover"
            alt="{{ UserInfo?.username }}"
            onerror="this.src='/default_avatar.jpg'"
          />
          <div
            class="user-info-level"
            style="background-color: white; border-color: #3498db"
          >
            <p class="font-semibold text-center">Lv.5</p>
          </div>
        </div>
      </div>

      <div class="user-info-details">
        <div class="flex justify-center mb-2 sm:mb-0">
          <span class="dark:text-gray-300">ID:</span
          ><span class="ml-3 font-semibold">#{{ UserInfo!.id }}</span>
        </div>

        <div class="flex justify-center mb-2 sm:mb-0">
          <!-- <span>Biệt danh: </span
              > -->
          <span class="uppercase font-bold text-xl">{{
            UserInfo?.username
          }}</span>
        </div>
        <div class="flex justify-center mb-2 sm:mb-0">
          <span class="dark:text-gray-300">Ngày tham gia: </span
          ><span class="ml-3 font-semibold">{{
            UserInfo?.createAt | date : "dd/MM/yyyy"
          }}</span>
        </div>
        <div class="flex justify-center mb-2 sm:mb-0">
          <span class="dark:text-gray-300"
            >{{ UserInfo?.typeLevel === 0 ? "Tinh hoa:" : "Kinh nghiệm:" }}
          </span>
          <span class="ml-3 font-semibold">{{ UserInfo?.experience }}</span>
        </div>
        <div class="flex justify-center">
          <span class="dark:text-gray-300"
            >{{ UserInfo?.typeLevel === 0 ? "Cảnh giới:" : "Cấp độ:" }}
          </span>
          <span class="ml-3 font-semibold text-primary-100">{{
            UserInfo?.levelInfo?.level
          }}</span>
        </div>
        <div class="flex justify-center mt-2 font-semibold text-sm">
          {{ UserInfo?.maxim ?? "Chưa cập nhật châm ngôn" }}
        </div>
      </div>
    </div>
    }
  </div>
</ng-container>
