// Modern Breadcrumb Component - Comic Website Design
.breadcrumb-container {
  @apply w-full dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 shadow-sm overflow-hidden;
}

// Breadcrumb Header
.breadcrumb-header {
  @apply flex items-center gap-3 p-1.5 bg-gray-50 dark:bg-neutral-800 border-b border-gray-200 dark:border-neutral-700;
}

.breadcrumb-icon-wrapper {
  @apply flex items-center justify-center w-8 h-8 bg-primary-100 rounded-lg shadow-sm;
}

.breadcrumb-icon {
  @apply w-4 h-4 text-white;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Breadcrumb List
.breadcrumb-list {
  @apply flex items-center flex-wrap gap-1 flex-1 min-w-0;
}

.breadcrumb-item {
  @apply flex items-center gap-2 transition-all duration-200;

  &.breadcrumb-item-current {
    @apply text-primary-100;
  }
}

// Breadcrumb Links
.breadcrumb-link {
  @apply inline-flex items-center px-2 py-1 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-100/50;
}

.breadcrumb-text {
  @apply truncate max-w-32 sm:max-w-48 md:max-w-none;
}

.breadcrumb-current {
  @apply px-2 py-1 text-sm font-bold text-primary-100 bg-primary-100/10 dark:bg-primary-100/20 rounded-md;
}

// Divider
.breadcrumb-divider {
  @apply flex items-center justify-center;
}

.divider-icon {
  @apply w-4 h-4 text-gray-400 dark:text-gray-500;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Mobile Breadcrumb
.breadcrumb-mobile {
  @apply flex md:hidden items-center justify-between p-3 bg-white dark:bg-dark-bg;
}

.breadcrumb-back-button {
  @apply flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-100/50;
}

.back-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.back-text {
  @apply text-sm font-medium truncate max-w-24;
}

.current-page-mobile {
  @apply flex items-center;
}

.current-page-text {
  @apply text-sm font-bold text-primary-100 truncate max-w-48;
}

// Responsive Design
@media (min-width: 768px) {
  .breadcrumb-mobile {
    @apply hidden;
  }
}

@media (max-width: 767px) {
  .breadcrumb-container {
    @apply border-0 shadow-none bg-transparent dark:bg-transparent;
  }
}

