<div class="genre-container bg-accent">
  <p class="genre-title">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      class="feather feather-info icon small text-icon-contrast text-undefined"
      viewBox="0 0 24 24"
    >
      <circle cx="12" cy="12" r="10"></circle>
      <path d="M12 16v-4m0-4h.01"></path>
    </svg>
    <span class="genre-label">Danh sách theo tên thể loại</span>
  </p>
  <form class="genre-search">
    <div class="genre-search-icon">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="none"
        viewBox="0 0 24 24"
        class="feather feather-info icon small text-icon-contrast text-undefined"
      >
        <path
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M11 19a8 8 0 1 0 0-16 8 8 0 0 0 0 16m10 2-4.35-4.35"
        ></path>
      </svg>
    </div>
    <input
      class="genre-input"
      type="search"
      placeholder="Tìm thể loại..."
      title="Search"
      (input)="filterGenres($event)"
      name="search"
      max="100"
      [value]="searchTerm"
    />
  </form>

  <div class="genre-list">
    <div class="genre-list-header">
      <span class="genre-list-title">Theo các nước</span>
      <hr class="genre-list-divider" />
    </div>
    <ul class="genre-list-items">
      <li
        *ngFor="let genre of filteredCatagoriesGenre.get('countries')"
        class="genre-list-item"
        [ngClass]="{
          'genre-list-item-normal': !statusGenres[genre.id],
          'genre-list-item-active': statusGenres[genre.id] === 1,
          'genre-list-item-disabled': statusGenres[genre.id] === 2
        }"
        (mouseover)="genreHovered = genre"
      >
        <div
          [routerLink]="routerLinkGenres ? ['tim-truyen'] : null"
          [queryParams]="routerLinkGenres ? { genres: genre.id } : null"
          (click)="clickGenre(genre)"
          class="genre-list-item-link"
        >
          <span class="genre-list-item-name">{{ genre.title }}</span>
        </div>
      </li>
    </ul>
  </div>

  <div class="genre-list">
    <div class="genre-list-header">
      <span class="genre-list-title">Thể loại</span>
      <hr class="genre-list-divider" />
    </div>
    <ul class="genre-list-items">
      <li
        *ngFor="let genre of filteredCatagoriesGenre.get('genreCommon')"
        class="genre-list-item"
        [ngClass]="{
          'genre-list-item-normal': !statusGenres[genre.id],
          'genre-list-item-active': statusGenres[genre.id] === 1,
          'genre-list-item-disabled': statusGenres[genre.id] === 2
        }"
        (mouseover)="genreHovered = genre"
      >
        <div
          [routerLink]="routerLinkGenres ? ['tim-truyen'] : null"
          [queryParams]="routerLinkGenres ? { genres: genre.id } : null"
          (click)="clickGenre(genre)"
          class="genre-list-item-link"
        >
          <span class="genre-list-item-name">{{ genre.title }}</span>
        </div>
      </li>
    </ul>
  </div>

  <div class="genre-hover-info">
    <div *ngIf="genreHovered">
      <hr class="genre-hover-divider" />
      <div class="genre-hover-content">
        <span class="genre-hover-icon">
          <svg
            class="h-5 w-5 text-gray-700 dark:text-gray-300"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <circle cx="12" cy="12" r="9" />
            <line x1="12" y1="8" x2="12.01" y2="8" />
            <polyline points="11 12 12 12 12 16 13 16" />
          </svg>
        </span>
        <p class="genre-hover-desc">{{ genreHovered.description }}</p>
      </div>
    </div>
  </div>
</div>
