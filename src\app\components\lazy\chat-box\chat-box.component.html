<ng-container *ngIf="isShowChatBox">
  <div
    class="fixed bottom-0 right-0 md:bottom-2 md:right-2 h-[425px] z-50 w-full sm:w-fit"
  >
    <div
      class="bg-white border-2 rounded-t-2xl overflow-hidden relative flex h-full gap-0 w-full"
    >
      <div
        #chatMenu
        class="absolute z-[100] bg-white left-0 top-0 h-full w-[250px] max-w-xs flex-none flex-col lg:relative lg:flex transition-all duration-300 ease-in-out"
        [ngClass]="{ '!w-0': !isShowChatMenu }"
      >
        <div
          class="chat-users relative h-full border-r border-[#e0e6ed] overflow-y-scroll scrollbar-style-1"
          appearance="compact"
        >
          <ng-container *ngFor="let person of searchUsers()">
            <button
              type="button"
              class="flex w-full h-[58px] items-center justify-between p-2 hover:bg-gray-100"
              [ngClass]="{
                'bg-gray-100':
                  selectedUser && selectedUser.userId === person.userId
              }"
              (click)="selectUser(person)"
            >
              <div class="flex">
                <div class="relative flex-shrink-0">
                  <img
                    [src]="person.path"
                    class="size-10 rounded-full object-cover"
                  />
                </div>
                <div class="mx-1 flex flex-col items-start text-left">
                  <p class="line-clamp-1 mb-1 font-semibold">
                    {{ person.name }}
                  </p>
                  <p class="line-clamp-1 text-xs text-white-dark">
                    {{ person.preview }}
                  </p>
                </div>
              </div>

              <div class="text-xs font-semibold shrink-0">
                <p>{{ person.time }}</p>
              </div>
            </button>
          </ng-container>
        </div>
      </div>
      <div
        class="absolute z-[5] h-full w-full rounded-md bg-black/60 lg:hidden"
        [ngClass]="{ hidden: !isShowChatMenu }"
        (click)="isShowChatMenu = false"
      ></div>

      <div class="flex flex-col w-full sm:w-[430px] relative h-full">
        <div
          class="flex items-center justify-between p-2 border-b border-[#e0e6ed]"
        >
          <div class="flex items-center">
            <button
              type="button"
              class="hover:text-primary-100"
              (click)="isShowChatMenu = !isShowChatMenu"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                ng-reflect-ng-class=""
                class="ng-star-inserted"
              >
                <path
                  d="M20 7L4 7"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                ></path>
                <path
                  opacity="0.5"
                  d="M20 12L4 12"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                ></path>
                <path
                  d="M20 17L4 17"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                ></path>
              </svg>
            </button>
            <div class="relative">
              <img
                [src]="selectedUser.path"
                class="h-10 w-10 rounded-full object-cover"
              />
              <div class="absolute bottom-0">
                <div class="h-4 w-4 rounded-full bg-success"></div>
              </div>
            </div>
            <div class="mx-3">
              <p class="font-semibold">{{ selectedUser.name }}</p>
            </div>
          </div>
          <button
            type="button"
            (click)="closeChat()"
            class="hover:text-primary-100"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
            >
              <circle
                opacity="0.5"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="1.5"
              ></circle>
              <path
                d="M14.5 9.50002L9.5 14.5M9.49998 9.5L14.5 14.5"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
              ></path>
            </svg>
          </button>
        </div>
        <div
          #chatBox
          class="chat-conversation-boxflex flex-col h-[calc(100%-90px)] relative p-4 overflow-y-scroll scrollbar-style-1 space-y-1 no-scrollbar"
        >
          <ng-container
            *ngIf="selectedUser.messages && selectedUser.messages.length"
          >
            <div *ngFor="let message of selectedUser.messages">
              <div
                class="flex items-start gap-3"
                [ngClass]="{
                  'justify-end': selectedUser.userId === message.fromUserId
                }"
              >
                <div
                  class="flex-none"
                  [ngClass]="{
                    'order-2': selectedUser.userId === message.fromUserId
                  }"
                >
                  <ng-container
                    *ngIf="selectedUser.userId === message.fromUserId"
                  >
                    <img
                      [src]="loginUser?.avatar"
                      class="h-10 w-10 rounded-full object-cover"
                    />
                  </ng-container>
                  <ng-container
                    *ngIf="selectedUser.userId !== message.fromUserId"
                  >
                    <img
                      alt="Ảnh đại diện"
                      [src]="selectedUser.path"
                      class="h-10 w-10 rounded-full object-cover"
                    />
                  </ng-container>
                </div>
                <div class="space-y-1">
                  <div class="flex items-center gap-3">
                    <div
                      class="rounded-md bg-black/10 px-4 py-1"
                      [ngClass]="
                        message.fromUserId === selectedUser.userId
                          ? 'ltr:rounded-br-none rtl:rounded-bl-none !bg-primary-100 text-white'
                          : 'ltr:rounded-bl-none rtl:rounded-br-none'
                      "
                      [innerHTML]="message.text"
                    >
                      <!-- {{ message.text }} -->
                    </div>
                    <div
                      [ngClass]="{
                        hidden: selectedUser.userId === message.fromUserId
                      }"
                    >
                      <img class="hover:text-primary-100" />
                    </div>
                  </div>
                  <div
                    class="text-xs text-white-dark"
                    [ngClass]="{
                      'text-right': selectedUser.userId === message.fromUserId
                    }"
                  >
                    {{ message.time ? message.time : "5h ago" }}
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
        <div class="absolute bottom-0 left-0 w-full">
          <div class="w-full items-center flex border-t-2 bg-gray-200 h-10">
            <div class="flex items-center py-3 sm:py-0">
              <button
                type="button"
                class="p-2 hover:bg-primary-light hover:text-primary-100 relative"
              >
                <div class="" (appClickOutside)="activeEmojiPicker = false">
                  <svg
                    class="h-8 w-8 text-gray-400 hover:bg-gray-200 rounded-full p-1 cursor-pointer"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    (click)="toggleEmojiPicker()"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" />
                    <circle cx="12" cy="12" r="9" />
                    <line x1="9" y1="9" x2="9.01" y2="9" />
                    <line x1="15" y1="9" x2="15.01" y2="9" />
                    <path d="M8 13a4 4 0 1 0 8 0m0 0H8" />
                  </svg>

                  <div *ngIf="activeEmojiPicker" class="mt-2">
                    <app-emoji
                      class="absolute top-0 -right-52"
                      (emojiSelect)="addEmoji($event)"
                    ></app-emoji>
                  </div>
                </div>
              </button>
            </div>
            <div class="relative w-full flex">
              <input
                class="border-0 px-4 py-2 bottom-0 left-0 w-full outline-none"
                placeholder="Nhập tin nhắn"
                [value]="textMessage"
                (change)="msgChange($event)"
                name="textMessage"
                (keyup.enter)="sendMessage()"
              />
              <button
                type="button"
                class="p-2 hover:text-primary-100 rounded-r-lg"
                (click)="sendMessage()"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6"
                >
                  <path
                    d="M17.4975 18.4851L20.6281 9.09373C21.8764 5.34874 22.5006 3.47624 21.5122 2.48782C20.5237 1.49939 18.6511 2.12356 14.906 3.37189L5.57477 6.48218C3.49295 7.1761 2.45203 7.52305 2.13608 8.28637C2.06182 8.46577 2.01692 8.65596 2.00311 8.84963C1.94433 9.67365 2.72018 10.4495 4.27188 12.0011L4.55451 12.2837C4.80921 12.5384 4.93655 12.6658 5.03282 12.8075C5.22269 13.0871 5.33046 13.4143 5.34393 13.7519C5.35076 13.9232 5.32403 14.1013 5.27057 14.4574C5.07488 15.7612 4.97703 16.4131 5.0923 16.9147C5.32205 17.9146 6.09599 18.6995 7.09257 18.9433C7.59255 19.0656 8.24576 18.977 9.5522 18.7997L9.62363 18.79C9.99191 18.74 10.1761 18.715 10.3529 18.7257C10.6738 18.745 10.9838 18.8496 11.251 19.0285C11.3981 19.1271 11.5295 19.2585 11.7923 19.5213L12.0436 19.7725C13.5539 21.2828 14.309 22.0379 15.1101 21.9985C15.3309 21.9877 15.5479 21.9365 15.7503 21.8474C16.4844 21.5244 16.8221 20.5113 17.4975 18.4851Z"
                    stroke="currentColor"
                    stroke-width="1.5"
                  ></path>
                  <path
                    opacity="0.5"
                    d="M6 18L21 3"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-linecap="round"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>
