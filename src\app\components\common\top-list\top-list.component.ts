import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Input, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic, IServiceResponse, TopType } from '@schema';
import { ComicService } from '@services/comic.service';

@Component({
  selector: 'app-top-list',
  templateUrl: './top-list.component.html',
  styleUrl: './top-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  // encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, RouterLink, NumeralPipe],
})
export class TopListComponent implements OnInit {
  @Input() listTopComics!: Comic[];
  topType!: number;

  constructor(
    private ComicService: ComicService,
    @Inject(PLATFORM_ID) private platformId: Document,
    private cd: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.getTopComic(TopType.Day);
    }
  }

  getTopComic(type: TopType): void {
    this.topType = type;
    this.ComicService.getTopComics(this.topType).subscribe((res: IServiceResponse<Comic[]>) => {
      if (!res.data) return;
      this.listTopComics = res.data;
      this.cd.detectChanges();
    });
  }

  // TrackBy function for performance optimization
  trackByComicId = (_index: number, comic: Comic): number => {
    return comic.id;
  };

  // Get rank class based on position
  getRankClass(rank: number): string {
    switch (rank) {
      case 1:
        return 'rank-gold';
      case 2:
        return 'rank-silver';
      case 3:
        return 'rank-bronze';
      default:
        return 'rank-default';
    }
  }
}
