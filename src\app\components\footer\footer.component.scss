// Modern Comic-themed Footer Styles
.footer-container {
  @apply relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900;
  @apply dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900;
  @apply text-white overflow-hidden;
}

// Background Elements
.footer-background {
  @apply absolute inset-0 pointer-events-none;
}

.comic-elements {
  @apply absolute inset-0;
}

.comic-bubble {
  @apply absolute text-2xl opacity-10 animate-pulse;
  animation-duration: 4s;
  animation-iteration-count: infinite;

  &.bubble-1 {
    @apply top-10 left-10;
    animation-delay: 0s;
  }

  &.bubble-2 {
    @apply top-20 right-20;
    animation-delay: 1s;
  }

  &.bubble-3 {
    @apply bottom-20 left-20;
    animation-delay: 2s;
  }

  &.bubble-4 {
    @apply bottom-10 right-10;
    animation-delay: 3s;
  }
}

// Main Content
.footer-content {
  @apply relative z-10 max-w-7xl mx-auto px-4 py-12;
}

.footer-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12;
}

// Brand Section
.footer-brand {
  @apply lg:col-span-1;
}

.brand-logo {
  @apply flex items-center gap-4 mb-6;
}

.logo-image {
  @apply w-12 h-12 object-contain;
}

.brand-text {
  @apply flex-1;
}

.brand-name {
  @apply text-xl font-bold text-white mb-1;
  background: linear-gradient(135deg, #E83A3A 0%, #ff6b6b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-tagline {
  @apply text-sm text-gray-400;
}

.brand-description {
  @apply text-gray-300 text-sm leading-relaxed mb-6;
}

// Social Media
.social-section {
  @apply space-y-4;
}

.social-title {
  @apply text-lg font-semibold text-white mb-4;
}

.social-links {
  @apply flex gap-3;
}

.social-link {
  @apply flex items-center gap-2 px-4 py-2 bg-gray-800 hover:bg-gray-700;
  @apply rounded-lg transition-all duration-200 text-sm;
  @apply hover:scale-105 hover:shadow-lg;

  &.facebook {
    @apply hover:bg-blue-600;
  }

  &.twitter {
    @apply hover:bg-blue-400;
  }

  &.discord {
    @apply hover:bg-indigo-600;
  }
}

.social-icon {
  @apply w-4 h-4;
}

// Footer Sections
.footer-links,
.footer-tags,
.footer-newsletter {
  @apply space-y-4;
}

.section-title {
  @apply text-lg font-semibold text-white mb-4;
}

// Quick Links
.links-grid {
  @apply space-y-6;
}

.link-group {
  @apply space-y-3;
}

.link-group-title {
  @apply text-sm font-semibold text-gray-300 uppercase tracking-wide;
}

.link-list {
  @apply space-y-2;
}

.footer-link {
  @apply text-gray-400 hover:text-white transition-colors duration-200;
  @apply text-sm hover:underline;
}

// Popular Tags
.tags-container {
  @apply flex flex-wrap gap-2;
}

.tag-item {
  @apply px-3 py-1 bg-gray-800 hover:bg-primary-100 text-gray-300 hover:text-white;
  @apply rounded-full text-xs transition-all duration-200;
  @apply hover:scale-105;
}

// Newsletter
.newsletter-description {
  @apply text-gray-400 text-sm mb-4;
}

.newsletter-form {
  @apply space-y-3;
}

.input-group {
  @apply flex gap-2;
}

.newsletter-input {
  @apply flex-1 px-3 py-2 bg-gray-800 border border-gray-700;
  @apply rounded-lg text-white placeholder-gray-400;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:border-transparent;
}

.newsletter-button {
  @apply px-4 py-2 bg-primary-100 hover:bg-primary-200 text-white;
  @apply rounded-lg transition-colors duration-200;
  @apply hover:scale-105;
}

// Facebook Section
.facebook-section {
  @apply mb-8 flex justify-center;
}

// Footer Bottom
.footer-bottom {
  @apply border-t border-gray-700 pt-8;
}

.footer-bottom-content {
  @apply flex flex-col lg:flex-row justify-between items-center gap-6;
}

.copyright-section {
  @apply text-center lg:text-left;
}

.copyright-text {
  @apply text-white font-medium mb-2;
}

.disclaimer-text {
  @apply text-gray-400 text-sm leading-relaxed;
}

.footer-stats {
  @apply flex gap-8;
}

.stat-item {
  @apply text-center;
}

.stat-number {
  @apply block text-2xl font-bold text-primary-100;
}

.stat-label {
  @apply text-gray-400 text-sm;
}

// Responsive Design
@media (max-width: 768px) {
  .footer-grid {
    @apply grid-cols-1 gap-6;
  }

  .brand-logo {
    @apply justify-center text-center;
  }

  .social-links {
    @apply justify-center;
  }

  .tags-container {
    @apply justify-center;
  }

  .footer-stats {
    @apply gap-4;
  }

  .stat-number {
    @apply text-xl;
  }

  .comic-bubble {
    @apply text-lg;
  }
}