
.inventory-container {
  @apply max-w-7xl mx-auto space-y-6;
}

// Header
.inventory-header {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm;
}

.inventory-header__title {
  @apply space-y-1;
}

.inventory-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100;
}

.inventory-subtitle {
  @apply text-gray-600 dark:text-gray-400;
}

.inventory-header__actions {
  @apply flex items-center gap-3;
}

// View Mode Toggle
.view-mode-toggle {
  @apply flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1;
}

.view-mode-btn {
  @apply px-3 py-2 rounded-md text-sm font-medium transition-colors;
  @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100;

  &--active {
    @apply bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm;
  }
}

// Card Size Toggle
.card-size-toggle {
  @apply flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1;
}

.size-btn {
  @apply w-8 h-8 rounded-md text-sm font-medium transition-colors;
  @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100;

  &--active {
    @apply bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm;
  }
}

// Filters
.inventory-filters {
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm;
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4;
}

.filter-group {
  @apply space-y-2;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.filter-input, .filter-select {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
  @apply rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply text-sm;
}

// Selected Actions
.selected-actions {
  @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700;
  @apply rounded-lg p-4 flex items-center justify-between;
}

.selected-info {
  @apply text-blue-700 dark:text-blue-300 font-medium;
}

.selected-buttons {
  @apply flex gap-2;
}

.action-btn {
  @apply px-4 py-2 rounded-md text-sm font-medium transition-colors;

  &--secondary {
    @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
    @apply hover:bg-gray-200 dark:hover:bg-gray-600;
  }
}

// Loading
.loading-container {
  @apply flex flex-col items-center justify-center py-12 space-y-4;
  @apply text-gray-500 dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin;
}

// Empty State
.empty-state {
  @apply text-center py-12 space-y-4;
}

.empty-state__icon {
  @apply text-6xl text-gray-300 dark:text-gray-600;
}

.empty-state__title {
  @apply text-xl font-semibold text-gray-900 dark:text-gray-100;
}

.empty-state__description {
  @apply text-gray-600 dark:text-gray-400 max-w-md mx-auto;
}

// Items Grid
.inventory-content {
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm;
}

.items-grid {
  @apply grid gap-4;

  &--small {
    @apply grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10;
  }

  &--medium {
    @apply grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5;
  }

  &--large {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
}

// Items List
.items-list {
  @apply space-y-2;
}

.list-header {
  @apply grid grid-cols-5 gap-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg;
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.list-body {
  @apply space-y-1;
}

.list-row {
  @apply grid grid-cols-5 gap-4 p-3 rounded-lg transition-colors;
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;

  &--selected {
    @apply bg-blue-50 dark:bg-blue-900/20;
  }

  &--equipped {
    @apply bg-green-50 dark:bg-green-900/20;
  }
}

.list-col {
  @apply flex items-center;

  &--item {
    @apply gap-3;
  }

  &--actions {
    @apply gap-2 justify-end;
  }
}

.list-checkbox {
  @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded;
  @apply focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800;
  @apply dark:bg-gray-700 dark:border-gray-600;
}

.list-item-icon {
  @apply w-10 h-10 rounded-lg object-cover;
}

.list-item-info {
  @apply flex flex-col min-w-0;
}

.list-item-name {
  @apply font-medium text-gray-900 dark:text-gray-100 truncate;
}

.list-item-description {
  @apply text-sm text-gray-600 dark:text-gray-400 truncate;
}

.category-badge, .rarity-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.category-badge {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
}

.list-action-btn {
  @apply px-3 py-1 rounded text-xs font-medium transition-colors;

  &--primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }

  &--secondary {
    @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
    @apply hover:bg-gray-200 dark:hover:bg-gray-600;
  }

  &--ghost {
    @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100;
  }
}

// Pagination
.inventory-pagination {
  @apply flex items-center justify-center gap-4 py-4;
}

.pagination-btn {
  @apply px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600;
  @apply text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.pagination-info {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// Responsive adjustments


@screen lg {
  .inventory-filters {
    @apply grid-cols-6;
  }
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .inventory-container {
    @apply bg-gray-900;
  }
}
