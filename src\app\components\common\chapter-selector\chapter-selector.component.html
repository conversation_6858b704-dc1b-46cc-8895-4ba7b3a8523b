<!-- Modern Chapter Selector -->
<div (appClickOutside)="isDropdownOpen = false" class="chapter-selector">
  <!-- Trigger Button -->
  <div *ngIf="mainChapter" class="selector-trigger" (click)="toggleDropdown()">
    <div class="trigger-content">
      <div class="trigger-icon">
        <svg class="icon-book" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/>
          <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/>
        </svg>
      </div>
      <div class="trigger-text">
        <span class="trigger-title">{{ displayTitle }}</span>
      </div>
    </div>

    <div class="trigger-arrow" [class.rotated]="isDropdownOpen">
      <svg class="arrow-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="6 9 12 15 18 9"/>
      </svg>
    </div>
  </div>

  <!-- Dropdown Panel -->
  <div
    *ngIf="isDropdownOpen"
    class="selector-dropdown"
    [class.dropdown-top]="!topToBottom"
    [class.dropdown-bottom]="topToBottom">

    <!-- Header -->
    <div class="dropdown-header">
      <div class="header-title">
        <svg class="header-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
        </svg>
        <span>Danh sách chương</span>
      </div>
      <div class="chapter-count">{{ filteredChapters.length }} chương</div>
    </div>

    <!-- Search Input -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <input
          type="text"
          (input)="onSearch($event)"
          maxlength="255"
          class="search-input"
          placeholder="Tìm kiếm chương..."
          [value]="searchTerm"
        />
        <button
          *ngIf="searchTerm"
          (click)="resetSearch()"
          class="search-clear">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Chapter List -->
    <div class="chapter-list-wrapper">
      <div *ngIf="!hasFilteredChapters && searchTerm" class="empty-state">
        <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <span class="empty-text">Không tìm thấy chương nào</span>
      </div>

      <app-loop-scroll
        *ngIf="hasFilteredChapters"
        class="chapter-scroll"
        [allitems]="filteredChapters"
        [selectedID]="mainChapter?.id"
        [itemHeight]="37">

        <ng-template #ItemTemplate let-item="item;">
          <div
            [routerLink]="['/truyen-tranh', item.slug]"
            (click)="selectChapter(item)"
            class="chapter-item"
            [class.selected]="item.id === mainChapter?.id">

            <div class="chapter-info">
              <div class="chapter-number">#{{ item.title }}</div>
              <div class="chapter-meta">
                <span class="chapter-date" *ngIf="item.updateAt">
                  {{ item.updateAt | dateAgo }}
                </span>
              </div>
            </div>

            <div class="chapter-status">
              <svg class="arrow-right" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9 18 15 12 9 6"/>
              </svg>
            </div>
          </div>
        </ng-template>
      </app-loop-scroll>
    </div>

    <!-- Footer -->
    
  </div>
</div>

<ng-content />
