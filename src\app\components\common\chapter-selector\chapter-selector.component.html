<div (appClickOutside)="isDropdownOpen = false" class="chapter-selector">
  <div *ngIf="mainChapter" class="chapter-selector-trigger" (click)="toggleDropdown()">
    <span class="chapter-selector-title"> {{ displayTitle }}</span>

    <svg
      class="chapter-selector-icon"
      [ngClass]="{ rotated: isDropdownOpen }"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <polyline points="6 9 12 15 18 9" />
    </svg>
  </div>
  <div
    *ngIf="isDropdownOpen"
    class="chapter-dropdown"
    [ngClass]="{ 'top-9': topToBottom, 'bottom-9': !topToBottom, }"
  >
    <input
      type="text"
      (input)="onSearch($event)"
      maxlength="255"
      class="chapter-search-input"
      placeholder="Tìm chương..."
      [value]="searchTerm"
    />
    <div class="chapter-list-container">
      <app-loop-scroll
        class="chapter-scroll"
        [allitems]="filteredChapters"
        [selectedID]="mainChapter?.id"
        [itemHeight]="28"
      >
        <ng-template #ItemTemplate let-item="item;">
          <div
            [routerLink]="['/truyen-tranh', item.slug]"
            (click)="selectChapter(item)"
            class="chapter-item"
            [class.chapter-item-selected]="item.id === mainChapter?.id"
            [class.chapter-item-default]="item.id !== mainChapter?.id"
          >
            #{{ item.title }}
          </div>
        </ng-template>
      </app-loop-scroll>
    </div>
  </div>
</div>
<ng-content />
