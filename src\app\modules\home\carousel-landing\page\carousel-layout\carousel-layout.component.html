<div class="block-item">
  <a
    *ngFor="let comic of comics; let i = index"
    [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
    [class]="classes[i]"
  >
    <img
      (mouseenter)="OnComicHover(comic)"
      (mouseleave)="OnComicLeave()"
      class="object-cover h-full w-full transition-[filter] hover:brightness-75"
      src="{{ comic.coverImage }}"
      alt="{{ comic.title }}"
      onerror="this.src='/option2.png'"
    />
  </a>
</div>
