import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Output, QueryList, ViewChildren, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-swiper',
  templateUrl: './swiper.component.html',
  styleUrl: './swiper.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
})
export class SwiperComponent implements AfterViewInit {
  Btns: number[] = [1, 2, 3, 4, 5, 6, 7, 8, 9];
  @ViewChildren('btn') Elements!: QueryList<ElementRef>;
  value = 0;
  width = 20;
  current = 4;
  lastTime = 0;
  @Output() nextChange = new EventEmitter<number>();

constructor(private cd: ChangeDetectorRef) {
}

  // ngOnChanges() {
  // }

  ngAfterViewInit() {

    this.Elements?.forEach((e: any, index) => {
      let v = this.value + (index) * this.width;
      v %= this.width * this.Btns.length
      v -= this.width * 2
      e.nativeElement.style.left = `${v}px`;
    })
  }
  click(id: number) {
    const now = Date.now();
    if (now - this.lastTime < 500) {
      return;
    }
    this.lastTime = now;
    let direct = ((id - this.current) % this.Btns.length + this.Btns.length) % this.Btns.length;
    this.change(direct);
    if (direct >= this.Btns.length - 2) {
      direct = direct - this.Btns.length
    }
    this.nextChange.emit(direct);
  }
  next() {
    this.change(1);
  }
  prev() {
    this.change(this.Btns.length - 1);
  }
  change(direct: number) {
    this.value += this.width * direct;
    const isNext = direct <= 2;
    this.Elements?.forEach((e: any, index) => {
      let v = this.value + (index) * this.width;
      v %= this.width * this.Btns.length;
      v -= this.width * 2
      if (isNext && (v == -this.width || v == -this.width * 2) ||
        !isNext && (v == (this.Btns.length - 4) * this.width || (this.Btns.length - 3) * this.width == v)) {
        e.nativeElement.classList.add('invisible');
      }
      else {
        e.nativeElement.classList.remove('invisible');
      }

      e.nativeElement.style.left = `${v}px`;
    })
    this.current = ((this.current - direct) % this.Btns.length + this.Btns.length) % this.Btns.length;
    this.cd.detectChanges();
  }
}
