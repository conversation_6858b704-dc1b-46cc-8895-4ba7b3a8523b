<div id="content" class="home-content">
  <!-- <div> <img src="/image.png" class="w-full h-20 object-cover my-3"></div> -->
  <app-anouncement></app-anouncement>
  <div
    class="flex h-[36px] mt-3 flex-row justify-between gap-6 bg-dark-bg dark:bg-black rounded-t"
  >
    <div class="min-w-32 flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="currentColor"
        class="text-white h-6 w-6 ml-2"
        viewBox="0 0 32 32"
        id="icon"
      >
        <defs>
          <style>
            .cls-1 {
              fill: none;
            }
          </style>
        </defs>
        <title>recommend</title>
        <path
          d="M16,2a9,9,0,0,0-6,15.69V30l6-4,6,4V17.69A9,9,0,0,0,16,2Zm4,24.26-2.89-1.92L16,23.6l-1.11.74L12,26.26V19.05a8.88,8.88,0,0,0,8,0ZM20.89,16A7,7,0,1,1,23,11,7,7,0,0,1,20.89,16Z"
        />
        <rect
          id="_Transparent_Rectangle_"
          data-name="&lt;Transparent Rectangle&gt;"
          class="cls-1"
          width="32"
          height="32"
        />
      </svg>
      <h1 class="block-title !text-white">Truyện đề cử</h1>
    </div>
  </div>
  <app-carousel-landing class="carousel-landing"> </app-carousel-landing>
  <!-- <app-carousel-layout></app-carousel-layout> -->

  <div class="grid grid-cols-1 xl:grid-cols-4 gap-2 lg:gap-4">
    <div id="comics" class="xl:col-span-3 row-span-3">
      <app-grid-comic
        [listComics]="listComics"
        [nPreview]="ssr() ? 12 : 30"
        [title]="'Mới cập nhật'"
        [_class]="
          'grid gap-[12px] grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-5 xl:grid-cols-5 2xl:grid-cols-5 mx-3 lg:mx-0'
        "
      >
        <svg
          class="size-6"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <path
            d="M19 18a3.5 3.5 0 0 0 0 -7h-1a5 4.5 0 0 0 -11 -2a4.6 4.4 0 0 0 -2.1 8.4"
          />
          <line x1="12" y1="13" x2="12" y2="22" />
          <polyline points="9 19 12 22 15 19" />
        </svg>
      </app-grid-comic>
      <app-pagination
        *ngIf="!ssr()"
        (OnChange)="OnChangePage($event)"
        [currentPage]="currentPage"
        [totalpage]="totalpage"
      >
      </app-pagination>
    </div>
    <div class="row-start-1 xl:row-start-auto">
      <app-recent-read class="mt-6 lg:mt-3 block"></app-recent-read>
    </div>

    <app-top-list class="mb-4"></app-top-list>
  </div>
</div>
