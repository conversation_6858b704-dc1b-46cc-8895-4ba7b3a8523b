import { isPlatformServer } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { Comic, ComicStatus, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import {
  IFilters,
  rankFiltersOptions,
} from '../../components/utils/constants';
import { UrlService } from '@services/url.service';

@Component({
  selector: 'app-rank',
  templateUrl: './rank.component.html',
  styleUrl: './rank.component.scss',
  standalone: false
})
export class RankComponent implements OnInit {
  listComics!: Comic[];
  listTopComics!: Comic[];

  totalpage!: number;
  totalResult!: number;
  currentPage = 1;
  dataView!: IFilters;

  selectedComic?: Comic;
  isShowDetails = false;

  selectOptions: any = {
    sorts: { value: 8, name: 'Top All' },
    status: { value: -1, name: 'T<PERSON><PERSON> c<PERSON>' },
  };
  private isOnInit = false;

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: object,
  ) {
    this.dataView = {
      status: rankFiltersOptions.status,
      sorts: rankFiltersOptions.sorts,
    };
    this.SetupSeo();
  }

  ssr() {
    return isPlatformServer(this.platformId);
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      const page = Number(params['page']) || 1;
      const status =
        Number(params['status']) >= 0
          ? Number(params['status'])
          : ComicStatus.ALL;
      const sort =
        Number(params['sort']) >= 0 ? Number(params['sort']) : SortType.TopAll;

      this.currentPage = page;
      this.OnSearchComic(page, sort, status);
    });
  }


  OnSearchComic(page: number, sort: number, status: number) {
    this.comicService
      .getComics({
        step: '35',
        genre: '-1',
        page: page.toString(),
        sort: sort.toString(),
        status: status.toString(),
      })
      .subscribe((res: any) => {
        this.totalpage = res.data.totalpage;
        this.listComics = res.data.comics;
        // Show details of the first comic if not already selected
        if (!this.selectedComic && this.listComics.length > 0) {
          this.showDetails(this.listComics[0]);
        }
        this.GetTopComics();
        this.cd.detectChanges();
      });
  }

  GetTopComics() {
    if (!this.listComics) return;
    this.listTopComics = this.listComics.slice(0, 5);
  }

  OnSortOptionChange(value: number) {
    this.selectOptions.sorts.value = value;
    this.router.navigate([], {
      queryParams: {
        sort: this.selectOptions.sorts.value,
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }
  OnStatusOptionChange(value: number) {
    this.selectOptions.status.value = value;
    this.router.navigate([], {
      queryParams: {
        status: this.selectOptions.status.value,
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }


  showDetails(comic: Comic) {
    this.selectedComic = comic;
  }

  OnChangePage(page: number) {
    this.router.navigate([], {
      queryParams: { page: page },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }
  SetupSeo() {
    this.seoService.setTitle('Bảng xếp hạng truyện tranh - MeTruyenMoi');
    this.seoService.addTags([
      {
        name: 'description',
        content:
          'Bảng xếp hạng - Xếp hạng tất cả truyện đều có thể tìm thấy tại Mê truyện mới- MeTruyenMoi',
      },
      {
        property: 'og:title',
        content: 'Bảng xếp hạng truyện tranh - MeTruyenMoi',
      },
      {
        property: 'og:description',
        content:
          'Bảng xếp hạng - Xếp hạng tất cả truyện đều có thể tìm thấy tại Mê truyện mới - MeTruyenMoi',
      },
      { name: 'keywords', content: 'Bảng xếp hạng truyện tranh, Mê truyện, Mê Truyện Mới, Truyen tranh online, Đọc truyện tranh, Truyện tranh hot, Truyện tranh hay, Truyện ngôn tình, metruyen, metruyentranh, metruyenmoi, me truyen moi' },

    ]);
    this.seoService.updateLink('canonical', `${this.urlService.BASE_URL}/xep-hang`); // Update the canonical link;
  }
}
