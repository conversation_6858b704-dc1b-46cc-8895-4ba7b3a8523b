<div class="container mx-auto mt-5">
  <div *ngIf="user" class="flex overflow-hidden h-full">
    <aside
      id="sidebar"
      class="z-20 flex flex-col flex-shrink-0 hidden w-64 bottom-0 font-normal duration-75 lg:flex transition-width"
      aria-label="Sidebar"
    >
      <div
        class="relative flex flex-col flex-1 h-full pt-0 border border-gray-200 dark:border-neutral-700"
      >
        <div class="flex flex-col flex-1 pt-5 pb-4 overflow-y-auto">
          <div
            class="flex-1 px-3 space-y-1 divide-y divide-gray-200 dark:divide-gray-700"
          >
            <ul class="pb-2 space-y-2">
              <li>
                <span
                  class="flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-primary-200 group dark:text-gray-200 dark:hover:bg-neutral-700 tab-selected"
                >
                  <svg
                    class="w-6 h-6 text-white transition duration-75 dark:text-gray-400 dark:group-hover:text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                    <path
                      d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"
                    ></path>
                  </svg>
                  <span class="ml-3" sidebar-toggle-item=""
                    >Thông tin cá nhân</span
                  >
                </span>
              </li>

              <li>
                <span
                  class="flex items-center p-2 text-base text-gray-900 rounded-lg group dark:text-gray-200 cursor-pointer"
                  (click)="onSettingClick()"
                >
                  <svg
                    class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                  >
                    <path
                      clip-rule="evenodd"
                      fill-rule="evenodd"
                      d="M8.34 1.804A1 1 0 019.32 1h1.36a1 1 0 01.98.804l.295 1.473c.497.144.971.342 1.416.587l1.25-.834a1 1 0 011.262.125l.962.962a1 1 0 01.125 1.262l-.834 1.25c.245.445.443.919.587 1.416l1.473.294a1 1 0 01.804.98v1.361a1 1 0 01-.804.98l-1.473.295a6.95 6.95 0 01-.587 1.416l.834 1.25a1 1 0 01-.125 1.262l-.962.962a1 1 0 01-1.262.125l-1.25-.834a6.953 6.953 0 01-1.416.587l-.294 1.473a1 1 0 01-.98.804H9.32a1 1 0 01-.98-.804l-.295-1.473a6.957 6.957 0 01-1.416-.587l-1.25.834a1 1 0 01-1.262-.125l-.962-.962a1 1 0 01-.125-1.262l.834-1.25a6.957 6.957 0 01-.587-1.416l-1.473-.294A1 1 0 011 10.68V9.32a1 1 0 01.804-.98l1.473-.295c.144-.497.342-.971.587-1.416l-.834-1.25a1 1 0 01.125-1.262l.962-.962A1 1 0 015.38 3.03l1.25.834a6.957 6.957 0 011.416-.587l.294-1.473zM13 10a3 3 0 11-6 0 3 3 0 016 0z"
                    ></path>
                  </svg>
                  <span class="ml-3" sidebar-toggle-item=""
                    >Cài đặt giao diện</span
                  >
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </aside>

    <div id="main-content" class="relative w-full h-full">
      <div class="">
        <div class="grid grid-cols-1 px-4 xl:grid-cols-3 xl:gap-4">
          <!-- Right Content -->
          <div class="col-span-full xl:col-auto">
            <div
              class="p-4 mb-4 border border-gray-200 rounded-2xl shadow-sm 2xl:col-span-2 dark:border-neutral-700 sm:p-6 relative"
            >
              <div class="items-center flex-col flex">
                <div class="ml-0 flex flex-col w-full h-full gap-10">
                  <div class="w-100">
                    <label
                      for="dropzone-file"
                      class="flex flex-col items-center justify-center border-gray-300 cursor-pointer relative"
                    >
                      <img
                        loading="lazy"
                        class="rounded-2xl w-37 h-37 object-cover mb-10 lg:mb-5"
                        [src]="avatar"
                        (error)="
                          avatar =
                            'https://static.vecteezy.com/system/resources/previews/002/002/257/non_2x/beautiful-woman-avatar-character-icon-free-vector.jpg'
                        "
                        alt="Jese picture"
                      />

                      <input
                        id="dropzone-file"
                        type="file"
                        class="hidden"
                        accept="image/*"
                        (change)="onFileChange($event)"
                      />
                    </label>
                    <div
                      class="dark:text-white uppercase w-full text-xs flex flex-col items-center absolute bottom-40 left-0"
                    >
                      <svg
                        class="mt-10 h-12 w-12 text-purple-500 text-center flex items-center bg-white dark:bg-dark-bg p-0.5 rounded-full"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        stroke-width="2"
                        stroke="currentColor"
                        fill="none"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path stroke="none" d="M0 0h24v24H0z" />
                        <circle cx="12" cy="12" r="9" />
                        <path d="M12 3a4.5 4.5 0 0 0 0 9a4.5 4.5 0 0 1 0 9" />
                        <circle cx="12" cy="7.5" r=".5" fill="currentColor" />
                        <circle cx="12" cy="16.5" r=".5" fill="currentColor" />
                      </svg>
                      <span
                        class="text-xs font-bold bg-white rounded-lg p-2 dark:bg-dark-bg"
                      >
                        {{ levelUser.level }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="p-4 w-full 2xl:col-span-2 sm:p-6">
                  <div class="flow-root">
                    <div class="ml-2 my-2">
                      <div
                        class="flex justify-center gap-20 mb-1 text-2xl font-bold dark:text-white"
                      >
                        <div>
                          <span>XP</span>
                          <div class="font-medium text-xs text-center">
                            Level
                          </div>
                        </div>
                        <div>
                          <span class="text-purple-500">
                            {{ levelUser.percent }}%</span
                          >
                          <div class="font-medium text-xs text-center">
                            {{ levelUser.nextLevel }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <ul class="mt-5 flex justify-center items-center gap-3">
                      <li
                        class="ml-2 flex flex-col gap-1 justify-center items-center"
                      >
                        <input
                          id="radio-tutien"
                          name="options"
                          type="radio"
                          value="tutien"
                          [checked]="user.typeLevel === 0"
                          (change)="updateTypelevel(0)"
                          [ngClass]="{
                            'bg-primary-100 dark:bg-primary-100 border-2 border-white dark:border-neutral-700 ring-2 ring-primary-50':
                              user.typeLevel === 0
                          }"
                          class="appearance-none w-4 h-4 text-primary-100 rounded-full border border-gray-300 bg-gray-100 dark:bg-neutral-600 dark:border-neutral-500"
                        />

                        <div
                          for="radio-tutien"
                          class="text-sm font-medium text-gray-900 dark:text-gray-300"
                        >
                          Tu tiên
                        </div>
                      </li>
                      <li
                        class="ml-2 flex flex-col gap-1 justify-center items-center"
                      >
                        <input
                          id="radio-capdo"
                          name="options"
                          type="radio"
                          value="capdo"
                          [checked]="user.typeLevel === 1"
                          (change)="updateTypelevel(1)"
                          [ngClass]="{
                            'bg-primary-100 dark:bg-primary-100 border-2 border-white dark:border-neutral-700 ring-2 ring-primary-50':
                              user.typeLevel === 1
                          }"
                          class="appearance-none w-4 h-4 text-primary-100 rounded-full border border-gray-300 bg-gray-100 dark:bg-neutral-600 dark:border-neutral-500"
                        />
                        <div
                          for="radio-capdo"
                          class="text-sm font-medium text-gray-900 dark:text-gray-300"
                        >
                          Cấp độ
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="p-4 mb-4 border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-neutral-700 sm:p-6"
            >
              <div class="flow-root">
                <h3 class="text-xl font-semibold dark:text-white">Châm ngôn</h3>
                <textarea
                  [(ngModel)]="maxim"
                  [textContent]="maxim"
                  name=""
                  id=""
                  class="w-full border dark:border-neutral-600 dark:text-white dark:bg-neutral-700 py-2 px-3"
                ></textarea>
                <button
                  (click)="onUpdateMaxim(maxim)"
                  class="bg-primary-100 text-white hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  Lưu
                </button>
              </div>
            </div>

            <div
              class="flex justify-content cursor-default select-none p-[13px] mb-4 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-sm 2xl:col-span-2 sm:p-[13px]"
            >
              <p class="text-sm font-semibold dark:text-white mr-2">
                Ngày tham gia:
              </p>
              <span class="text-sm font-medium dark:text-white">{{
                user.createAt | date : "dd/MM/yyyy"
              }}</span>
            </div>
          </div>
          <div class="col-span-2">
            <div
              class="p-4 mb-4 border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-neutral-700 sm:p-6"
            >
              <h3 class="mb-4 text-xl font-semibold dark:text-white">
                Thông tin chung
              </h3>
              <form
                [formGroup]="infoForm"
                (ngSubmit)="$event.preventDefault(); onUpdateInfo()"
              >
                <div class="grid grid-cols-6 gap-6">
                  <div class="col-span-6 sm:col-span-3">
                    <label
                      for="first-name"
                      class="block mb-2 font-medium text-gray-900 dark:text-white"
                    >
                      <div class="flex items-center gap-1">
                        <svg
                          class="h-6 w-6"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          stroke-width="2"
                          stroke="currentColor"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path stroke="none" d="M0 0h24v24H0z" />
                          <circle cx="12" cy="12" r="9" />
                          <line x1="9" y1="10" x2="9.01" y2="10" />
                          <line x1="15" y1="10" x2="15.01" y2="10" />
                          <path d="M9.5 15a3.5 3.5 0 0 0 5 0" />
                        </svg>

                        <span>Tên</span>
                      </div>
                    </label>
                    <input
                      type="text"
                      name="first-name"
                      id="first-name"
                      class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                      placeholder="Bonnie"
                      required=""
                      [value]="user.firstName"
                      formControlName="firstName"
                    />
                    <div
                      *ngIf="
                        (infoForm.get('firstName')?.invalid &&
                          infoForm.get('firstName')?.dirty) ||
                        infoForm.get('firstName')?.touched ||
                        submitInfoFailed
                      "
                    >
                      <small
                        *ngIf="infoForm.get('firstName')?.hasError('required')"
                        class="text-red-400"
                        >Vui lòng nhập Tên người dùng</small
                      >
                    </div>
                  </div>
                  <div class="col-span-6 sm:col-span-3">
                    <label
                      for="last-name"
                      class="block mb-2 font-medium text-gray-900 dark:text-white"
                    >
                      <div class="flex items-center gap-1">
                        <svg
                          class="h-6 w-6"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          stroke-width="2"
                          stroke="currentColor"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path stroke="none" d="M0 0h24v24H0z" />
                          <circle cx="12" cy="7" r="4" />
                          <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                        </svg>
                        <span>Họ</span>
                      </div>
                    </label>
                    <input
                      type="text"
                      name="last-name"
                      id="last-name"
                      class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 placeholder-gray-200 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                      placeholder="{{ user.lastName }}"
                      required=""
                      [defaultValue]="user.lastName"
                      formControlName="lastName"
                    />
                    <div
                      *ngIf="
                        isControlInvalid('lastName', infoForm) ||
                        submitInfoFailed
                      "
                    >
                      <small
                        *ngIf="infoForm.get('lastName')?.hasError('required')"
                        class="text-red-400"
                        >Vui lòng nhập Họ người dùng
                      </small>
                    </div>
                  </div>

                  <div class="col-span-6 sm:col-span-3">
                    <label
                      for="email"
                      class="block mb-2 font-medium text-gray-900 dark:text-white"
                    >
                      <div class="flex items-center gap-1">
                        <svg
                          class="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>

                        <span>Email</span>
                      </div>
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                      placeholder="<EMAIL>"
                      required=""
                      [value]="user.email"
                      formControlName="email"
                    />
                    <div
                      *ngIf="
                        isControlInvalid('email', infoForm) || submitInfoFailed
                      "
                    >
                      <small
                        *ngIf="infoForm.get('email')?.hasError('required')"
                        class="text-red-400"
                        >Vui lòng nhập email người dùng
                      </small>
                      <small
                        *ngIf="infoForm.get('email')?.hasError('email')"
                        class="text-red-400"
                        >Email không chính xác
                      </small>
                    </div>
                  </div>

                  <div class="col-span-6 sm:col-span-3">
                    <label
                      for="birthday"
                      class="block mb-2 font-medium text-gray-900 dark:text-white"
                    >
                      <div class="flex items-center gap-1">
                        <svg
                          class="h-6 w-6"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          stroke-width="2"
                          stroke="currentColor"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path stroke="none" d="M0 0h24v24H0z" />
                          <rect x="4" y="5" width="16" height="16" rx="2" />
                          <line x1="16" y1="3" x2="16" y2="7" />
                          <line x1="8" y1="3" x2="8" y2="7" />
                          <line x1="4" y1="11" x2="20" y2="11" />
                          <rect x="8" y="15" width="2" height="2" />
                        </svg>

                        <span>Ngày sinh</span>
                      </div>
                    </label>
                    <input
                      type="date"
                      name="birthday"
                      id="birthday"
                      class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                      placeholder="15/08/1990"
                      required=""
                      [value]="user.dob"
                      formControlName="dob"
                    />
                    <div
                      *ngIf="
                        isControlInvalid('dob', infoForm) || submitInfoFailed
                      "
                    >
                      <small
                        *ngIf="infoForm.get('dob')?.hasError('required')"
                        class="text-red-400"
                        >Vui lòng nhập năm sinh
                      </small>
                    </div>
                  </div>

                  <div class="col-span-6 sm:col-full">
                    <button
                      class="bg-primary-100 text-white hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                      type="submit"
                    >
                      Lưu
                    </button>
                  </div>
                </div>
              </form>
            </div>
            <div
              class="p-4 mb-4 border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-neutral-700 sm:p-6"
            >
              <h3 class="mb-4 text-xl font-semibold dark:text-white">
                Đổi mật khẩu
              </h3>
              <form [formGroup]="passwordForm" (ngSubmit)="onUpdatePassword()">
                <div class="grid grid-cols-6 gap-6">
                  <div class="col-span-6 sm:col-span-3">
                    <label
                      for="current-password"
                      class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >Mật khẩu hiện tại</label
                    >
                    <div class="mt-1 relative">
                      <input
                        [type]="showPassword ? 'text' : 'password'"
                        name="current-password"
                        id="current-password"
                        class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                        placeholder="••••••••"
                        required=""
                        formControlName="oldPassword"
                      />
                      <app-eye-icon
                        class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500"
                        [show]="showPassword"
                        (click)="showPassword = !showPassword"
                      ></app-eye-icon>
                    </div>
                    <div
                      *ngIf="
                        isControlInvalid('oldPassword', passwordForm) ||
                        submitFailed
                      "
                    >
                      <small
                        *ngIf="
                          passwordForm.get('oldPassword')?.hasError('required')
                        "
                        class="text-red-400"
                        >Vui lòng nhập mật khẩu hiện tại</small
                      >
                    </div>
                  </div>
                  <div class="col-span-6 sm:col-span-3">
                    <label
                      for="password"
                      class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >Mật khẩu mới</label
                    >
                    <div class="mt-1 relative">
                      <input
                        data-popover-target="popover-password"
                        data-popover-placement="bottom"
                        [type]="showNewPassword ? 'text' : 'password'"
                        id="password"
                        class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-100 focus:border-primary-100 block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-100 dark:focus:border-primary-100"
                        placeholder="••••••••"
                        required=""
                        formControlName="newPassword"
                      />
                      <app-eye-icon
                        class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500"
                        [show]="showNewPassword"
                        (click)="showNewPassword = !showNewPassword"
                      ></app-eye-icon>
                    </div>
                    <div
                      *ngIf="
                        isControlInvalid('newPassword', passwordForm) ||
                        submitFailed
                      "
                    >
                      <small
                        *ngIf="
                          passwordForm.get('newPassword')?.hasError('required')
                        "
                        class="text-red-400"
                        >Vui lòng nhập mật khẩu mới</small
                      >
                    </div>
                    <div
                      data-popover=""
                      id="popover-password"
                      role="tooltip"
                      class="absolute z-10 invisible inline-block text-sm text-gray-500 transition-opacity duration-300 border border-gray-200 rounded-lg shadow-sm opacity-0 w-72 dark:border-neutral-500 dark:text-gray-400"
                      style="
                        position: absolute;
                        inset: auto auto 0px 0px;
                        margin: 0px;
                        transform: translate3d(510.4px, -1755.2px, 0px);
                      "
                      data-popper-placement="top"
                      data-popper-reference-hidden=""
                      data-popper-escaped=""
                    >
                      <div class="p-3 space-y-2">
                        <h3 class="font-semibold text-gray-900 dark:text-white">
                          Must have at least 6 characters
                        </h3>
                        <div class="grid grid-cols-4 gap-2">
                          <div
                            class="h-1 bg-orange-300 dark:bg-orange-400"
                          ></div>
                          <div
                            class="h-1 bg-orange-300 dark:bg-orange-400"
                          ></div>
                          <div class="h-1 bg-gray-200 dark:bg-gray-600"></div>
                          <div class="h-1 bg-gray-200 dark:bg-gray-600"></div>
                        </div>
                        <p>It’s better to have:</p>
                        <ul>
                          <li class="flex items-center mb-1">
                            <svg
                              class="w-4 h-4 mr-2 text-green-400 dark:text-green-500"
                              aria-hidden="true"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"
                              ></path>
                            </svg>
                            Upper &amp; lower case letters
                          </li>
                          <li class="flex items-center mb-1">
                            <svg
                              class="w-4 h-4 mr-2 text-gray-300 dark:text-gray-400"
                              aria-hidden="true"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fill-rule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clip-rule="evenodd"
                              ></path>
                            </svg>
                            A symbol (#$&amp;)
                          </li>
                          <li class="flex items-center">
                            <svg
                              class="w-4 h-4 mr-2 text-gray-300 dark:text-gray-400"
                              aria-hidden="true"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fill-rule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clip-rule="evenodd"
                              ></path>
                            </svg>
                            A longer password (min. 12 chars.)
                          </li>
                        </ul>
                      </div>
                      <div
                        data-popper-arrow=""
                        style="
                          position: absolute;
                          left: 0px;
                          transform: translate3d(139.2px, 0px, 0px);
                        "
                      ></div>
                    </div>
                  </div>
                  <div class="col-span-6 sm:col-span-3">
                    <label
                      for="confirm-password"
                      class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >Nhập lại mật khẩu mới</label
                    >
                    <div class="mt-1 relative">
                      <input
                        [type]="showRePassword ? 'text' : 'password'"
                        name="confirm-password"
                        id="confirm-password"
                        class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                        placeholder="••••••••"
                        required=""
                        formControlName="rePassword"
                      />
                      <app-eye-icon
                        [show]="showRePassword"
                        (click)="showRePassword = !showRePassword"
                        class="absolute inset-y-0 right-0 flex items-center px-3 text-sm font-medium text-gray-700"
                      ></app-eye-icon>
                    </div>
                    <div
                      *ngIf="
                        isControlInvalid('rePassword', passwordForm) ||
                        submitFailed
                      "
                    >
                      <small
                        *ngIf="
                          passwordForm.get('rePassword')?.hasError('required')
                        "
                        class="text-red-400"
                        >Vui lòng nhập lại mật khẩu</small
                      >
                    </div>
                  </div>
                  <div class="col-span-6 sm:col-full">
                    <button
                      class="bg-primary-100 text-white hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                      type="submit"
                    >
                      Lưu
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
