import { Component, Inject, OnInit, PLATFORM_ID, ChangeDetectionStrategy, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import {
  IFilters,
  advancedFiltersOptions,
} from '../../components/utils/constants';

import { isPlatformServer } from '@angular/common';
import { Comic, ComicStatus, Genre, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import { IOption } from 'src/app/dataSource/schema/IOption';

@Component({
    selector: 'app-search-page',
    templateUrl: './search-page.component.html',
    styleUrl: './search-page.component.scss',
    standalone: false,
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SearchPageComponent implements OnInit, OnDestroy {
  listComics: Comic[] = [];
  totalpage!: number;
  totalResult!: number;
  currentPage = 1;
  dataView: IFilters;
  listGenres: Genre[] = [];
  showFilters = false;
  nowyear!: number;
  isLoad = false;
  private isOnInit = false;
  filterTags: any[] = [];
  isLoading = false;
  isSearching = false;
  lastupdate!: SortType.LastUpdate;

  // Performance optimizations
  private destroy$ = new Subject<void>();
  private searchSubject = new Subject<string>();

  selectOptions: any = {
    sorts: { value: -1, label: '', isShow: false },
    status: { value: -1, label: '', isShow: false },
    year: { value: 0, label: '', isShow: false },
    genres: { value: {}, label: {}, isShow: false },
    keyword: { value: '', label: '' },
  };

  step = 30;
  nPreview = 30;

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    private cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: object,
  ) {
      this.seoService.setTitle('Tìm kiếm truyện tranh');
      this.seoService.addTags([
        { name: 'description', content: 'Mọi truyện tranh đều có thể tìm thấy tại Metruyenmoi' },
        { property: 'og:description', content: 'Mọi truyện tranh đều có thể tìm thấy tại Metruyenmoi' },
        { property: 'og:title', content: 'Tìm kiếm truyện tranh' },
      ]);
    this.seoService.updateLink('canonical', `${this.urlService.BASE_URL}/tim-truyen`);


      this.dataView = {
        status: advancedFiltersOptions.status,
        sorts: advancedFiltersOptions.sorts,
      };
      this.selectOptions.status.label = advancedFiltersOptions.status[0].label;
      this.selectOptions.sorts.label = advancedFiltersOptions.sorts[0].label;
      this.lastupdate = SortType.LastUpdate;
      this.isLoading = true;
  }
  ssr() {
    return isPlatformServer(this.platformId);
  }

  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  ngOnInit(): void {
    if (this.ssr()) return;

    this.comicService.getGenres().subscribe((genres) => {
      this.listGenres = genres;
    });
    this.route.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe((params) => {
      const page = +params['page'] || 1;
      const status = +params['status'] || ComicStatus.ALL;
      const sort = +params['sort'] >= 0 ? +params['sort'] : SortType.LastUpdate;
      const genres = params['genres'] || '';
      const nogenres = params['nogenres'] || '';
      const year = +params['year'] || 0;
      const keyword = params['keyword'] || '';
      this.currentPage = page;

      this.resetOptions(status, sort, year, genres, nogenres, keyword);
      this.searchComics(page, sort, status, genres, nogenres, year, keyword);
    });

    // Setup debounced search
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe((keyword) => {
      this.selectOptions.keyword.value = keyword;
      this.performSearch();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Performance optimized methods
  trackByComicId = (index: number, comic: Comic): number => {
    return comic?.id ?? index;
  };

  trackByIndex = (index: number): number => index;

  onSearchInput(keyword: string): void {
    this.searchSubject.next(keyword);
  }

  private performSearch(): void {
    if (!this.isLoad) return;
    this.ClickSearch();
  }

  getGenreKeys() {
    return Object.keys(this.selectOptions.genres.value).filter(
      (key) => this.selectOptions.genres.value[key] > 0,
    );
  }
  private resetOptions(
    status: number,
    sort: number,
    year: number,
    genres: string,
    nogenres: string,
    keyword: string,
  ) {
    if (this.isOnInit) return;

    this.selectOptions.keyword.value = keyword;

    genres
      .split(',')
      .filter((x) => x != '')
      .forEach((id) => {
        this.selectOptions.genres.value[id] = 1;
        this.selectOptions.genres.label[id] = this.listGenres.find(
          (x) => x.id == parseInt(id),
        )?.title;
      });
    nogenres
      .split(',')
      .filter((x) => x != '')
      .forEach((id) => {
        this.selectOptions.genres.value[id] = 2;
        this.selectOptions.genres.label[id] = this.listGenres.find(
          (x) => x.id == parseInt(id),
        )?.title;
      });

    this.selectOptions.year.value = year;
    this.selectOptions.year.label = year.toString();

    this.dataView.status.forEach((filter) => {
      filter.selected = filter.value === status;
      if (filter.selected) {
        this.selectOptions.status.label = filter.label;
        this.selectOptions.status.value = filter.value;
      }
    });

    this.dataView.sorts.forEach((filter) => {
      filter.selected = filter.value === sort;
      if (filter.selected) {
        this.selectOptions.sorts.label = filter.label;
        this.selectOptions.sorts.value = filter.value;
      }
    });

    this.isOnInit = true;
  }

  private searchComics(
    page: number,
    sort: number,
    status: number,
    genres: string,
    nogenres: string,
    year: number,
    keyword: string,
  ) {
    this.listComics = [];
    this.isSearching = true;
    this.comicService
      .getAdvanceSearchComic(
        page,
        this.step,
        sort,
        status,
        genres,
        nogenres,
        year,
        keyword,
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe((res: any) => {
        this.totalpage = res.data.totalpage;
        this.listComics = res.data.comics;
        this.isLoading = false;
        this.isSearching = false;
        this.nPreview = this.listComics.length;
        this.cdr.detectChanges();
      });
  }

  OnFilterChange({ option, data }: { option: string; data: IOption }) {
    if (data.selected) return;

    this.isLoad = true;
    this.dataView[option as keyof IFilters].forEach(
      (filter) => (filter.selected = false),
    );
    data.selected = true;

    this.selectOptions[option as keyof IFilters].label = data.label;
    this.selectOptions[option as keyof IFilters].value = data.value;
    this.selectOptions[option as keyof IFilters].isShow = false;
  }
  OnFilterChange1(key:string, value:any) {
    this.isLoad = true;
    this.selectOptions[key].value = value;
    if(key === 'status'){
      this.selectOptions.status.label = this.dataView.status.find((x) => x.value === value)?.label;
    }
    else if(key === 'sorts'){
      this.selectOptions.sorts.label = this.dataView.sorts.find((x) => x.value === value)?.label;
    }

  }

  OnYearChange(year: number) {
    this.isLoad = true;
    this.selectOptions.year.isShow = true;
    this.selectOptions.genres.isShow = false;
    this.selectOptions.year.value = this.selectOptions.year.value + year;
    this.selectOptions.year.label = year.toString();
  }
  OnGenresChange(genre: Genre) {
    this.isLoad = true;
    const genreValue = this.selectOptions.genres.value[genre.id];
    this.selectOptions.genres.value[genre.id] = (genreValue + 1 || 1) % 3;
    this.selectOptions.genres.label[genre.id] = genre.title;
  }

  private updateGenres(): Promise<{ genres: string; nogenres: string }> {
    return new Promise((resolve) => {
      const genres: string[] = [];
      const nogenres: string[] = [];

      Object.entries(this.selectOptions.genres.value).forEach(
        ([key, value]) => {
          if (value === 1) genres.push(key);
          if (value === 2) nogenres.push(key);
        },
      );

      resolve({
        genres: genres.join(','),
        nogenres: nogenres.join(','),
      });
    });
  }

  ClickSearch() {
    if (!this.isLoad) return;
    this.updateGenres().then(({ genres, nogenres }) => {
      this.isLoad = false;
      this.router.navigate([], {
        queryParams: {
          page: 1,
          status: this.selectOptions.status.value,
          sort: this.selectOptions.sorts.value,
          year: this.selectOptions.year.value,
          genres,
          nogenres,
          keyword: this.selectOptions.keyword.value,
        },
        queryParamsHandling: 'merge',
        fragment: 'listComic',
      });
    });
  }

  RemoveFilterTag({ option, data }: { option: string; data: any }) {
    this.isLoad = true;
    switch (option) {
      case 'status':
        this.selectOptions.status.value = -1;
        this.selectOptions.status.label = this.dataView.status[0].label;
        break;
      case 'sort':
        this.selectOptions.sorts.value = this.lastupdate;
        this.selectOptions.sorts.label = this.dataView.sorts[0].label;
        break;
      case 'year':
        this.selectOptions.year.value = 0;
        break;
      case 'genres':
        this.selectOptions.genres.value[data] = 0;
        break;
    }
  }

  getJoinedGenreNames(): string {
    return this.getGenreKeys()
      .map((key) => this.selectOptions.genres.label[key])
      .join(', ');
  }

  clearAllFilter() {
    this.isLoad = true;
    this.dataView.status.map(
      (d: any, index: number) => (d.selected = index === 0),
    );
    this.dataView.sorts.map(
      (d: any, index: number) => (d.selected = index === 0),
    );

    this.selectOptions = {
      sorts: {
        value: SortType.LastUpdate,
        label: advancedFiltersOptions.sorts[0].label,
      },
      status: {
        value: ComicStatus.ALL,
        label: advancedFiltersOptions.status[0].label,
      },
      year: { value: 0, label: '', isShow: false },
      genres: { value: {}, label: {}, isShow: false },
      keyword: { value: '', label: '' },
    };
  }
  saveFilters() {
    return;
  }
  OnChangePage(page: number) {
    this.router.navigate([], {
      queryParams: { page },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }
}
