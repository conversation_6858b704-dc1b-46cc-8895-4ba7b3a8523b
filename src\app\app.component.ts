import { isPlatformBrowser, isPlatformServer, ViewportScroller } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, PLATFORM_ID, ViewContainerRef } from '@angular/core';
import {
  Router,
  Scroll,
} from '@angular/router';
import { PopupService } from '@services/popup.service';
import { ToastService } from '@services/toast.service';
import { filter } from 'rxjs';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent {
  get isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  constructor(
    private router: Router,
    private viewportScroller: ViewportScroller,
    private popupService: PopupService,
    private toastService: ToastService,
    private vcRef: ViewContainerRef,
    private cd: ChangeDetectorRef,  
    @Inject(PLATFORM_ID) private platformId: object,
  ) {
    if (isPlatformServer(this.platformId)) return;
    this.popupService.viewContainerRef = this.vcRef;
    this.toastService.viewContainerRef = this.vcRef;
    this.router.events
      .pipe(filter((e) => e instanceof Scroll))
      .subscribe((e: any) => {
        if (e.position) {
          // backward navigation
          window.scrollTo({
            top: e.position[1],
            left: e.position[0],
            behavior: 'instant',
          }); // scrollToPosition(e.position);
        } else if (e.anchor) {
          viewportScroller.scrollToAnchor(e.anchor);
        } else {
          window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
        }
      });

  }
  // @HostListener('window:error', ['$event'])
  // onWindowError(event: any) {

  // }
  // ngOnInit(): void {
  //   // this.popupService.viewContainerRef = this.vcRef;
  // }
  // ngAfterViewInit(): void {
  //   // this.popupService.viewContainerRef = this.vcRef;
  // }
  // ngOnDestroy(): void {
  //   // this.popupService.viewContainerRef = undefined;

  // }
}
