<!-- Modern Announcement Component -->
<div class="announcements-container mt-2">
  <div *ngFor="let noti of anouncements; trackBy: trackByAnnouncement" class="announcement-card">

    <div class="announcement-icon-wrapper">
        <div class="announcement-icon-bg">
          <svg class="announcement-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M11 5L6 9H2v6h4l5 4V5z"/>
            <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
          </svg>
        </div>
        <div class="announcement-pulse"></div>
        <div class="announcement-content">
          <div class="announcement-text" [innerHTML]="noti.content"></div>
        </div>
      </div>

    <!-- Announcement Footer -->
    <!-- <div class="announcement-footer">
      <div class="announcement-meta">
        <svg class="announcement-meta-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10"/>
          <polyline points="12,6 12,12 16,14"/>
        </svg>
        <span class="announcement-time">Vừa xong</span>
      </div>

      <div class="announcement-priority">
        <span class="priority-indicator priority-high"></span>
        <span class="priority-text">Quan trọng</span>
      </div>
    </div> -->
  </div>

  <!-- Empty State -->
  <div *ngIf="!anouncements || anouncements.length === 0" class="announcements-empty">
    <div class="empty-icon">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M11 5L6 9H2v6h4l5 4V5z"/>
        <path d="M23 9l-2 2-2-2"/>
      </svg>
    </div>
    <h3 class="empty-title">Không có thông báo</h3>
    <p class="empty-subtitle">Hiện tại chưa có thông báo nào mới</p>
  </div>
</div>
