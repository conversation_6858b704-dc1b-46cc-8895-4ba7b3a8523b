<div *ngFor="let noti of anouncements" class="anouncement w-full">
  <div class="mx-1 flex items-center">
    <svg
      fill="currentColor"
      class="mr-0.5 size-4 text-primary-100 shrink-0 animate-ping"
      viewBox="0 0 518.518 518.518"
      xml:space="preserve"
    >
      <g>
        <g>
          <path
            d="M298.043,49.725L106.029,186.812h-74.97C14,186.812,0,200.812,0,217.871v82.773c0,17.06,14,31.06,31.059,31.06h74.97    l192.014,137.088c14,10.021,25.17,4.131,25.17-12.929V62.653C323.213,45.594,312.043,39.703,298.043,49.725z"
          />
          <path
            d="M402.16,141.448c-14.305,0-25.857,11.551-25.857,25.856s11.553,25.857,25.857,25.857    c35.648,0,64.643,28.993,64.643,64.643c0,35.648-28.994,64.643-64.643,64.643c-14.305,0-25.857,11.552-25.857,25.856    c0,14.306,11.553,25.857,25.857,25.857c64.336,0,116.357-52.097,116.357-116.356C518.518,193.697,466.496,141.448,402.16,141.448z    "
          />
        </g>
      </g>
    </svg>
    <svg
      fill="currentColor"
      class="absolute size-5 text-primary-100 shrink-0"
      viewBox="0 0 518.518 518.518"
      xml:space="preserve"
    >
      <g>
        <g>
          <path
            d="M298.043,49.725L106.029,186.812h-74.97C14,186.812,0,200.812,0,217.871v82.773c0,17.06,14,31.06,31.059,31.06h74.97    l192.014,137.088c14,10.021,25.17,4.131,25.17-12.929V62.653C323.213,45.594,312.043,39.703,298.043,49.725z"
          />
          <path
            d="M402.16,141.448c-14.305,0-25.857,11.551-25.857,25.856s11.553,25.857,25.857,25.857    c35.648,0,64.643,28.993,64.643,64.643c0,35.648-28.994,64.643-64.643,64.643c-14.305,0-25.857,11.552-25.857,25.856    c0,14.306,11.553,25.857,25.857,25.857c64.336,0,116.357-52.097,116.357-116.356C518.518,193.697,466.496,141.448,402.16,141.448z    "
          />
        </g>
      </g>
    </svg>
  </div>
  <div class="ml-2 dark:text-white text-sm h-full">
    <p [innerHTML]="noti.content"></p>
  </div>
</div>
