import { Component, ChangeDetectionStrategy, OnInit, ChangeDetectorRef, inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformServer } from '@angular/common';
import { RouterLink } from '@angular/router';
import { Comic } from '@schema';
import { HistoryService } from '@services/history.service';
import { ToastService, ToastType } from '@services/toast.service';
import { PopupService } from '@services/popup.service';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { EmptyComponent } from '@components/common/empty/empty.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { ComicCardComponent } from '@components/common/comic-card/Ver1/comic-card.component';

@Component({
  selector: 'app-reading-history',
  standalone: true,
  imports: [CommonModule, RouterLink, ComicCardComponent, PaginationComponent, EmptyComponent, SpinnerComponent],
  templateUrl: './reading-history.component.html',
  styleUrl: './reading-history.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ReadingHistoryComponent implements OnInit {
  private historyService = inject(HistoryService);
  private toastService = inject(ToastService);
  private popupService = inject(PopupService);
  private cd = inject(ChangeDetectorRef);
  private platformId = inject(PLATFORM_ID);

  comics: Comic[] = [];
  isLoading = true;
  currentPage = 1;
  itemsPerPage = 14;
  totalPages = 1;
  searchQuery = '';
  sortBy: 'recent' | 'title' | 'progress' = 'recent';

  ngOnInit() {
    if (isPlatformServer(this.platformId)) return;
    this.loadHistory();
  }

  private loadHistory() {
    this.isLoading = true;

    const historyData = this.historyService.GetHistorys();

    if (historyData.length === 0) {
      this.comics = [];
      this.isLoading = false;
      this.cd.detectChanges();
      return;
    }

    // Calculate total pages
    this.totalPages = Math.floor((historyData.length - 1) / this.itemsPerPage) + 1;

    // Get paginated data
    const paginatedHistory = historyData.slice(
      (this.currentPage - 1) * this.itemsPerPage,
      this.currentPage * this.itemsPerPage
    );

    // Set comics for display
    this.comics = paginatedHistory;
    this.isLoading = false;
    this.cd.detectChanges();
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.loadHistory();
  }

  onRemoveSelectedComics(ids: number[]) {
    if (!ids.length) return;

    this.popupService.showConfirmPopup({
      title: 'Xóa lịch sử đọc',
      message: this.getRemoveMessage(ids),
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy'
    }).then((result: any) => {
      const { isconfirm } = result;
      if (isconfirm) {
        ids.forEach(id => {
          this.historyService.RemoveHistory(id);
        });

        this.loadHistory();
        this.toastService.show(ToastType.Success, 'Đã xóa lịch sử đọc thành công');
      }
    });
  }

  private getRemoveMessage(ids: number[]): string {
    if (ids.length > 1) {
      return `Bạn có chắc chắn muốn xóa <b>${ids.length}</b> truyện khỏi lịch sử đọc?`;
    }

    const [comicId] = ids;
    const comic = this.comics.find(comic => comic.id === comicId);
    return comic
      ? `Bạn có chắc chắn muốn xóa <b>${comic.title}</b> khỏi lịch sử đọc?`
      : 'Bạn có chắc chắn muốn xóa truyện này khỏi lịch sử đọc?';
  }

  get hasHistory(): boolean {
    return this.historyService.GetHistorySize() > 0;
  }

  ssr(): boolean {
    return isPlatformServer(this.platformId);
  }
}
