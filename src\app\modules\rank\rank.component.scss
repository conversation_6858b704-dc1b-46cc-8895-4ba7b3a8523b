// Subtle animations for ranking
@keyframes gentlePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes subtleFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Main ranking container - lighter design
.ranking-container {
  @apply bg-white dark:bg-neutral-900;
  @apply border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm p-6;
}

// Simplified ranking header
.ranking-header {
  @apply mb-6 flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4;
}

.ranking-title {
  @apply text-2xl font-bold text-gray-800 dark:text-white;
  @apply flex items-center gap-2;

  &::before {
    content: '🏆';
    @apply text-2xl opacity-80;
    animation: gentlePulse 3s infinite;
  }
}

// Simplified filter controls
.ranking-filters {
  @apply flex flex-wrap gap-3 items-center;
}

.filter-group {
  @apply flex flex-col gap-1;
}

.filter-label {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.filter-select {
  @apply bg-white dark:bg-neutral-800 border border-gray-300 dark:border-gray-600;
  @apply rounded-lg px-3 py-2 text-sm text-gray-700 dark:text-gray-300;
  @apply focus:border-primary-100 focus:ring-1 focus:ring-primary-100/30;
  @apply transition-colors duration-200;

  &:hover {
    @apply border-gray-400 dark:border-gray-500;
  }
}

// Simplified top 3 podium styling
.podium-container {
  @apply relative mb-8 p-5 bg-gray-50 dark:bg-neutral-800;
  @apply rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm;
}

.podium-grid {
  @apply grid grid-cols-3 gap-4 items-end;
}

.podium-item {
  @apply relative flex flex-col items-center;

  &.first-place {
    @apply order-2;

    .podium-card {
      @apply bg-white dark:bg-neutral-700 border-2 border-primary-100;
      @apply shadow-md;
      transform: scale(1.05);
    }

    .rank-badge {
      @apply bg-primary-100 text-white;
      @apply w-10 h-10 text-lg;
    }
  }

  &.second-place {
    @apply order-1;

    .podium-card {
      @apply bg-white dark:bg-neutral-700 border border-gray-300 dark:border-gray-600;
      @apply shadow-sm;
    }

    .rank-badge {
      @apply bg-gray-400 text-white;
      @apply w-9 h-9 text-base;
    }
  }

  &.third-place {
    @apply order-3;

    .podium-card {
      @apply bg-white dark:bg-neutral-700 border border-gray-300 dark:border-gray-600;
      @apply shadow-sm;
    }

    .rank-badge {
      @apply bg-orange-400 text-white;
      @apply w-9 h-9 text-base;
    }
  }
}

.podium-card {
  @apply relative p-4 rounded-xl transition-all duration-200;
  @apply cursor-pointer;

  &:hover {
    transform: translateY(-2px);
    @apply shadow-lg;
  }
}

.rank-badge {
  @apply absolute -top-3 left-1/2 transform -translate-x-1/2;
  @apply rounded-full font-semibold shadow-sm;
  @apply flex items-center justify-center;
  @apply border border-white dark:border-gray-800;
}

.comic-cover {
  @apply w-full h-32 object-cover rounded-lg shadow-md mb-3;
  @apply transition-transform duration-300;

  &:hover {
    transform: scale(1.05);
  }
}

.comic-title {
  @apply text-sm font-bold text-center mb-1 line-clamp-2;
  @apply text-gray-800 dark:text-white;
}

.comic-author {
  @apply text-xs text-gray-500 dark:text-gray-400 text-center mb-2;
}

.comic-stats {
  @apply flex items-center justify-center gap-1 text-xs;
  @apply text-primary-100 font-semibold;
}

// Simplified selected comic details
.selected-comic-details {
  @apply mt-6 p-5 bg-gray-50 dark:bg-neutral-800;
  @apply rounded-xl border border-gray-200 dark:border-gray-700;
  @apply shadow-sm;
}

.comic-stats-row {
  @apply flex justify-center gap-6 mb-4;
}

.stat-item {
  @apply flex items-center gap-2 text-sm;
  @apply bg-white dark:bg-neutral-800 px-3 py-2 rounded-full;
  @apply border border-gray-200 dark:border-gray-600 shadow-sm;
}

.genre-tags {
  @apply flex justify-center gap-2 mb-4 flex-wrap;
}

.genre-tag {
  @apply px-3 py-1 rounded-full text-xs font-semibold;
  @apply transition-all duration-200 cursor-pointer;

  &.primary {
    @apply bg-primary-100 text-white shadow-md;

    &:hover {
      @apply bg-primary-200 transform scale-105;
    }
  }

  &.secondary {
    @apply bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200;

    &:hover {
      @apply bg-gray-300 dark:bg-gray-600 transform scale-105;
    }
  }
}

.comic-description {
  @apply text-sm text-center text-gray-700 dark:text-gray-300 leading-relaxed;
}

// Ranking list section
.ranking-list-section {
  @apply mt-8;
}

.ranking-list-header {
  @apply flex items-center gap-3 mb-6 pb-3 border-b-2 border-primary-100;
}

.ranking-list-title {
  @apply text-2xl font-bold text-gray-800 dark:text-white;
}

.ranking-list-icon {
  @apply w-8 h-8 text-primary-100;
}

// Enhanced grid comic styling
.ranking-grid {
  @apply grid gap-4 grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7;
  @apply mx-3 lg:mx-0;
}

.ranking-comic-card {
  @apply relative bg-white dark:bg-neutral-800 rounded-xl shadow-sm overflow-hidden;
  @apply border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-200 cursor-pointer;

  &:hover {
    @apply shadow-md border-gray-300 dark:border-gray-600;
    transform: translateY(-1px);

    .comic-cover-image {
      transform: scale(1.02);
    }
  }
}

.comic-rank-badge {
  @apply absolute top-2 left-2 z-10;
  @apply bg-primary-100 text-white;
  @apply w-7 h-7 rounded-full flex items-center justify-center;
  @apply text-xs font-semibold shadow-sm;
}

.comic-cover-container {
  @apply relative overflow-hidden;
  padding-bottom: 140%; // Aspect ratio for comic covers
}

.comic-cover-image {
  @apply absolute inset-0 w-full h-full object-cover;
  @apply transition-transform duration-300;
}

.comic-info-overlay {
  @apply absolute bottom-0 left-0 right-0;
  @apply bg-gradient-to-t from-black/80 via-black/40 to-transparent;
  @apply p-3 text-white;
  @apply transform translate-y-full group-hover:translate-y-0;
  @apply transition-transform duration-300;
}

.comic-overlay-title {
  @apply text-sm font-bold line-clamp-2 mb-1;
}

.comic-overlay-stats {
  @apply flex items-center gap-2 text-xs;
}

// Loading states
.ranking-loading {
  @apply flex items-center justify-center py-12;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-primary-100 border-t-transparent rounded-full animate-spin;
}

// Empty state
.ranking-empty {
  @apply text-center py-12;
}

.empty-icon {
  @apply w-16 h-16 mx-auto mb-4 text-gray-400;
}

.empty-title {
  @apply text-xl font-bold text-gray-600 dark:text-gray-400 mb-2;
}

.empty-description {
  @apply text-gray-500 dark:text-gray-500;
}

// Pagination styling
.ranking-pagination {
  @apply mt-8 flex justify-center;
}

// Responsive design improvements
@media (max-width: 768px) {
  .ranking-title {
    @apply text-2xl;

    &::before {
      @apply text-3xl;
    }
  }

  .ranking-filters {
    @apply flex-col items-stretch;
  }

  .filter-group {
    @apply w-full;
  }

  .filter-select {
    @apply w-full;
  }

  .podium-grid {
    @apply grid-cols-1 gap-6;
  }

  .podium-item {
    &.first-place,
    &.second-place,
    &.third-place {
      @apply order-none;
    }

    &.first-place .podium-card {
      transform: none;
    }
  }

  .comic-stats-row {
    @apply flex-col gap-3;
  }

  .genre-tags {
    @apply gap-1;
  }

  .ranking-grid {
    @apply grid-cols-2 gap-3;
  }
}

@media (max-width: 480px) {
  .ranking-container {
    @apply p-4;
  }

  .podium-container {
    @apply p-4;
  }

  .selected-comic-details {
    @apply p-4;
  }

  .ranking-title {
    @apply text-xl;
  }
}

// Custom active state for selections
.custom-active {
  @apply bg-primary-100 text-white outline-none shadow-lg;
  transform: translateY(-1px);
}

// Enhanced hover effects for interactive elements
.interactive-element {
  @apply transition-all duration-200;

  &:hover {
    @apply transform scale-105;
  }

  &:active {
    @apply transform scale-95;
  }
}

// Comic theme decorative elements
.ranking-decoration {
  @apply absolute top-4 right-4 opacity-10 pointer-events-none;

  &.star {
    @apply text-yellow-400;
    animation: rankingPulse 3s infinite;
  }

  &.trophy {
    @apply text-primary-100;
    animation: trophyShine 4s infinite;
  }
}

