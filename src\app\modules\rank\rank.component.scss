// Comic-themed animations for ranking
@keyframes rankingPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(232, 58, 58, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 10px rgba(232, 58, 58, 0.1);
  }
}

@keyframes trophyShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes cardHover {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-8px) scale(1.02);
  }
}

@keyframes badgeGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(232, 58, 58, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(232, 58, 58, 0.6);
  }
}

// Main ranking container
.ranking-container {
  @apply bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-700;
  @apply border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-xl p-6;
}

// Enhanced ranking header
.ranking-header {
  @apply mb-8 flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4;
}

.ranking-title {
  @apply text-3xl font-bold bg-gradient-to-r from-primary-100 to-primary-200 bg-clip-text text-transparent;
  @apply flex items-center gap-3;

  &::before {
    content: '🏆';
    @apply text-4xl text-primary-200;
    animation: rankingPulse 2s infinite;
  }
}

// Enhanced filter controls
.ranking-filters {
  @apply flex flex-wrap gap-3 items-center;
}

.filter-group {
  @apply flex flex-col gap-2;
}

.filter-label {
  @apply text-sm font-semibold text-gray-700 dark:text-gray-300;
}

.filter-select {
  @apply bg-white dark:bg-neutral-700 border-2 border-gray-200 dark:border-gray-600;
  @apply rounded-xl px-4 py-2 font-medium text-gray-800 dark:text-white;
  @apply focus:border-primary-100 focus:ring-2 focus:ring-primary-100/20;
  @apply transition-all duration-200 shadow-sm hover:shadow-md;

  &:focus {
    transform: translateY(-1px);
  }
}

// Top 3 podium styling
.podium-container {
  @apply relative mb-12 p-6 bg-gradient-to-r from-yellow-50 via-orange-50 to-red-50;
  @apply dark:from-yellow-900/20 dark:via-orange-900/20 dark:to-red-900/20;
  @apply rounded-2xl border-2 border-yellow-200 dark:border-yellow-700 shadow-lg;
}

.podium-grid {
  @apply grid grid-cols-3 gap-4 items-end;
}

.podium-item {
  @apply relative flex flex-col items-center;

  &.first-place {
    @apply order-2;

    .podium-card {
      @apply bg-gradient-to-br from-yellow-100 to-yellow-200 dark:from-yellow-800 dark:to-yellow-900;
      @apply border-yellow-400 dark:border-yellow-600;
      @apply shadow-2xl;
      transform: scale(1.1);

      &:hover {
        animation: cardHover 0.3s ease-out forwards;
      }
    }

    .rank-badge {
      @apply bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900;
      @apply w-12 h-12 text-xl;
      animation: badgeGlow 2s infinite;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: trophyShine 2s infinite;
      }
    }
  }

  &.second-place {
    @apply order-1;

    .podium-card {
      @apply bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800;
      @apply border-2 border-gray-400 dark:border-gray-600;
    }

    .rank-badge {
      @apply bg-gradient-to-r from-gray-400 to-gray-500 text-gray-900;
      @apply w-10 h-10 text-lg;
    }
  }

  &.third-place {
    @apply order-3;

    .podium-card {
      @apply bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-800 dark:to-orange-900;
      @apply border-2 border-orange-400 dark:border-orange-600;
    }

    .rank-badge {
      @apply bg-gradient-to-r from-orange-400 to-orange-500 text-orange-900;
      @apply w-10 h-10 text-lg;
    }
  }
}

.podium-card {
  @apply relative p-4 rounded-2xl transition-all duration-300;
  @apply hover:shadow-xl cursor-pointer;

  &:hover {
    transform: translateY(-4px);
  }
}

.rank-badge {
  @apply absolute -top-4 left-1/2 transform -translate-x-1/2;
  @apply rounded-full font-bold shadow-lg;
  @apply flex items-center justify-center;
  @apply border-2 border-white dark:border-gray-800;
  position: relative;
  overflow: hidden;
}

.comic-cover {
  @apply w-full h-32 object-cover rounded-lg shadow-md mb-3;
  @apply transition-transform duration-300;

  &:hover {
    transform: scale(1.05);
  }
}

.comic-title {
  @apply text-sm font-bold text-center mb-1 line-clamp-2;
  @apply text-gray-800 dark:text-white;
}

.comic-author {
  @apply text-xs text-gray-500 dark:text-gray-400 text-center mb-2;
}

.comic-stats {
  @apply flex items-center justify-center gap-1 text-xs;
  @apply text-primary-100 font-semibold;
}

// Selected comic details
.selected-comic-details {
  @apply mt-6 p-6 bg-gradient-to-r from-primary-50 to-primary-100/10;
  @apply rounded-2xl border border-primary-200;
  @apply shadow-lg;
}

.comic-stats-row {
  @apply flex justify-center gap-6 mb-4;
}

.stat-item {
  @apply flex items-center gap-2 text-sm;
  @apply bg-white dark:bg-neutral-800 px-3 py-2 rounded-full;
  @apply border border-gray-200 dark:border-gray-600 shadow-sm;
}

.genre-tags {
  @apply flex justify-center gap-2 mb-4 flex-wrap;
}

.genre-tag {
  @apply px-3 py-1 rounded-full text-xs font-semibold;
  @apply transition-all duration-200 cursor-pointer;

  &.primary {
    @apply bg-primary-100 text-white shadow-md;

    &:hover {
      @apply bg-primary-200 transform scale-105;
    }
  }

  &.secondary {
    @apply bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200;

    &:hover {
      @apply bg-gray-300 dark:bg-gray-600 transform scale-105;
    }
  }
}

.comic-description {
  @apply text-sm text-center text-gray-700 dark:text-gray-300 leading-relaxed;
}

// Ranking list section
.ranking-list-section {
  @apply mt-8;
}

.ranking-list-header {
  @apply flex items-center gap-3 mb-6 pb-3 border-b-2 border-primary-100;
}

.ranking-list-title {
  @apply text-2xl font-bold text-gray-800 dark:text-white;
}

.ranking-list-icon {
  @apply w-8 h-8 text-primary-100;
}

// Enhanced grid comic styling
.ranking-grid {
  @apply grid gap-4 grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7;
  @apply mx-3 lg:mx-0;
}

.ranking-comic-card {
  @apply relative bg-white dark:bg-neutral-800 rounded-2xl shadow-lg overflow-hidden;
  @apply border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2;
  @apply  cursor-pointer;

  &:hover {
    @apply border-primary-100;

    .comic-rank-badge {
      animation: badgeGlow 1s ease-in-out;
    }

    .comic-cover-image {
      transform: scale(1.05);
    }
  }
}

.comic-rank-badge {
  @apply absolute top-2 left-2 z-10;
  @apply bg-gradient-to-r from-primary-100 to-primary-200 text-white;
  @apply w-8 h-8 rounded-full flex items-center justify-center;
  @apply text-sm font-bold shadow-lg;
}

.comic-cover-container {
  @apply relative overflow-hidden;
  padding-bottom: 140%; // Aspect ratio for comic covers
}

.comic-cover-image {
  @apply absolute inset-0 w-full h-full object-cover;
  @apply transition-transform duration-300;
}

.comic-info-overlay {
  @apply absolute bottom-0 left-0 right-0;
  @apply bg-gradient-to-t from-black/80 via-black/40 to-transparent;
  @apply p-3 text-white;
  @apply transform translate-y-full group-hover:translate-y-0;
  @apply transition-transform duration-300;
}

.comic-overlay-title {
  @apply text-sm font-bold line-clamp-2 mb-1;
}

.comic-overlay-stats {
  @apply flex items-center gap-2 text-xs;
}

// Loading states
.ranking-loading {
  @apply flex items-center justify-center py-12;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-primary-100 border-t-transparent rounded-full animate-spin;
}

// Empty state
.ranking-empty {
  @apply text-center py-12;
}

.empty-icon {
  @apply w-16 h-16 mx-auto mb-4 text-gray-400;
}

.empty-title {
  @apply text-xl font-bold text-gray-600 dark:text-gray-400 mb-2;
}

.empty-description {
  @apply text-gray-500 dark:text-gray-500;
}

// Pagination styling
.ranking-pagination {
  @apply mt-8 flex justify-center;
}

// Responsive design improvements
@media (max-width: 768px) {
  .ranking-title {
    @apply text-2xl;

    &::before {
      @apply text-3xl;
    }
  }

  .ranking-filters {
    @apply flex-col items-stretch;
  }

  .filter-group {
    @apply w-full;
  }

  .filter-select {
    @apply w-full;
  }

  .podium-grid {
    @apply grid-cols-1 gap-6;
  }

  .podium-item {
    &.first-place,
    &.second-place,
    &.third-place {
      @apply order-none;
    }

    &.first-place .podium-card {
      transform: none;
    }
  }

  .comic-stats-row {
    @apply flex-col gap-3;
  }

  .genre-tags {
    @apply gap-1;
  }

  .ranking-grid {
    @apply grid-cols-2 gap-3;
  }
}

@media (max-width: 480px) {
  .ranking-container {
    @apply p-4;
  }

  .podium-container {
    @apply p-4;
  }

  .selected-comic-details {
    @apply p-4;
  }

  .ranking-title {
    @apply text-xl;
  }
}

// Custom active state for selections
.custom-active {
  @apply bg-primary-100 text-white outline-none shadow-lg;
  transform: translateY(-1px);
}

// Enhanced hover effects for interactive elements
.interactive-element {
  @apply transition-all duration-200;

  &:hover {
    @apply transform scale-105;
  }

  &:active {
    @apply transform scale-95;
  }
}

// Comic theme decorative elements
.ranking-decoration {
  @apply absolute top-4 right-4 opacity-10 pointer-events-none;

  &.star {
    @apply text-yellow-400;
    animation: rankingPulse 3s infinite;
  }

  &.trophy {
    @apply text-primary-100;
    animation: trophyShine 4s infinite;
  }
}

