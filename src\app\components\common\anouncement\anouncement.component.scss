// Modern Announcement Component - Comic Website Design

// Announcements Container
.announcements-container {
  @apply space-y-4 w-full;
}

// Announcement Card
.announcement-card {
  @apply bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-sm hover:shadow-lg overflow-hidden;
}

.announcement-icon-wrapper {
  @apply relative flex items-center px-1;
}

.announcement-icon-bg {
  @apply flex items-center justify-center w-8 h-8 rounded-full relative z-10;
}

.announcement-icon {
  @apply w-5 h-5 text-primary-100;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.announcement-pulse {
  @apply absolute inset-0 w-10 h-10 bg-primary-100 rounded-full animate-ping opacity-30;
}


// Announcement Content
.announcement-content {
  @apply p-3;
}

.announcement-text {
  @apply text-sm text-gray-700 dark:text-gray-300 leading-relaxed;
}