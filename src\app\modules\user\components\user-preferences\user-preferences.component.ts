import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-user-preferences',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="user-preferences-container">
      <div class="page-header">
        <h2 class="page-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="3"/>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
          </svg>
          Cài đặt
        </h2>
        <p class="page-description">Tùy chỉnh giao diện và thông báo theo ý thích</p>
      </div>

      <div class="content-card">
        <div class="empty-state">
          <div class="empty-illustration">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </div>
          <h3 class="empty-title">Đang phát triển</h3>
          <p class="empty-description">
            Tính năng cài đặt đang được phát triển. Sẽ có nhiều tùy chọn tùy chỉnh!
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .user-preferences-container {
      @apply space-y-6;
    }

    .page-header {
      @apply space-y-2;
    }

    .page-title {
      @apply flex items-center gap-3 text-2xl font-bold text-gray-900 dark:text-white;
    }

    .title-icon {
      @apply w-8 h-8 text-purple-500;
      fill: none;
      stroke: currentColor;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .page-description {
      @apply text-gray-600 dark:text-gray-400;
    }

    .content-card {
      @apply bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm p-12;
      backdrop-filter: blur(20px);
      background: rgba(255, 255, 255, 0.95);
      
      .dark & {
        background: rgba(31, 41, 55, 0.95);
      }
    }

    .empty-state {
      @apply text-center space-y-6;
    }

    .empty-illustration {
      @apply flex justify-center;
    }

    .empty-icon {
      @apply w-24 h-24 text-gray-300 dark:text-gray-600;
      fill: none;
      stroke: currentColor;
      stroke-width: 1;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .empty-title {
      @apply text-xl font-semibold text-gray-900 dark:text-white;
    }

    .empty-description {
      @apply text-gray-600 dark:text-gray-400 max-w-md mx-auto leading-relaxed;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserPreferencesComponent {
}
