<div
  class="md:container flex flex-col w-full bg-gray-100 dark:bg-neutral-800 border dark:border-black dark:text-white px-4 py-5 mt-10 mx-auto rounded-t"
>
  @if (!isLogin) {
  <a class="login-prompt" [routerLink]="['/auth/login']"> <PERSON><PERSON><PERSON> <PERSON>h<PERSON>p để bình luận </a>
  }
  <div class="comments-count-section">
    <div data-comments-count="2810" class="comments-count-container">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 21" fill="none" class="comments-icon">
        <g clip-path="url(#clip0_281_3026)">
          <path
            d="M14.499 0.5H6.50109C3.19363 0.5 0.502686 3.19095 0.502686 6.4984V11.1638C0.502686 14.3596 3.01468 16.9796 6.16784 17.1532V19.9338C6.16784 20.2461 6.42244 20.5 6.73536 20.5C6.88498 20.5 7.02661 20.4407 7.13358 20.3337L7.75875 19.7085C9.40031 18.0666 11.5834 17.1622 13.9054 17.1622H14.499C17.8064 17.1622 20.4974 14.4713 20.4974 11.1638V6.4984C20.4974 3.19095 17.8064 0.5 14.499 0.5ZM6.16784 10.1641C5.4327 10.1641 4.83486 9.56625 4.83486 8.83111C4.83486 8.09597 5.4327 7.49813 6.16784 7.49813C6.90298 7.49813 7.50082 8.09597 7.50082 8.83111C7.50082 9.56625 6.90265 10.1641 6.16784 10.1641ZM10.5 10.1641C9.76488 10.1641 9.16704 9.56625 9.16704 8.83111C9.16704 8.09597 9.76488 7.49813 10.5 7.49813C11.2352 7.49813 11.833 8.09597 11.833 8.83111C11.833 9.56625 11.2348 10.1641 10.5 10.1641ZM14.8322 10.1641C14.0971 10.1641 13.4992 9.56625 13.4992 8.83111C13.4992 8.09597 14.0971 7.49813 14.8322 7.49813C15.5673 7.49813 16.1652 8.09597 16.1652 8.83111C16.1652 9.56625 15.567 10.1641 14.8322 10.1641Z"
            fill="currentColor"
          ></path>
        </g>
      </svg>
      Bình luận (<span class="text-lg">{{ commentsCount }}</span
      >)
    </div>
  </div>
  <div class="flex flex-col gap-2">
    @if (isLogin) {
    <form (submit)="onSubmit(formComment)" [formGroup]="formComment" class="comment-form">
      <div class="comment-form-wrapper">
        <div class="comment-avatar">
          <div class="comment-avatar-icon">
            <img
              loading="lazy"
              download="tai xuong.jpg"
              onerror="this.src='/default_avatar.jpg'"
              [src]="user?.avatar"
              class="comment-avatar-img"
              style="background: url('/effects/than_hoa.webp')"
              alt="avatar"
            />
          </div>
        </div>
        <textarea
          class="comment-textarea"
          name="content"
          placeholder="Viết bình luận..."
          required
          formControlName="content"
          (input)="onInput(formComment)"
        >
        </textarea>
      </div>

      <div class="comment-form-actions">
        <div class="emoji-picker-container" (appClickOutside)="clickOutSite()">
          <svg
            class="emoji-button"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
            (click)="toggleEmojiPicker()"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <circle cx="12" cy="12" r="9" />
            <line x1="9" y1="9" x2="9.01" y2="9" />
            <line x1="15" y1="9" x2="15.01" y2="9" />
            <path d="M8 13a4 4 0 1 0 8 0m0 0H8" />
          </svg>

          <div #emojiPickerContainer class="absolute top-0 -right-52">
            <app-emoji
              *ngIf="activeEmojiPicker"
              (emojiSelect)="addEmoji($event, formComment)"
            ></app-emoji>
          </div>
        </div>
        <button class="submit-button" type="submit">
          Gửi
          <svg
            class="submit-icon"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <line x1="10" y1="14" x2="21" y2="3" />
            <path d="M21 3L14.5 21a.55 .55 0 0 1 -1 0L10 14L3 10.5a.55 .55 0 0 1 0 -1L21 3" />
          </svg>
        </button>
      </div>
    </form>
    }

    <div
      [@fadeInAnimation]="'in'"
      *ngFor="let comment of listComments; trackBy: trackByCommentId"
      class="comment-item"
    >
      <ng-container
        [ngTemplateOutlet]="commentBlockTemplate"
        [ngTemplateOutletContext]="{ comment: comment, hide: false }"
      >
      </ng-container>

      <div
        #ViewReplyEle
        [attr.reply-block]="comment.id"
        class="reply-container  h-0"
        [ngClass]="{
          'overflow-visible': replyId === comment.id && !isViewReply
        }"
      >
        <ng-container
          *ngFor="let reply of comment.replies; trackBy: trackByReplyId"
          [ngTemplateOutlet]="commentBlockTemplate"
          [ngTemplateOutletContext]="{ comment: reply, hide: true }"
        >
        </ng-container>

        @if (isLogin) {
        <form
          (submit)="onSubmit(formReply, comment.id)"
          [formGroup]="formReply"
          *ngIf="replyId === comment.id"
        >
          <div class="reply-form-wrapper">
            <div class="comment-avatar">
              <div class="comment-avatar-icon">
                <img
                  loading="lazy"
                  downloda="tai xuong.jpg"
                  [src]="user?.avatar"
                  onerror="this.src='/default_avatar.jpg'"
                  class="comment-avatar-img"
                  style="background: url('/effects/than_hoa.webp')"
                  alt="avatar"
                />
              </div>
            </div>
            <textarea
              class="reply-textarea"
              name="content"
              placeholder="Viết bình luận..."
              required
              formControlName="content"
            ></textarea>
          </div>

          <div class="comment-form-actions">
            <div
              class="emoji-picker-container"
              (appClickOutside)="activeEmojiPicker2 = false"
            >
              <svg
                class="emoji-button"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
                (click)="activeEmojiPicker2 = !activeEmojiPicker2"
              >
                <path stroke="none" d="M0 0h24v24H0z" />
                <circle cx="12" cy="12" r="9" />
                <line x1="9" y1="9" x2="9.01" y2="9" />
                <line x1="15" y1="9" x2="15.01" y2="9" />
                <path d="M8 13a4 4 0 1 0 8 0m0 0H8" />
              </svg>

              <div *ngIf="activeEmojiPicker2 === true" class="mt-2">
                <app-emoji
                  class="absolute top-0 -right-52"
                  (emojiSelect)="addEmoji($event, formComment)"
                ></app-emoji>
              </div>
            </div>

            <button class="reply-submit-button" type="submit">Gửi</button>
          </div>
        </form>
        }
      </div>
      <!-- Reply Container  -->

      <!-- Reply Container  -->
    </div>

    <!-- Reply Container  -->
  </div>

  <app-pagination
    *ngIf="showPagination"
    [currentPage]="currentPage"
    [totalpage]="totalPages"
    (OnChange)="OnChangePage($event)"
  >
  </app-pagination>
</div>

<ng-template #commentBlockTemplate let-comment="comment" let-hide="hide">
  <div class="comment-block" [id]="'id' + comment.id">
    <div class="comment-avatar">
      <div (click)="ViewInfoUser(comment.userID)" class="comment-avatar-icon">
        <img
          loading="lazy"
          download="tai xuong.jpg"
          [src]="comment?.avatar"
          class="comment-avatar-img"
          onerror="this.src='/default_avatar.jpg'"
          style="background: url('/effects/than_hoa.webp')"
          alt="avatar"
        />
      </div>
    </div>
    <div class="comment-content">
      <div class="comment-header">
        <p class="comment-username">{{ comment.userName }}</p>
        <span *ngIf="hide === false">
          <span class="comment-date">{{ comment.commentedAt | dateAgo }}</span>
        </span>
        <span class="flex-1"></span>
        <a [routerLink]="['/truyen-tranh', comment.url, comment.chapterID]" class="comment-chapter">
          [ Chapter-{{ comment.chapterName }} ]
        </a>
      </div>
      <div class="comment-text" [innerHTML]="comment.content | emojiParser">
        <!-- {{ comment.content | emojiParser }} -->
      </div>
      <div class="comment-actions">
        <div class="comment-actions-left">
          <svg
            class="comment-action-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"
            />
          </svg>
          <div class="comment-action-text" title="0">0</div>
          <svg
            class="comment-action-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"
            />
          </svg>
        </div>

        <button class="comment-reply" (click)="replyCmt(comment, hide)">Phản hồi</button>
      </div>

      <div *ngIf="comment.replies && comment.replies.length > 0">
        <button class="comment-view-reply" (click)="ViewReplyCmt(comment.id)">
          Xem ({{ comment.replies.length }}) phản hồi
        </button>
      </div>
    </div>
  </div>
</ng-template>
