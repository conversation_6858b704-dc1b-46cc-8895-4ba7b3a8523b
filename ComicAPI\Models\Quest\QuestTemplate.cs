using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Quest
{
    public class QuestTemplate
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(255), <PERSON>umn("title")]
        public string Title { get; set; } = string.Empty;

        [Column("description")]
        public string? Description { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(50), <PERSON>umn("questtype")]
        public string QuestType { get; set; } = string.Empty;

        [Required, Column("target")]
        public int Target { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(20), Column("difficulty")]
        public string Difficulty { get; set; } = string.Empty;

        [MaxLength(50), Column("icon")]
        public string? Icon { get; set; }

        [Column("isdaily")]
        public bool IsDaily { get; set; } = true;

        [Column("isweekly")]
        public bool IsWeekly { get; set; } = false;

        [Column("isactive")]
        public bool IsActive { get; set; } = true;

        [Column("createat")]
        public DateTime CreateAt { get; set; } = DateTime.UtcNow;

        [Column("updateat")]
        public DateTime UpdateAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<QuestReward> Rewards { get; set; } = new List<QuestReward>();
        public virtual ICollection<UserQuest> UserQuests { get; set; } = new List<UserQuest>();
    }

    public enum QuestType
    {
        READ_chapters,
        read_comics,
        add_favorites,
        write_comments,
        rate_comics,
        share_comics,
        login_streak,
        discover_new,
        complete_series,
        weekly_reading
    }

    public enum QuestDifficulty
    {
        easy,
        medium,
        hard,
        legendary
    }
}
