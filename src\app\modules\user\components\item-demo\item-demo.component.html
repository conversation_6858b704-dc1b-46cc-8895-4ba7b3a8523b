<div class="item-demo-container">
  <!-- Demo Header -->
  <div class="demo-header">
    <h1 class="demo-title">🎮 Item System Demo</h1>
    <p class="demo-subtitle">
      <PERSON><PERSON> thống Item với Mock Data - Test các tính năng quản lý vật phẩm
    </p>
  </div>

  <!-- Demo Controls -->
  <div class="demo-controls">
    <div class="control-section">
      <h3 class="control-title">🛠️ Demo Controls</h3>
      
      <!-- Add Item Control -->
      <div class="control-group">
        <label class="control-label">Thêm vật phẩm vào kho:</label>
        <div class="control-row">
          <select 
            [(ngModel)]="selectedItemId" 
            class="control-select"
          >
            <option 
              *ngFor="let item of availableItems; trackBy: trackByItemId" 
              [value]="item.id"
            >
              {{ item.name }} ({{ item.category }})
            </option>
          </select>
          
          <input 
            type="number" 
            [(ngModel)]="selectedQuantity" 
            min="1" 
            max="999"
            class="control-input"
            placeholder="Số lượng"
          >
          
          <button 
            (click)="addItemToInventory()"
            class="control-btn control-btn--primary"
          >
            ➕ Thêm
          </button>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="control-group">
        <label class="control-label">Hành động nhanh:</label>
        <div class="control-row">
          <button 
            (click)="giveRandomItems()"
            class="control-btn control-btn--secondary"
          >
            🎲 Thêm vật phẩm ngẫu nhiên
          </button>
          
          <button 
            (click)="clearEquippedItems()"
            class="control-btn control-btn--warning"
          >
            🗑️ Xóa trang bị
          </button>
          
          <button 
            (click)="resetToDefault()"
            class="control-btn control-btn--danger"
          >
            🔄 Reset về mặc định
          </button>
        </div>
      </div>
    </div>

    <!-- Info Section -->
    <div class="info-section">
      <h4 class="info-title">ℹ️ Hướng dẫn sử dụng</h4>
      <ul class="info-list">
        <li>✅ <strong>Xem kho đồ:</strong> Scroll xuống để xem inventory với filter và sort</li>
        <li>✅ <strong>Trang bị vật phẩm:</strong> Click "Trang bị" trên item card</li>
        <li>✅ <strong>Sử dụng vật phẩm:</strong> Click "Sử dụng" với consumable items</li>
        <li>✅ <strong>Xem trang bị:</strong> Phần "Trang bị hiện tại" hiển thị items đã equip</li>
        <li>✅ <strong>Thay đổi view:</strong> Switch giữa Grid và List view</li>
        <li>✅ <strong>Filter & Search:</strong> Lọc theo category, rarity, tìm kiếm theo tên</li>
      </ul>
    </div>
  </div>

  <!-- Equipped Items Section -->
  <div class="demo-section">
    <h2 class="section-title">⚔️ Trang bị hiện tại</h2>
    <app-equipped-items></app-equipped-items>
  </div>

  <!-- Inventory Section -->
  <div class="demo-section">
    <h2 class="section-title">🎒 Kho đồ</h2>
    <app-user-inventory></app-user-inventory>
  </div>

  <!-- Features Info -->
  <div class="features-section">
    <h3 class="features-title">🚀 Tính năng đã implement</h3>
    <div class="features-grid">
      <div class="feature-card">
        <h4 class="feature-title">📦 Quản lý Inventory</h4>
        <ul class="feature-list">
          <li>✅ Hiển thị danh sách vật phẩm</li>
          <li>✅ Filter theo category & rarity</li>
          <li>✅ Search theo tên</li>
          <li>✅ Sort theo nhiều tiêu chí</li>
          <li>✅ Pagination</li>
          <li>✅ Grid/List view</li>
        </ul>
      </div>

      <div class="feature-card">
        <h4 class="feature-title">⚡ Item Operations</h4>
        <ul class="feature-list">
          <li>✅ Use consumable items</li>
          <li>✅ Equip/Unequip items</li>
          <li>✅ View item details</li>
          <li>✅ Bulk selection</li>
          <li>✅ Quantity tracking</li>
          <li>✅ Expiry warnings</li>
        </ul>
      </div>

      <div class="feature-card">
        <h4 class="feature-title">🎨 UI/UX Features</h4>
        <ul class="feature-list">
          <li>✅ Responsive design</li>
          <li>✅ Dark mode support</li>
          <li>✅ Loading states</li>
          <li>✅ Empty states</li>
          <li>✅ Rarity color coding</li>
          <li>✅ Smooth animations</li>
        </ul>
      </div>

      <div class="feature-card">
        <h4 class="feature-title">⚙️ Performance</h4>
        <ul class="feature-list">
          <li>✅ OnPush change detection</li>
          <li>✅ Angular Signals</li>
          <li>✅ TrackBy functions</li>
          <li>✅ Lazy loading</li>
          <li>✅ Memory management</li>
          <li>✅ Debounced search</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Mock Data Info -->
  <div class="mock-data-section">
    <h3 class="mock-title">🧪 Mock Data Information</h3>
    <div class="mock-info">
      <p class="mock-description">
        Hệ thống hiện đang sử dụng <strong>Mock Data</strong> để demo. 
        Khi backend sẵn sàng, chỉ cần set <code>USE_MOCK_DATA = false</code> 
        trong <code>ItemService</code> để chuyển sang API thật.
      </p>
      
      <div class="mock-stats">
        <div class="stat-item">
          <span class="stat-number">20</span>
          <span class="stat-label">Item Templates</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">13</span>
          <span class="stat-label">Inventory Items</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">3</span>
          <span class="stat-label">Equipped Items</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">9</span>
          <span class="stat-label">Categories</span>
        </div>
      </div>
    </div>
  </div>
</div>
