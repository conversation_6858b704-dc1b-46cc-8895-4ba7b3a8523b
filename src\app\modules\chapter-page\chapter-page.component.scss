// Modern Chapter Page Component - Comic Website Design

// Header Container
.chapter-header-container {
  @apply mx-auto md:container flex flex-col items-center font-bold text-base static;
}

.chapter-header-card {
  @apply bg-white dark:bg-neutral-800 text-black dark:text-white p-4 lg:p-6 w-full rounded-t-xl dark:border-neutral-700 z-20;
}

// Tips Section
.tips-section {
  @apply mb-6;
}

.tips-content {
  @apply flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg border border-blue-200 dark:border-blue-800;
}

.tips-icon-wrapper {
  @apply flex-shrink-0;
}

.tips-icon {
  @apply w-6 h-6 text-blue-600 dark:text-blue-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.tips-text {
  @apply flex-1;
}

.tips-message {
  @apply flex items-center gap-2 text-sm font-medium text-blue-800 dark:text-blue-200 flex-wrap;
}

.tips-settings-icon {
  @apply w-4 h-4 text-blue-600 dark:text-blue-400;
  fill: currentColor;
  stroke: none;
}

// Main Content
.chapter-main-content {
  @apply text-center space-y-6;
}

// Report Error Section
.report-error-section {
  @apply flex justify-center;
}

.report-error-button {
  @apply flex flex-col items-center gap-2 px-4 py-3 bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-lg transition-all duration-200 border border-yellow-200 dark:border-yellow-800 hover:border-yellow-300 dark:hover:border-yellow-700;
}

.report-icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.report-text {
  @apply text-xs font-bold;
}

// Chapter Info Section
.chapter-info-section {
  @apply space-y-4;
}

.comic-title-wrapper {
  @apply space-y-2;
}

.comic-title {
  @apply text-center;
}

.comic-title-link {
  @apply text-xl lg:text-2xl font-bold uppercase text-primary-100 hover:text-primary-200 hover:underline transition-colors duration-200;
}

.chapter-details {
  @apply space-y-3;
}

.chapter-title {
  @apply text-base lg:text-lg font-semibold text-gray-900 dark:text-white;
}

.chapter-meta {
  @apply flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400;
}

.meta-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chapter-date {
  @apply font-medium;
}

// Server Selection Section
.server-selection-section {
  @apply mt-6;
}

.server-list {
  @apply flex flex-wrap items-center justify-center gap-3;
}

.server-button {
  @apply flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-neutral-700 hover:bg-gray-200 dark:hover:bg-neutral-600 text-gray-600 dark:text-gray-300 rounded-lg transition-all duration-200 border border-gray-200 dark:border-neutral-600 text-sm font-medium;

  &.server-button-active {
    @apply bg-primary-100 hover:bg-primary-200 text-white border-primary-100;
  }
}

.server-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.server-text {
  @apply font-medium;
}

.server-expand-button {
  @apply flex items-center justify-center p-2 bg-gray-100 dark:bg-neutral-700 hover:bg-gray-200 dark:hover:bg-neutral-600 text-gray-600 dark:text-gray-300 rounded-lg transition-all duration-200 border border-gray-200 dark:border-neutral-600;
}

.expand-icon {
  @apply w-5 h-5 transition-transform duration-200;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;

  &.expand-icon-rotated {
    @apply rotate-180;
  }
}

// Modern Control Bar
.modern-control-bar {
  @apply relative flex flex-row justify-center w-full items-center z-10 bg-white dark:bg-neutral-800 dark:border-neutral-700;
}

.control-bar-content {
  @apply flex justify-center w-full items-center h-full py-2 gap-2;
}

.control-group {
  @apply z-10 flex items-center gap-2;
}

.control-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border border-gray-200 dark:border-neutral-600 hover:border-primary-100/50;

  &.control-button-home {
    @apply text-primary-100 border-primary-100/30 bg-primary-100/5;
  }

  &.settings-button {
    @apply bg-gray-50 dark:bg-neutral-700;
  }
}

.control-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}


// Chapter Navigation Group
.chapter-navigation-group {
  @apply flex items-center gap-1 px-4 py-2 ;
}

.nav-button {
  @apply flex items-center gap-2 px-2 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-gray-200 dark:bg-neutral-600 hover:bg-primary-100 rounded-lg transition-all duration-200 border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed;

  &.nav-button-active {
    @apply text-white bg-primary-100;
  }
}

.nav-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.nav-label {
  @apply font-medium;
}

.chapter-selector-wrapper {
  @apply mx-1;
}

// Zoom Controls
.zoom-group {
  @apply relative;
}

.zoom-button {
  @apply relative;
}

.zoom-panel {
  @apply absolute hidden group-focus-within:flex bg-white dark:bg-neutral-800 rounded-lg shadow-lg border border-gray-200 dark:border-neutral-700 mt-12 p-4 space-y-3 items-center -translate-x-3/4 lg:translate-x-0 min-w-64 z-50;
}

.zoom-info {
  @apply flex items-center gap-4 w-full;
}

.zoom-percentage {
  @apply text-primary-100 text-sm font-semibold min-w-12;
}

.zoom-controls {
  @apply flex gap-2;
}

.zoom-control-btn {
  @apply flex items-center justify-center w-8 h-8 text-gray-600 dark:text-gray-300 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-md transition-all duration-200;
}

.zoom-control-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.zoom-reset-btn {
  @apply flex items-center gap-2 px-3 py-1.5 bg-primary-100 hover:bg-primary-200 text-white text-sm font-medium rounded-lg transition-all duration-200 border-none cursor-pointer;
}

.zoom-reset-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.zoom-reset-text {
  @apply font-medium;
}

// Settings Menu
.settings-menu {
  @apply absolute -z-50 bottom-0 transition-transform px-4 py-4 w-full bg-white dark:bg-neutral-800 shadow-lg border-t border-gray-200 dark:border-neutral-700;
}

.settings-menu-content {
  @apply -z-50 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-start justify-center lg:w-4/5 xl:w-3/4 mx-auto;
}

.settings-option {
  @apply space-y-3;
}

.settings-option-title {
  @apply text-sm font-semibold text-gray-900 dark:text-white mb-2;
}

.settings-option-buttons {
  @apply flex flex-col gap-2;
}

.settings-btn {
  @apply flex items-center gap-3 px-4 py-3 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-gray-50 dark:bg-neutral-700 hover:bg-gray-100 dark:hover:bg-neutral-600 rounded-lg transition-all duration-200 border border-gray-200 dark:border-neutral-600 cursor-pointer;

  &.settings-btn-active {
    @apply text-primary-100 bg-primary-100/10 border-primary-100/30;
  }

  &.settings-btn-full {
    @apply w-full justify-center;
  }
}

.settings-btn-icon {
  @apply w-5 h-5 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.settings-btn-text {
  @apply text-left;
}

// Toggle Switch
.settings-toggle {
  @apply flex items-center gap-3 cursor-pointer;
}

.settings-toggle-input {
  @apply sr-only;
}

.settings-toggle-slider {
  @apply relative w-12 h-6 bg-gray-200 dark:bg-neutral-600 rounded-full transition-colors duration-200;

  .settings-toggle-input:checked + & {
    @apply bg-primary-100;
  }
}

.settings-toggle-thumb {
  @apply absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-sm transition-transform duration-200;

  .settings-toggle-input:checked + .settings-toggle-slider & {
    @apply translate-x-6;
  }
}

.settings-toggle-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

// Reading Container
.reading-container {
  @apply relative w-full h-full;
}

.reading-content {
  @apply flex-grow min-h-screen mx-0 lg:mx-12 xl:mx-20;
}

// Scroll Navigation
.scroll-navigation {
  @apply absolute flex justify-between w-full z-20 h-1/4 top-1/3 px-4;

  &.scroll-navigation-hidden {
    @apply hidden;
  }
}

.scroll-btn {
  @apply flex items-center gap-2 px-4 py-3 bg-black/20 hover:bg-black/40 text-white rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/20;

  &.scroll-btn-prev {
    @apply bg-gradient-to-r from-black/30 to-transparent;
  }

  &.scroll-btn-next {
    @apply bg-gradient-to-l from-black/30 to-transparent;
  }
}

.scroll-btn-icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.scroll-btn-text {
  @apply font-medium hidden sm:inline;
}

// Loading State
.loading-container {
  @apply flex flex-col items-center justify-center min-h-screen space-y-8 p-8;
}

.loading-content {
  @apply flex flex-col items-center space-y-4;
}

.loading-spinner {
  @apply relative;
}

.loading-icon {
  @apply w-16 h-16 text-primary-100;
}

.loading-circle-bg {
  @apply opacity-25;
}

.loading-circle-progress {
  @apply opacity-75;
  animation: spin 1s linear infinite;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
  animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
  0% {
    stroke-dashoffset: 31.416;
  }
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -31.416;
  }
}

.loading-text {
  @apply text-center space-y-2;
}

.loading-title {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.loading-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.loading-skeleton {
  @apply space-y-4 w-full max-w-2xl;
}

.skeleton-page {
  @apply h-96 bg-gray-200 dark:bg-neutral-700 rounded-lg animate-pulse;
}

// End Chapter Navigation
.end-chapter-navigation {
  @apply flex justify-center w-full mt-8 mb-6;
}

.end-chapter-content {
  @apply flex items-center justify-between w-full max-w-4xl gap-6 p-6 bg-white dark:bg-neutral-800 rounded-xl border border-gray-200 dark:border-neutral-700 shadow-sm;
}

.end-nav-button {
  @apply flex items-center gap-3 px-4 py-3 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-gray-100 dark:bg-neutral-700 hover:bg-primary-100 rounded-lg transition-all duration-200 border border-gray-200 dark:border-neutral-600 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed;

  &.end-nav-active {
    @apply text-white bg-primary-100 border-primary-100;
  }
}

.end-nav-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.end-nav-text {
  @apply font-medium;
}

.end-chapter-info {
  @apply text-center space-y-2 flex-1 hidden sm:block;
}

.end-chapter-title {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.end-chapter-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// Scroll to Top Button
.scroll-to-top-btn {
  @apply fixed bottom-6 right-6 z-40 flex items-center gap-2 px-4 py-3 bg-primary-100 hover:bg-primary-200 text-white rounded-full shadow-lg transition-all duration-300 opacity-0 translate-y-4 pointer-events-none;

  &.scroll-to-top-visible {
    @apply opacity-100 translate-y-0 pointer-events-auto;
  }
}

.scroll-to-top-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.scroll-to-top-text {
  @apply font-medium hidden sm:inline;
}

// Responsive Design
@media (max-width: 1024px) {
  .reading-content {
    @apply mx-0 lg:mx-6;
  }


  .settings-menu-content {
    @apply grid-cols-1 sm:grid-cols-2 gap-3;
  }
}

@media (max-width: 768px) {
  .modern-control-bar {
    @apply h-14;
  }

  .control-button {
    @apply px-2 py-1.5 text-xs;
  }

  .chapter-navigation-group {
    @apply px-2 py-1.5 gap-2;
  }

  .nav-button {
    @apply px-2 py-1.5 text-xs;
  }


  .end-nav-button {
    @apply w-full justify-center;
  }

  .scroll-to-top-btn {
    @apply bottom-4 right-4 px-3 py-2;
  }
}

@media (max-width: 640px) {
  .control-label,
  .nav-label,
  .scroll-btn-text,
  .end-nav-text,


  .settings-menu-content {
    @apply grid-cols-1 gap-2;
  }

  .settings-btn {
    @apply px-3 py-2 text-xs;
  }

  .zoom-panel {
    @apply min-w-48 p-3;
  }
}

// Accessibility
.control-button:focus,
.nav-button:focus,
.settings-btn:focus,
.scroll-btn:focus,
.end-nav-button:focus,
.scroll-to-top-btn:focus {
  @apply outline-none ring-2 ring-primary-100/50;
}

// High Contrast Mode
@media (prefers-contrast: high) {
  .modern-control-bar,
  .settings-menu,
  .end-chapter-content {
    @apply border-black dark:border-white;
  }

  .control-button,
  .nav-button,
  .settings-btn,
  .end-nav-button {
    @apply border-2 border-gray-600;

    &.control-button-home,
    &.nav-button-active,
    &.settings-btn-active,
    &.end-nav-active {
      @apply border-2 border-primary-100;
    }
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  .control-button,
  .nav-button,
  .settings-btn,
  .scroll-btn,
  .end-nav-button,
  .scroll-to-top-btn,
  .settings-toggle-slider,
  .settings-toggle-thumb,
  .loading-icon {
    @apply transition-none;
  }

  .loading-circle-progress {
    animation: none;
  }
}

// Performance Optimizations
.modern-control-bar,
.settings-menu,
.control-button,
.nav-button,
.settings-btn,
.scroll-btn,
.end-nav-button,
.scroll-to-top-btn {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.sticky-top {
  @apply fixed w-full top-0 z-[100];
}

.sticky-bottom {
  @apply fixed w-full bottom-0 z-[100];
}

.translate-bottom {
  @apply -translate-y-full;
}
.translate-top {
  @apply translate-y-full;
}
 
#image-container {
  scroll-snap-align: start;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.night-mode {
  transition: all;
  transition-duration: 1.5s;
  transition-timing-function: ease-in-out;
  filter: brightness(0.9) sepia(0.4);
}

// Responsive Design
@media (max-width: 640px) {
  .chapter-header-card {
    @apply p-3 rounded-t-lg;
  }

  .tips-content {
    @apply p-3 gap-2;
  }

  .tips-message {
    @apply text-xs;
  }

  .comic-title-link {
    @apply text-lg;
  }

  .chapter-title {
    @apply text-sm;
  }

  .server-list {
    @apply gap-2;
  }

  .server-button {
    @apply px-3 py-1.5 text-xs;
  }
}

@media (max-width: 480px) {
  .tips-content {
    @apply flex-col items-start gap-3;
  }

}

.chapter-navigation {
  @apply bg-[#ccc] flex justify-center items-center rounded-lg cursor-pointer text-white;
}