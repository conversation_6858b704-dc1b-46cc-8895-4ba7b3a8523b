.sticky-top {
  @apply fixed w-full top-0 z-[100];
}

.sticky-bottom {
  @apply fixed w-full bottom-0 z-[100];
}

.translate-bottom {
  @apply -translate-y-full;
}
.translate-top {
  @apply translate-y-full;
}
 
#image-container {
  scroll-snap-align: start;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.night-mode {
  transition: all;
  transition-duration: 1.5s;
  transition-timing-function: ease-in-out;
  filter: brightness(0.9) sepia(0.4);
}

.chapter-navigation {
  @apply bg-[#ccc] flex justify-center items-center rounded-lg cursor-pointer text-white;
}