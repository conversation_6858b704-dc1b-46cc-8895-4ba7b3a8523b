<button
  type="button"
  title="Tì<PERSON> kiếm"
  class="btn-search"
  (click)="OnSearchClick()"
>
  <svg
    class="search-icon"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
</button>

<div
  #SearchFrame
  [ngClass]="{ 'z-50': isSearching }"
  class="hidden absolute top-0 right-0 left-0 h-16 md:relative md:flex justify-end md:h-8 md:w-[32rem]"
>
  <div class="relative w-full rounded-lg top-4 md:top-0">
    <input
      #SearchInput
      (focus)="OnSearchFocus(true)"
      (input)="OnSearchChange()"
      type="text"
      class="custom-transition search-input"
      placeholder="Tì<PERSON> kiếm..."
    />

    <svg
      class="h-4 w-4 text-primary-100 absolute right-3 top-4 transform -translate-y-1/2 transition-opacity duration-300"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      [ngClass]="{ 'opacity-0': isSearching }"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
    <svg
      *ngIf="searchText.length > 0 && isSearching"
      (click)="clearSearch()"
      class="clear-icon h-6 w-6 text-white absolute right-3 top-4 transform -translate-y-1/2 cursor-pointer"
      viewBox="0 0 24 24"
      fill="#F86E4C"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="15" y1="9" x2="9" y2="15" />
      <line x1="9" y1="9" x2="15" y2="15" />
    </svg>
  </div>

  <div
    (CloseEvent)="OnSearchFocus(false)"
    class="search-result-panel w-64 transition-[width] right-0"
    [ngClass]="{
      '!invisible': !isSearching,
      '!w-full': isSearching,
    }"
  >
    <div class="-mt-2">
      <ng-container *ngIf="isLoading; else searchResults">
        <section
          *ngFor="let comic of skeletonLoader; let i = index"
          class="item-search-skeleton"
        >
          <div class="px-2 py-1">
            <div class="flex flex-row space-y-0 py-1 text-left">
              <div
                class="h-20 w-14 rounded-lg bg-gray-200 dark:bg-neutral-800"
              ></div>
              <div class="relative flex px-2 lg:px-3 justify-between w-full">
                <div class="pr-3 my-auto w-full">
                  <div class="flex justify-between">
                    <div
                      class="h-4 bg-gray-200 dark:bg-neutral-800 rounded w-3/4 mb-2"
                    ></div>
                  </div>
                  <div class="flex gap-2">
                    <div
                      class="h-4 bg-gray-200 dark:bg-neutral-800 rounded w-1/2 mb-2"
                    ></div>
                  </div>
                  <div class="flex gap-3 mt-1">
                    <div
                      class="h-4 bg-gray-200 dark:bg-neutral-800 rounded w-1/6"
                    ></div>
                    <div
                      class="h-4 bg-gray-200 dark:bg-neutral-800 rounded w-1/6"
                    ></div>
                    <div
                      class="h-4 bg-gray-200 dark:bg-neutral-800 rounded w-1/6"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </ng-container>
      <ng-template #searchResults>
        <div
          class="flex items-center my-4 dark:text-white"
          *ngIf="searchText !== ''"
        >
          <div class="font-bold text-xl flex-grow">Kết quả</div>
          <svg
            class="h-6 w-6 cursor-pointer"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            (click)="OnSearchFocus(false)"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>
        <div class="flex items-center" *ngIf="!searchText">
          <div class="text-md flex-grow mt-3 text-gray-400">
            Nhập từ khóa tìm kiếm...
          </div>
        </div>
        <div
          *ngIf="!isLoading && listSearch.length === 0 && searchText"
          class="flex mx-auto w-20"
        >
          <app-empty class="w-96"> </app-empty>
        </div>
        <!-- Search Results -->
        <div *ngIf="!isLoading && listSearch.length > 0">
          <section *ngFor="let comic of listSearch" class="item-search-result">
            <a
              [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
              (click)="OnSearchFocus(false)"
            >
              <div class="px-2 py-1">
                <div class="flex flex-row space-y-0 py-1 text-left">
                  <img
                    loading="lazy"
                    class="h-20 w-14 rounded-lg object-cover my-auto"
                    src="{{ comic.coverImage }}"
                    alt="{{ comic.title }}"
                    onerror="this.src='/option2.png'"
                  />
                  <div class="relative flex px-2 lg:px-3 justify-between">
                    <div class="pr-3 my-auto">
                      <div class="flex justify-between">
                        <p class="text-lg font-bold line-clamp-1">
                          {{ comic.title }}
                        </p>
                      </div>
                      <div
                        class="text-[0.75rem] text-center comment-container flex gap-1 items-center"
                      >
                        @if (comic.status === 0) {
                        <div
                          class="h-2 w-2 rounded-full bg-sky-400 opacity-75"
                        ></div>
                        <div>Đang tiến hành</div>
                        } @else {
                        <div
                          class="h-2 w-2 rounded-full bg-lime-500 opacity-75"
                        ></div>
                        <div>Đã hoàn thành</div>
                        }
                      </div>
                      <div class="flex gap-3 mt-1">
                        <div
                          class="text-sm text-center uppercase flex gap-1 items-center text-primary-100"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            fill="none"
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            class="feather feather-eye icon small text-icon-contrast text-undefined"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"
                            ></path>
                            <circle cx="12" cy="12" r="3"></circle>
                          </svg>
                          {{ comic.viewCount | numeral }}
                        </div>
                        <div
                          class="text-sm text-center flex gap-1 items-center"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            fill="none"
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            class="feather feather-star icon rel"
                            viewBox="0 0 24 24"
                            style="color: currentcolor"
                          >
                            <path
                              d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01z"
                            ></path></svg
                          >{{ comic.rating }}
                        </div>
                        <div
                          class="text-sm text-center flex gap-1 items-center"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            fill="none"
                            viewBox="0 0 24 24"
                            class="icon small text-icon-contrast text-undefined"
                          >
                            <path
                              stroke="currentColor"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"
                            ></path></svg
                          >{{ comic.rating }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </section>
        </div>
        <a
          *ngIf="listSearch.length > 0"
          [routerLink]="['/tim-truyen']"
          [queryParams]="{ page: 1, keyword: searchText }"
          (click)="OnSearchFocus(false)"
          class="cursor-pointer flex items-center justify-end text-center mx-auto hover:underline dark:text-gray-500 dark:hover:text-primary-100 hover:text-primary-100"
        >
          Xem thêm
          <svg
            class="h-5 w-5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="13 17 18 12 13 7" />
            <polyline points="6 17 11 12 6 7" />
          </svg>
        </a>
      </ng-template>
    </div>
  </div>

  <div
    (click)="OnSearchFocus(false)"
    class="fixed -z-10 top-0 bottom-0 right-0 left-0 bg-gray-800 opacity-30"
  ></div>
</div>
