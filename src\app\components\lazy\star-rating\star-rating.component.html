<ng-container *ngIf="isVisible">
  <div class="modal-overlay">
    <div class="modal-content">
      <button class="close-button" (click)="cancelVote()">
        <svg
          class="close-icon"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <line x1="18" y1="6" x2="6" y2="18" />
          <line x1="6" y1="6" x2="18" y2="18" />
        </svg>
      </button>
      <h3 class="modal-title"><PERSON><PERSON><PERSON> c<PERSON><PERSON> th<PERSON><PERSON> t<PERSON><PERSON>n như thế nào?</h3>
      <div class="star-container">
        <ng-container *ngFor="let star of stars; let i = index">
          <svg
            class="star"
            [class.star-gray]="i >= tempRating"
            [class.star-yellow]="i < tempRating"
            (click)="tempRating = i + 1"
            xmlns="http://www.w3.org/2000/svg"
            height="16.8"
            width="18.9"
            viewBox="0 0 576 512"
          >
            <path
              d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"
            />
          </svg>
        </ng-container>
      </div>
      <div class="modal-actions">
        <button class="submit-button" (click)="confirmVote()">Gửi</button>
        <button class="cancel-button" (click)="cancelVote()">Hủy</button>
      </div>
    </div>
  </div>
</ng-container>
