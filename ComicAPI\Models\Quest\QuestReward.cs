using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Quest
{
    public class QuestReward
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, <PERSON>umn("questtemplateid")]
        public int QuestTemplateID { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(50), <PERSON><PERSON><PERSON>("rewardtype")]
        public string RewardType { get; set; } = string.Empty;

        [Required, Column("amount")]
        public int Amount { get; set; }

        [MaxLength(100), Column("item")]
        public string? Item { get; set; }

        [MaxLength(255), Column("description")]
        public string? Description { get; set; }

        // Navigation properties
        public virtual QuestTemplate QuestTemplate { get; set; } = null!;
    }

    public enum RewardType
    {
        coins,
        experience,
        badge,
        avatar_frame,
        title,
        premium_days
    }
}
