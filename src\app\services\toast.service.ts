// src/app/toast.service.ts
import { ComponentRef, Injectable, ViewContainerRef } from '@angular/core';
import { ToastComponent } from '@components/common/toast/toast.component';
import { BehaviorSubject, timer } from 'rxjs';

export enum ToastType {
  Success = 'success',
  Error = 'error',
  Info = 'info',
  Warning = 'warning',
}
interface Toast {
  id: number;
  type: ToastType
  message: string;
  state: 'enter' | 'leave'; // Add state property
}

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private _viewContainerRef?: ViewContainerRef;
  private componentRef?: ComponentRef<ToastComponent>;
  toasts: Toast[] = [];
  toasts$: BehaviorSubject<Toast[]> = new BehaviorSubject<Toast[]>(this.toasts);
  private nextId = 0;

  show(type: ToastType, message: string, duration = 3000): void {
    this.componentRef = this.createDynamicComponent();
    const id = this.nextId++;
    const toast: Toast = { id, message, type, state: 'enter' }; // Initialize with 'enter' state
    this.toasts.push(toast);
    this.toasts$.next(this.toasts);
    timer(duration).subscribe(() => this.startLeaveAnimation(toast));
  }

  startLeaveAnimation(toast: Toast): void {
    toast.state = 'leave';
    this.toasts$.next(this.toasts);

    timer(300).subscribe(() => this.remove(toast));
  }

  remove(toast: Toast): void {
    this.toasts = this.toasts.filter((t) => t.id !== toast.id);
    this.toasts$.next(this.toasts);

  }
  set viewContainerRef(viewContainerRef: ViewContainerRef | undefined) {
    this._viewContainerRef = viewContainerRef;
  }

  private createDynamicComponent(): ComponentRef<ToastComponent> {
    if (this.componentRef) {
      if (!this.componentRef.hostView.destroyed) {
        return this.componentRef;
      }
    }
    const componentRef = this._viewContainerRef?.createComponent<ToastComponent>(ToastComponent)!;

    return componentRef;
  }
}
