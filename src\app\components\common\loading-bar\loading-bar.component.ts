import { isPlatformServer } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  NgZone,
  PLATFORM_ID,
  ViewChild,
  ViewEncapsulation, OnInit
} from '@angular/core';
import {
  NavigationCancel,
  NavigationEnd,
  NavigationError,
  NavigationStart,
  ResolveEnd,
  Router
} from '@angular/router';
import { LoadingService } from '@services/loading.service';
import { Subscription, timer } from 'rxjs';

@Component({
  selector: 'app-loading-bar',
  templateUrl: './loading-bar.component.html',
  styleUrl: './loading-bar.component.scss',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingBarComponent implements OnInit {
  @ViewChild('ProgressBar') ProgressBar!: ElementRef;
  isLoading = false;
  isDone = false;
  maxtask = 0;
  timer1: Subscription | null = null;
  timer2: Subscription | null = null;
  progress = 0;
  navigationEnd = false;
  constructor(
    public loadingService: LoadingService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: object,
    private ngZone: NgZone,
    private cd: ChangeDetectorRef
  ) { }
  ngOnInit(): void {
    this.router.events.subscribe((event) => {
      if (isPlatformServer(this.platformId)) return;
      if (event instanceof NavigationStart) {
        this.isLoading = true;
        this.isDone = false;
        this.progress = 5;
        this.navigationEnd = false;
        this.clearTimer();
        this.cd.detectChanges();

      }

      if (event instanceof NavigationCancel || event instanceof NavigationError) {
        this.isLoading = false;
        this.clearTimer();
        this.cd.detectChanges();

      }

      const x = event instanceof ResolveEnd ? 5 : 1;
      this.progress += x;
      this.ProgressBar.nativeElement.style.width = `${this.progress}%`;

      if (event instanceof NavigationEnd) {
        this.maxtask = this.loadingService.tasks.length;
        this.navigationEnd = true;
        this.checkTask(this.loadingService.tasks);
      }
    });

    this.loadingService.tasks$.subscribe((tasks) => {
      if (isPlatformServer(this.platformId)) return;
      this.checkTask(tasks);
    })
  }
  checkTask(tasks: string[]) {
    if (!this.navigationEnd) return;
    if (!this.isDone) {
      const taskLength = tasks.length
      const value = ((this.maxtask - taskLength) / this.maxtask) * (80 - this.progress);
      this.ProgressBar.nativeElement.style.width = `${this.progress + value}%`;

      if (taskLength === 0) {
        this.isDone = true;
        this.timer1 = timer(200).subscribe(() => {
          this.DoneLoading();
          this.timer1 = null;
        })
        return;
      }
    }
  }
  clearTimer() {
    if (this.timer1) this.timer1.unsubscribe();
    if (this.timer2) this.timer2.unsubscribe();
    this.timer1 = null;
    this.timer2 = null;
  }

  DoneLoading() {
    this.ProgressBar.nativeElement.style.width = '100%';
    this.timer2 = timer(500).subscribe(() => {
      this.isLoading = false;
      this.timer2 = null;
      this.cd.detectChanges();
    });

  }
}
