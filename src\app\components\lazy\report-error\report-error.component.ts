import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AccountService } from '@services/account.service';
import { ReportErrorService } from '@services/reportError.service';
import { ToastService, ToastType } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
  templateUrl: './report-error.component.html',
  styleUrl: './report-error.component.scss',
  imports: [CommonModule, ReactiveFormsModule],
  animations: [
    trigger('formAnimation', [
      state('void', style({ opacity: 0, transform: 'scale(0.9)' })),
      state('*', style({ opacity: 1, transform: 'scale(1)' })),
      transition('void => *', [animate('200ms ease-out')]),
      transition('* => void', [animate('200ms ease-in')]),
    ]),
  ]
})
export class ReportErrorComponent implements IPopupComponent {
  isVisible = false;
  errorForm: FormGroup;
  chapterID = 0;
  defaultType: string[] = ['Truyện tải chậm', 'Lỗi hiển thị', 'khác'];

  constructor(
    private fb: FormBuilder,
    private toastService: ToastService,
    private reportErrorService: ReportErrorService,
    private accountService: AccountService,
    private cd: ChangeDetectorRef
  ) {
    this.errorForm = this.fb.group({
      errorType: [
        '',
        Validators.compose([Validators.required, Validators.maxLength(1000)]),
      ],
      message: ['', Validators.maxLength(1000)],
    });
  }
  setVisible(isVisible: boolean): void {
    this.isVisible = isVisible;
    this.cd.detectChanges();
  }
  async show(o: any) {
    this.setVisible(true);
    this.chapterID = o.chapterID;
    this.cd.detectChanges();
    return new Promise((resolve) => {
      resolve({});
    });
  }

  sendReportError() {
    if (this.errorForm.valid) {
      const { errorType, message } = this.errorForm.value;
      const user = this.accountService.GetUser();
      const name = user?.email || '<EMAIL>';
      this.toastService.show(
        ToastType.Info,
        'Cảm ơn bạn đã báo lỗi, team sẽ xác nhận và cải thiện nhé!',
      );

      this.reportErrorService
        .sendReport({
          name,
          errorType,
          message,
          chapterid: this.chapterID,
        })
        .subscribe({
          next: () => {
            this.toastService.show(ToastType.Success, 'Gửi báo cáo thành công');
            this.errorForm.reset();
            this.setVisible(false);
          },
          error: (error) => {
            this.toastService.show(
              ToastType.Error,
              'Gửi báo cáo thất bại, đã có lỗi xảy ra',
            );
            this.setVisible(false);
          },
        });
    } else {
      console.error('Form is invalid');
      this.setVisible(false);
      this.toastService.show(
        ToastType.Error,
        'Gửi thất báo cáo thất bại, đã có lỗi xảy ra',
      );
      return;
    }
    this.setVisible(false);


  }


  setErrorType(tag: string) {
    this.errorForm.controls['errorType'].setValue(tag);
  }
}
