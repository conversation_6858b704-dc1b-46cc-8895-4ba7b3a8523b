import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AccountService } from '@services/account.service';
import { ToastService, ToastType } from '@services/toast.service';
import { first } from 'rxjs';

@Component({
    selector: 'app-register-form',
    templateUrl: './register-form.component.html',
    styleUrl: './register-form.component.scss',
    standalone: false
})
export class RegisterFormComponent implements OnDestroy {
  form!: FormGroup;
  loading = false;
  submitted = false;
  showPassword = false;
  showConfirmPassword = false;

  constructor(
    private router: Router, // private authService: AuthService
    private formBuilder: FormBuilder,
    private accountService: AccountService,
    private toast: ToastService,
  ) {
    this.form = this.formBuilder.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required],
      'confirm-password': ['', Validators.required],
      accept: ['', Validators.required],
    });
  }

  ngOnDestroy(): void { }
  onSubmit() {
    if (!this.form.valid) {
      this.submitted = true;
      return;
    }

    const email = this.form.value.email;
    const password = this.form.value.password;
    const confirmPassword = this.form.value['confirm-password'];
    if (password !== confirmPassword) {
      this.toast.show(ToastType.Error, 'Mật khẩu xác nhận không đúng');
      return;
    }
    const name = this.form.value.name;
    this.accountService
      .Register(name, email, password)
      .pipe(first())
      .subscribe((res: any) => {
        if (res.status === 1) {
          this.submitted = false;
          this.toast.show(ToastType.Success, res.message);
          setTimeout(() => {
            this.accountService.SaveUserDeconfirm({
              id: res.data.id,
              email: email,
            });
            // this.router.navigate(['auth/confirm-email']);
            this.router.navigate(['auth/login']);
          }, 1000);
        } else {
          this.toast.show(ToastType.Error, res.message);
        }
      });
  }
  isControlInvalid(control: string): boolean {
    const ctrl = this.form.get(control);
    return ctrl
      ? (ctrl.invalid && (ctrl.dirty || ctrl.touched)) || this.submitted
      : false;
  }
}
