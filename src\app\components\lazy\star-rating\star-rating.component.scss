.modal-overlay {
    @apply fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50;
  }
  
  .modal-content {
    @apply bg-white p-6 rounded-lg shadow-lg text-center relative dark:bg-neutral-800;
  }
  
  .close-button {
    @apply absolute top-2 right-2 text-gray-500 hover:text-gray-700;
  }
  
  .close-icon {
    @apply h-5 w-5 text-gray-400;
  }
  
  .modal-title {
    @apply text-lg font-semibold mb-2;
  }
  
  .star-container {
    @apply flex justify-center mb-6;
  }
  
  .star {
    @apply fill-current cursor-pointer mx-1;
  }
  
  .star-gray {
    @apply text-gray-300;
  }
  
  .star-yellow {
    @apply text-yellow-500;
  }
  
  .modal-actions {
    @apply flex justify-center;
  }
  
  .submit-button {
    @apply bg-primary-100 text-white px-4 py-2 rounded mr-2;
  }
  
  .cancel-button {
    @apply bg-gray-200 text-gray-700 px-4 py-2 rounded;
  }
  