// Modern Range Component - Comic Website Design
.setting-range-container {
  @apply flex flex-col gap-3;
}

.setting-range-header {
  @apply flex items-center justify-between;
}

.setting-range-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-200;
}

.setting-range-value {
  @apply text-sm font-semibold text-primary-100;
  @apply bg-primary-50 dark:bg-primary-100/10;
  @apply px-2 py-1 rounded-md;
}

.setting-range-wrapper {
  @apply relative;
}

.setting-range-input {
  @apply w-full h-2 bg-transparent cursor-pointer appearance-none;
  @apply relative z-10;

  &::-webkit-slider-track {
    @apply w-full h-2 bg-transparent;
  }

  &::-webkit-slider-thumb {
    @apply appearance-none w-5 h-5 rounded-full;
    @apply bg-primary-100 shadow-lg;
    @apply border-2 border-white dark:border-neutral-800;
    @apply cursor-pointer;
    @apply transition-all duration-200;
    @apply hover:scale-110;
  }

  &::-moz-range-track {
    @apply w-full h-2 bg-transparent;
  }

  &::-moz-range-thumb {
    @apply w-5 h-5 rounded-full;
    @apply bg-primary-100 shadow-lg;
    @apply border-2 border-white dark:border-neutral-800;
    @apply cursor-pointer;
    @apply transition-all duration-200;
  }

  &:disabled {
    @apply opacity-50 cursor-not-allowed;

    &::-webkit-slider-thumb {
      @apply cursor-not-allowed;
    }

    &::-moz-range-thumb {
      @apply cursor-not-allowed;
    }
  }

  &:focus {
    @apply outline-none;

    &::-webkit-slider-thumb {
      @apply ring-2 ring-primary-100 ring-offset-2;
    }

    &::-moz-range-thumb {
      @apply ring-2 ring-primary-100 ring-offset-2;
    }
  }
}

.setting-range-track {
  @apply absolute top-1/2 left-0 right-0 h-2 -translate-y-1/2;
  @apply bg-gray-200 dark:bg-neutral-700 rounded-full;
  @apply overflow-hidden;
}

.setting-range-progress {
  @apply h-full bg-gradient-to-r from-primary-100 to-primary-200;
  @apply transition-all duration-200;
  @apply rounded-full;
}

.setting-range-labels {
  @apply flex justify-between text-xs text-gray-500 dark:text-gray-400;
}

.setting-range-min,
.setting-range-max {
  @apply font-medium;
}

.setting-range-description {
  @apply text-xs text-gray-500 dark:text-gray-400;
  @apply leading-relaxed;
}
