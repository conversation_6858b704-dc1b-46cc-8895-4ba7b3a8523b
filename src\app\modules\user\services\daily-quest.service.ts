import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, timer } from 'rxjs';
import { catchError, switchMap, tap } from 'rxjs/operators';

import { UrlService } from '@services/url.service';
import {
  ClaimRewardResponse,
  DailyQuest,
  QuestHistory,
  QuestLeaderboard,
  QuestRefreshStatus,
  QuestTemplate,
  UserQuestStats,
  WeeklyQuest
} from '../interfaces/daily-quest.interface';

@Injectable({
  providedIn: 'root'
})
export class QuestService {
  private readonly API_BASE = '/quest';

  // State management
  private dailyQuestsSubject = new BehaviorSubject<DailyQuest[]>([]);
  private weeklyQuestsSubject = new BehaviorSubject<WeeklyQuest[]>([]);
  private userStatsSubject = new BehaviorSubject<UserQuestStats | null>(null);
  private questTemplatesSubject = new BehaviorSubject<QuestTemplate[]>([]);

  // Public observables
  public dailyQuests$ = this.dailyQuestsSubject.asObservable();
  public weeklyQuests$ = this.weeklyQuestsSubject.asObservable();
  public userStats$ = this.userStatsSubject.asObservable();
  public questTemplates$ = this.questTemplatesSubject.asObservable();

  // Auto-refresh timer
  private refreshTimer$ = timer(0, 60000); // Check every minute

  constructor(
    private http: HttpClient,
    private urlService: UrlService
  ) {
    this.initializeData();
    this.setupAutoRefresh();
  }

  // Initialize data from API
  private initializeData(): void {
    this.loadDailyQuests();
    this.loadWeeklyQuests();
    this.loadUserStats();
    this.loadQuestTemplates();
    this.checkAndRefreshQuests();
  }

  private setupAutoRefresh(): void {
    // Auto-refresh every minute to check for quest resets
    this.refreshTimer$.pipe(
      switchMap(() => this.checkAndRefreshQuests())
    ).subscribe({
      next: (refreshStatus) => {
        if (refreshStatus.dailyRefreshed || refreshStatus.weeklyRefreshed) {
          console.log('Quests auto-refreshed:', refreshStatus);
          this.loadDailyQuests();
          this.loadWeeklyQuests();
        }
      },
      error: (error) => {
        console.error('Auto-refresh error:', error);
      }
    });
  }

  // API Methods
  getDailyQuests(): Observable<DailyQuest[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/daily`);
    return this.http.get<DailyQuest[]>(url).pipe(
      tap(quests => this.dailyQuestsSubject.next(quests)),
      catchError(error => {
        console.error('Error fetching daily quests:', error);
        return of([]);
      })
    );
  }

  getWeeklyQuests(): Observable<WeeklyQuest[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/weekly`);
    return this.http.get<WeeklyQuest[]>(url).pipe(
      tap(quests => this.weeklyQuestsSubject.next(quests)),
      catchError(error => {
        console.error('Error fetching weekly quests:', error);
        return of([]);
      })
    );
  }

  getUserStats(): Observable<UserQuestStats | null> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/user-stats`);
    return this.http.get<UserQuestStats>(url).pipe(
      tap(stats => this.userStatsSubject.next(stats)),
      catchError(error => {
        console.error('Error fetching user stats:', error);
        return of(null);
      })
    );
  }

  getQuestTemplates(): Observable<QuestTemplate[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/templates`);
    return this.http.get<QuestTemplate[]>(url).pipe(
      tap(templates => this.questTemplatesSubject.next(templates)),
      catchError(error => {
        console.error('Error fetching quest templates:', error);
        return of([]);
      })
    );
  }

  claimReward(questId: string): Observable<ClaimRewardResponse> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/${questId}/claim`);
    return this.http.post<ClaimRewardResponse>(url, {}).pipe(
      tap((response) => {
        if (response.success) {
          // Refresh quests and stats after successful claim
          this.loadDailyQuests();
          this.loadWeeklyQuests();
          this.loadUserStats();
        }
      }),
      catchError(error => {
        console.error('Error claiming reward:', error);
        return of({
          success: false,
          message: 'Có lỗi xảy ra khi nhận thưởng',
          rewards: []
        });
      })
    );
  }

  updateQuestProgress(questId: string, progress: number): Observable<boolean> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/${questId}/progress`);
    const body = { progress };
    return this.http.put<boolean>(url, body).pipe(
      tap((success) => {
        if (success) {
          // Refresh quests after progress update
          this.loadDailyQuests();
          this.loadWeeklyQuests();
        }
      }),
      catchError(error => {
        console.error('Error updating quest progress:', error);
        return of(false);
      })
    );
  }

  checkAndRefreshQuests(): Observable<QuestRefreshStatus> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/check-refresh`);
    return this.http.post<QuestRefreshStatus>(url, {}).pipe(
      catchError(error => {
        console.error('Error checking quest refresh:', error);
        return of({
          dailyRefreshed: false,
          weeklyRefreshed: false,
          nextDailyReset: new Date(),
          nextWeeklyReset: new Date()
        });
      })
    );
  }

  refreshDailyQuests(): Observable<boolean> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/refresh/daily`);
    return this.http.post<boolean>(url, {}).pipe(
      tap((success) => {
        if (success) {
          this.loadDailyQuests();
        }
      }),
      catchError(error => {
        console.error('Error refreshing daily quests:', error);
        return of(false);
      })
    );
  }

  refreshWeeklyQuests(): Observable<boolean> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/refresh/weekly`);
    return this.http.post<boolean>(url, {}).pipe(
      tap((success) => {
        if (success) {
          this.loadWeeklyQuests();
        }
      }),
      catchError(error => {
        console.error('Error refreshing weekly quests:', error);
        return of(false);
      })
    );
  }

  getQuestHistory(page: number = 1, pageSize: number = 20): Observable<QuestHistory[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/history`);
    const params = { page: page.toString(), pageSize: pageSize.toString() };
    return this.http.get<QuestHistory[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Error fetching quest history:', error);
        return of([]);
      })
    );
  }

  getQuestLeaderboard(period: string = 'weekly', limit: number = 50): Observable<QuestLeaderboard[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/leaderboard`);
    const params = { period, limit: limit.toString() };
    return this.http.get<QuestLeaderboard[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Error fetching quest leaderboard:', error);
        return of([]);
      })
    );
  }

  initializeUserQuests(): Observable<boolean> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/initialize`);
    return this.http.post<boolean>(url, {}).pipe(
      tap((success) => {
        if (success) {
          this.initializeData();
        }
      }),
      catchError(error => {
        console.error('Error initializing user quests:', error);
        return of(false);
      })
    );
  }

  // Private helper methods
  private loadDailyQuests(): void {
    this.getDailyQuests().subscribe();
  }

  private loadWeeklyQuests(): void {
    this.getWeeklyQuests().subscribe();
  }

  private loadUserStats(): void {
    this.getUserStats().subscribe();
  }

  private loadQuestTemplates(): void {
    this.getQuestTemplates().subscribe();
  }

  // Utility methods
  refreshAll(): void {
    this.initializeData();
  }

  getCurrentDailyQuests(): DailyQuest[] {
    return this.dailyQuestsSubject.value;
  }

  getCurrentWeeklyQuests(): WeeklyQuest[] {
    return this.weeklyQuestsSubject.value;
  }

  getCurrentUserStats(): UserQuestStats | null {
    return this.userStatsSubject.value;
  }

  getCurrentQuestTemplates(): QuestTemplate[] {
    return this.questTemplatesSubject.value;
  }

  // Helper methods for quest progress calculation
  getQuestProgress(questId: string): number {
    const dailyQuest = this.getCurrentDailyQuests().find(q => q.id === questId);
    if (dailyQuest) {
      return Math.min((dailyQuest.current / dailyQuest.target) * 100, 100);
    }

    const weeklyQuest = this.getCurrentWeeklyQuests().find(q => q.id === questId);
    if (weeklyQuest) {
      return Math.min((weeklyQuest.current / weeklyQuest.target) * 100, 100);
    }

    return 0;
  }

  isQuestCompleted(questId: string): boolean {
    const dailyQuest = this.getCurrentDailyQuests().find(q => q.id === questId);
    if (dailyQuest) {
      return dailyQuest.status === 'completed';
    }

    const weeklyQuest = this.getCurrentWeeklyQuests().find(q => q.id === questId);
    if (weeklyQuest) {
      return weeklyQuest.status === 'completed';
    }

    return false;
  }

  canClaimReward(questId: string): boolean {
    return this.isQuestCompleted(questId);
  }
}
