import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import {
  DailyQuest,
  QuestDifficulty,
  QuestStatus,
  QuestType,
  RewardType,
  UserQuestStats,
  WeeklyQuest
} from '../interfaces/daily-quest.interface';

@Injectable({
  providedIn: 'root'
})
export class DailyQuestService {
  private dailyQuestsSubject = new BehaviorSubject<DailyQuest[]>([]);
  private weeklyQuestsSubject = new BehaviorSubject<WeeklyQuest[]>([]);
  private userStatsSubject = new BehaviorSubject<UserQuestStats | null>(null);

  dailyQuests$ = this.dailyQuestsSubject.asObservable();
  weeklyQuests$ = this.weeklyQuestsSubject.asObservable();
  userStats$ = this.userStatsSubject.asObservable();

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData(): void {
    // Mock daily quests data
    const dailyQuests: DailyQuest[] = [
      {
        id: 'daily_1',
        title: 'Đọc 3 chapter',
        description: 'Đọc ít nhất 3 chapter truyện tranh trong ngày',
        type: QuestType.READ_CHAPTERS,
        target: 3,
        current: 1,
        reward: {
          type: RewardType.COINS,
          amount: 50,
          description: '50 xu'
        },
        status: QuestStatus.ACTIVE,
        icon: 'book-open',
        difficulty: QuestDifficulty.EASY,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      },
      {
        id: 'daily_2',
        title: 'Bình luận 2 truyện',
        description: 'Viết bình luận cho 2 truyện tranh khác nhau',
        type: QuestType.WRITE_COMMENTS,
        target: 2,
        current: 0,
        reward: {
          type: RewardType.EXPERIENCE,
          amount: 100,
          description: '100 EXP'
        },
        status: QuestStatus.ACTIVE,
        icon: 'message-circle',
        difficulty: QuestDifficulty.EASY,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      },
      {
        id: 'daily_3',
        title: 'Thêm 1 truyện yêu thích',
        description: 'Thêm một truyện tranh vào danh sách yêu thích',
        type: QuestType.ADD_FAVORITES,
        target: 1,
        current: 1,
        reward: {
          type: RewardType.COINS,
          amount: 30,
          description: '30 xu'
        },
        status: QuestStatus.COMPLETED,
        icon: 'heart',
        difficulty: QuestDifficulty.EASY,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        completedAt: new Date()
      },
      {
        id: 'daily_4',
        title: 'Đánh giá 3 truyện',
        description: 'Đánh giá sao cho 3 truyện tranh',
        type: QuestType.RATE_COMICS,
        target: 3,
        current: 0,
        reward: {
          type: RewardType.EXPERIENCE,
          amount: 150,
          description: '150 EXP'
        },
        status: QuestStatus.ACTIVE,
        icon: 'star',
        difficulty: QuestDifficulty.MEDIUM,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      },
      {
        id: 'daily_5',
        title: 'Khám phá truyện mới',
        description: 'Đọc 1 chapter của truyện chưa từng đọc',
        type: QuestType.DISCOVER_NEW,
        target: 1,
        current: 0,
        reward: {
          type: RewardType.COINS,
          amount: 75,
          description: '75 xu'
        },
        status: QuestStatus.ACTIVE,
        icon: 'compass',
        difficulty: QuestDifficulty.MEDIUM,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      }
    ];

    // Mock weekly quests data
    const weeklyQuests: WeeklyQuest[] = [
      {
        id: 'weekly_1',
        title: 'Đọc 20 chapter trong tuần',
        description: 'Hoàn thành 20 chapter trong 7 ngày',
        type: QuestType.WEEKLY_READING,
        target: 20,
        current: 8,
        reward: {
          type: RewardType.BADGE,
          amount: 1,
          item: 'weekly_reader',
          description: 'Huy hiệu "Độc giả tuần"'
        },
        status: QuestStatus.ACTIVE,
        icon: 'trophy',
        difficulty: QuestDifficulty.HARD,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        weekNumber: 1,
        resetDay: 'Monday'
      },
      {
        id: 'weekly_2',
        title: 'Hoàn thành 1 bộ truyện',
        description: 'Đọc hết tất cả chapter của 1 bộ truyện',
        type: QuestType.COMPLETE_SERIES,
        target: 1,
        current: 0,
        reward: {
          type: RewardType.PREMIUM_DAYS,
          amount: 3,
          description: '3 ngày Premium'
        },
        status: QuestStatus.ACTIVE,
        icon: 'award',
        difficulty: QuestDifficulty.LEGENDARY,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        weekNumber: 1,
        resetDay: 'Monday'
      }
    ];

    // Mock user stats
    const userStats: UserQuestStats = {
      totalCompleted: 45,
      streakDays: 7,
      weeklyCompleted: 12,
      monthlyCompleted: 38,
      totalRewardsEarned: 2450,
      level: 8,
      experience: 1250,
      nextLevelExp: 1500
    };

    this.dailyQuestsSubject.next(dailyQuests);
    this.weeklyQuestsSubject.next(weeklyQuests);
    this.userStatsSubject.next(userStats);
  }

  getDailyQuests(): Observable<DailyQuest[]> {
    return this.dailyQuests$;
  }

  getWeeklyQuests(): Observable<WeeklyQuest[]> {
    return this.weeklyQuests$;
  }

  getUserStats(): Observable<UserQuestStats | null> {
    return this.userStats$;
  }

  completeQuest(questId: string): Observable<boolean> {
    const dailyQuests = this.dailyQuestsSubject.value;
    const weeklyQuests = this.weeklyQuestsSubject.value;
    
    // Find and complete daily quest
    const dailyQuest = dailyQuests.find(q => q.id === questId);
    if (dailyQuest && dailyQuest.status === QuestStatus.ACTIVE) {
      dailyQuest.status = QuestStatus.COMPLETED;
      dailyQuest.current = dailyQuest.target;
      dailyQuest.completedAt = new Date();
      this.dailyQuestsSubject.next([...dailyQuests]);
      return of(true);
    }

    // Find and complete weekly quest
    const weeklyQuest = weeklyQuests.find(q => q.id === questId);
    if (weeklyQuest && weeklyQuest.status === QuestStatus.ACTIVE) {
      weeklyQuest.status = QuestStatus.COMPLETED;
      weeklyQuest.current = weeklyQuest.target;
      weeklyQuest.completedAt = new Date();
      this.weeklyQuestsSubject.next([...weeklyQuests]);
      return of(true);
    }

    return of(false);
  }

  updateQuestProgress(questId: string, progress: number): Observable<boolean> {
    const dailyQuests = this.dailyQuestsSubject.value;
    const weeklyQuests = this.weeklyQuestsSubject.value;
    
    // Update daily quest progress
    const dailyQuest = dailyQuests.find(q => q.id === questId);
    if (dailyQuest && dailyQuest.status === QuestStatus.ACTIVE) {
      dailyQuest.current = Math.min(progress, dailyQuest.target);
      if (dailyQuest.current >= dailyQuest.target) {
        dailyQuest.status = QuestStatus.COMPLETED;
        dailyQuest.completedAt = new Date();
      }
      this.dailyQuestsSubject.next([...dailyQuests]);
      return of(true);
    }

    // Update weekly quest progress
    const weeklyQuest = weeklyQuests.find(q => q.id === questId);
    if (weeklyQuest && weeklyQuest.status === QuestStatus.ACTIVE) {
      weeklyQuest.current = Math.min(progress, weeklyQuest.target);
      if (weeklyQuest.current >= weeklyQuest.target) {
        weeklyQuest.status = QuestStatus.COMPLETED;
        weeklyQuest.completedAt = new Date();
      }
      this.weeklyQuestsSubject.next([...weeklyQuests]);
      return of(true);
    }

    return of(false);
  }

  claimReward(questId: string): Observable<boolean> {
    // Mock reward claiming logic
    console.log(`Claiming reward for quest: ${questId}`);
    return of(true);
  }
}
