using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Quest
{
    public class UserQuest
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, Column("userid")]
        public int UserID { get; set; }

        [Required, Column("questtemplateid")]
        public int QuestTemplateID { get; set; }

        [Column("current")]
        public int Current { get; set; } = 0;

        [Required, <PERSON><PERSON>ength(20), Column("status")]
        public string Status { get; set; } = "active";

        [Required, Column("expiresat")]
        public DateTime ExpiresAt { get; set; }

        [Column("completedat")]
        public DateTime? CompletedAt { get; set; }

        [Column("createat")]
        public DateTime CreateAt { get; set; } = DateTime.UtcNow;

        [Column("updateat")]
        public DateTime UpdateAt { get; set; } = DateTime.UtcNow;

        [Column("weeknumber")]
        public int? WeekNumber { get; set; }

        [MaxLength(10), Column("resetday")]
        public string? ResetDay { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual QuestTemplate QuestTemplate { get; set; } = null!;
        public virtual ICollection<UserQuestProgress> ProgressHistory { get; set; } = new List<UserQuestProgress>();
    }

    public enum QuestStatus
    {
        active,
        completed,
        expired,
        locked
    }
}
