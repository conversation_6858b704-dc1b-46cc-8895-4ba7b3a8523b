import { Injectable } from '@angular/core';
import { NavigationStart, Router } from '@angular/router';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  public tasks: string[] = [];
  private ignoredRoutes = ['/comic/topview'];
  public tasks$: Subject<string[]> = new Subject<string[]>();
  constructor(private router: Router) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.ClearAll();
      }
    });
  }

  TaskAdd(task: string) {

    if (this.ignoredRoutes.findIndex((r) => task.includes(r)) !== -1) {
      return;
    }
    this.tasks.push(task);
    this.tasks$.next(this.tasks);
  }
  ClearAll() {
    this.tasks = [];
    this.tasks$.next(this.tasks);
  }

  TaskRemove(task: string) {
    this.tasks = this.tasks.filter((t: string) => t !== task);
    this.tasks$.next(this.tasks);
  }

}
