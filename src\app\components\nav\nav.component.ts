import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
  PLATFORM_ID,
  ViewChild
} from '@angular/core';
import { CommonModule, isPlatformServer } from '@angular/common';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { GenreCatagoriesComponent } from '@components/common/genre-catagories/genre-catagories.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { FadeInDirective } from '@directives/fade-in.directive';
import { Genre, IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { ComicService } from '@services/comic.service';
import { ImageService } from '@services/image.service';
import { PopupService } from '@services/popup.service';
import { ThemeService } from '@services/theme.service';
import { ListSearchComicComponent } from './list-search-comic/list-search-comic.component';
import { NotifyComponent } from '../common/notify/notify.component';
import { OptimizedBaseComponent } from '../common/base/optimized-base.component';
import { UserMenuComponent, UserMenuItem, UserStats } from '../user-menu/user-menu.component';
import { filter } from 'rxjs/operators';
@Component({
  selector: 'app-nav',
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, NotifyComponent, RouterLink, ListSearchComicComponent, GenreCatagoriesComponent, UserMenuComponent],
})

export class NavComponent extends OptimizedBaseComponent implements OnInit, AfterViewInit, OnDestroy {


  // Component state
  listGenres: Genre[] = [];
  searchText = '';
  maxHeight = 0;
  avatar = '';
  user?: IUser;
  showSidebar = false;
  showAccount = false;

  // User menu properties
  userStats: UserStats | null = null;
  notificationCount = 0;
  userMenuConfig = {
    showPreview: true,
    showQuickActions: true
  };

  // Performance optimizations
  private readonly SIDEBAR_ITEM_HEIGHT = 40;
  private debouncedToggleSidebar: Function;

  @ViewChild('dropdownNavbar') dropdownNavbar!: ElementRef;

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private comicService: ComicService,
    private accountService: AccountService,
    private router: Router,
    private imageService: ImageService,
    public themeService: ThemeService,
    public popupService: PopupService
  ) {
    super(cdr, platformId);
    this.debouncedToggleSidebar = this.debounce(this.performToggleSidebar.bind(this), 100);
  }

  // Computed properties for better performance
  get isAuthenticated(): boolean {
    return this.accountService.isAuthenticated();
  }

  get hasGenres(): boolean {
    return this.listGenres.length > 0;
  }

  get isDarkMode(): boolean {
    return this.themeService.IsDark();
  }

  get userDisplayName(): string {
    return this.user?.firstName || 'Khách';
  }

  get shouldShowNotifications(): boolean {
    return !!this.user;
  }

  // TrackBy functions for ngFor optimization
  trackByGenreId = (index: number, genre: Genre): number => {
    return genre.id;
  };
  ngOnInit(): void {
    this.loadGenres();
    this.setupRouterSubscription();
    this.initializeUser();
  }

  ngAfterViewInit(): void {
    this.runInBrowser(() => {
      this.calculateMaxHeight();
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  onFeedbackClick(): void {
    this.popupService.showFeedback();
  }

  private loadGenres(): void {
    this.addSubscription(
      this.comicService.getGenres().subscribe((genres) => {
        this.listGenres = genres;
        this.safeMarkForCheck();
      })
    );
  }

  private setupRouterSubscription(): void {
    this.addSubscription(
      this.router.events
        .pipe(
          filter(event => event instanceof NavigationEnd),
          this.takeUntilDestroy()
        )
        .subscribe(() => {
          this.refresh();
        })
    );
  }

  private initializeUser(): void {
    if (!isPlatformServer(this.platformId)) {
      this.refresh();
    }
  }

  private calculateMaxHeight(): void {
    this.maxHeight = 0;
    for (const child of this.dropdownNavbar.nativeElement.children) {
      this.maxHeight += this.SIDEBAR_ITEM_HEIGHT;
    }
  }
  refresh(): void {
    if (this.isAuthenticated) {
      this.user = this.accountService.GetUser();
      if (this.user?.avatar) {
        this.avatar = this.user.avatar;
      }

      this.addSubscription(
        this.imageService.imageUrl$.subscribe((url) => {
          if (url) {
            this.avatar = url;
            this.safeMarkForCheck();
          }
        })
      );
    }
  }

  onLoginClick(): void {
    this.showAccount = false;
    this.router.navigate(['auth/login']);
  }

  onRegisterClick(): void {
    this.showAccount = false;
    this.router.navigate(['auth/register']);
  }

  onLogoutClick(): void {
    this.accountService.Logout();
    this.showAccount = false;
  }

  onUserClick(): void {
    this.showAccount = !this.showAccount;
  }

  toggleSidebar(): void {
    this.debouncedToggleSidebar();
  }

  private performToggleSidebar(): void {
    this.showSidebar = !this.showSidebar;
    this.runInBrowser(() => {
      const height = this.showSidebar ? `${this.maxHeight}px` : '0px';
      this.dropdownNavbar.nativeElement.style.height = height;
    });
    this.safeMarkForCheck();
  }
  async onSettingClick(): Promise<void> {
    // this.popupService.showSetting();
  }

  // User menu event handlers
  onUserMenuToggle(isOpen: boolean): void {
    this.showAccount = isOpen;
    this.safeMarkForCheck();
  }

  onUserMenuItemClick(item: UserMenuItem): void {
    console.log('Menu item clicked:', item);
    // Handle menu item clicks if needed
  }

  onUserQuickAction(action: string): void {
    console.log('Quick action:', action);
    // Handle quick actions if needed
    switch (action) {
      case 'notifications':
        // Handle notifications
        break;
      case 'favorites':
        // Handle favorites
        break;
      case 'history':
        // Handle history
        break;
    }
  }

  // Guest user event handlers
  onGuestLogin(): void {
    this.onLoginClick();
  }

  onGuestRegister(): void {
    this.onRegisterClick();
  }
}
