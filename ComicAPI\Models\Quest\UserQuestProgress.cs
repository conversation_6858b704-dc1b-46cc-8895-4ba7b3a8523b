using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Quest
{
    public class UserQuestProgress
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, <PERSON>umn("userquestid")]
        public int UserQuestID { get; set; }

        [Column("progress")]
        public int Progress { get; set; } = 0;

        [Column("lastupdated")]
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual UserQuest UserQuest { get; set; } = null!;
    }
}
