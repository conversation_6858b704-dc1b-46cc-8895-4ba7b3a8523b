using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using System.Threading;
using System.Threading.Tasks;
namespace ComicAPI.Services
{

    public class QueueService
    {
        private readonly ConcurrentQueue<Func<IServiceProvider, CancellationToken, Task>> _workItems = new();
        private readonly SemaphoreSlim _signal = new(0);


        public void QueueTask(Func<IServiceProvider, CancellationToken, Task> task)
        {
            if (task == null)
            {
                throw new ArgumentNullException(nameof(task));
            }

            _workItems.Enqueue(task);
            _signal.Release();
        }

        public async Task<Func<IServiceProvider, CancellationToken, Task>?> DequeueAsync(CancellationToken cancellationToken)
        {
            await _signal.WaitAsync(cancellationToken);
            _workItems.TryDequeue(out var workItem);
            return workItem;
        }
    }

}
