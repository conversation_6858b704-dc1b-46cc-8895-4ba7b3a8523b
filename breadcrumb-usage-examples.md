# Breadcrumb Component - Style Usage Examples

## 📋 Tổng quan

Breadcrumb component hiện đã hỗ trợ 4 style background khác nhau:

- **`transparent`**: Background trong suốt với hiệu ứng backdrop blur
- **`white`**: Background trắng cho cả light và dark mode
- **`black`**: Background đen cho cả light và dark mode  
- **`default`**: Background xám mặc định thích ứng với theme

## 🎨 Cách sử dụng

### 1. Transparent Background (Mặc định)
```html
<app-breadcrumb
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Xếp hạng', url: '/rank' },
    { label: 'Chi tiết', url: '' }
  ]"
  style="transparent"
>
</app-breadcrumb>
```

**Đặc điểm:**
- Background hoàn toàn trong suốt
- Hiệu ứng backdrop blur cho icon và current page
- <PERSON><PERSON> hợp với overlay trên hình ảnh hoặc background phức tạp

### 2. White Background
```html
<app-breadcrumb
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Truyện tranh', url: '/comics' },
    { label: 'One Piece', url: '' }
  ]"
  style="white"
>
</app-breadcrumb>
```

**Đặc điểm:**
- Background trắng cố định cho cả light và dark mode
- Text màu xám đậm để đảm bảo contrast tốt
- Phù hợp khi cần breadcrumb nổi bật trên background tối

### 3. Black Background
```html
<app-breadcrumb
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Thể loại', url: '/genres' },
    { label: 'Action', url: '' }
  ]"
  style="black"
>
</app-breadcrumb>
```

**Đặc điểm:**
- Background đen cố định cho cả light và dark mode
- Text màu xám nhạt để đảm bảo contrast tốt
- Phù hợp khi cần breadcrumb nổi bật trên background sáng

### 4. Default Background
```html
<app-breadcrumb
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Tìm kiếm', url: '/search' },
    { label: 'Kết quả', url: '' }
  ]"
  style="default"
>
</app-breadcrumb>
```

**Đặc điểm:**
- Background xám nhạt trong light mode, xám đậm trong dark mode
- Tự động thích ứng với theme hiện tại
- Phù hợp cho sử dụng chung trong hầu hết các trang

## 🎯 Khi nào sử dụng style nào?

### Transparent
- ✅ Overlay trên hero images
- ✅ Floating navigation
- ✅ Khi muốn breadcrumb hòa hợp với background

### White
- ✅ Dark themed pages
- ✅ Khi cần contrast cao với background tối
- ✅ Print-friendly layouts

### Black
- ✅ Light themed pages với background sáng
- ✅ Khi muốn tạo điểm nhấn mạnh mẽ
- ✅ Modern, minimalist designs

### Default
- ✅ Sử dụng chung trong hệ thống
- ✅ Khi muốn tự động thích ứng theme
- ✅ Content pages thông thường

## 🔧 Technical Details

### CSS Classes được áp dụng:
- `.breadcrumb-transparent`
- `.breadcrumb-white` 
- `.breadcrumb-black`
- `.breadcrumb-default`

### Features:
- Responsive design cho mobile
- Backdrop blur effects (transparent style)
- Smooth transitions
- Accessibility support
- SEO-friendly với schema.org markup
