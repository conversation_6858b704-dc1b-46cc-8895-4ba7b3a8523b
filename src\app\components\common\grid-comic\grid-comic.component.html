<div class="grid-container">
  <!-- Selection Controls -->
  <div *ngIf="showCheckboxes" class="selection-controls">
    <button
      (click)="selectAllComics(!isSelectAll)"
      class="select-all-btn"
      [class.active]="selectedComics.size"
      title="Select All"
    >
      Ch<PERSON><PERSON> tất cả
      <span class="count-badge" [class.active]="selectedComics.size">
        {{ selectedComics.size }}
      </span>
    </button>

    <button
      (click)="deleteSelectedComics()"
      class="delete-btn"
      [class.disabled]="selectedComics.size === 0"
      title="Delete Selected"
    >
      <svg class="delete-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path d="M4 7h16M10 11v6M14 11v6M5 7l1 12a2 2 0 002 2h8a2 2 0 002-2l1-12M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3"/>
      </svg>
      Xóa
    </button>
  </div>

  <!-- Header -->
  <header class="grid-header">
    <div class="title-section">
      <ng-content></ng-content>
      <h2 class="block-title">{{ title }}</h2>
    </div>

    <!-- Grid Type Switch -->
    <div class="grid-switch">
      <div class="switch-track">
        <div class="switch-thumb" [class.grid-mode]="girdType === 0" [class.list-mode]="girdType === 1"></div>
      </div>

      <button
        class="switch-btn"
        [class.active]="girdType === 1"
        (click)="ChangeGridType($event.target, 1)"
        title="Xem dạng danh sách"
        aria-label="Chuyển sang chế độ xem danh sách"
      >
        <svg class="switch-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <rect x="3" y="4" width="18" height="4" rx="1"/>
          <rect x="3" y="10" width="18" height="4" rx="1"/>
          <rect x="3" y="16" width="18" height="4" rx="1"/>
        </svg>
      </button>

      <button
        class="switch-btn"
        [class.active]="girdType === 0"
        (click)="ChangeGridType($event.target, 0)"
        title="Xem dạng lưới"
        aria-label="Chuyển sang chế độ xem lưới"
      >
        <svg class="switch-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <rect x="3" y="3" width="7" height="7" rx="1"/>
          <rect x="14" y="3" width="7" height="7" rx="1"/>
          <rect x="3" y="14" width="7" height="7" rx="1"/>
          <rect x="14" y="14" width="7" height="7" rx="1"/>
        </svg>
      </button>
    </div>
  </header>
</div>
<!-- Grid Content -->
<ng-container *ngIf="girdType === 0; else listView">
  <div class="{{ _class }}">
    <div *ngFor="let comic of listComics; let i = index; trackBy: trackById;" class="grid-item">
      <input
        *ngIf="showCheckboxes"
        [id]="'checkbox-' + i"
        class="item-checkbox"
        type="checkbox"
        (change)="toggleSelectComic(i)"
        [checked]="selectedComics.has(i)"
      />
      <app-comic-card
        [comic]="comic"
        (clickEvent)="clickEvent.emit([comic.id])"
        [eventName]="eventName"
        (comicHover)="onHoverComic($event)"
      />
    </div>
  </div>
</ng-container>

<ng-template #listView>
  <div class="list-grid">
    <div *ngFor="let comic of listComics; let i = index; trackBy: trackById;" class="list-item-v2">
      <input
        *ngIf="showCheckboxes"
        [id]="'checkbox-list-' + i"
        class="item-checkbox"
        type="checkbox"
        (change)="toggleSelectComic(i)"
        [checked]="selectedComics.has(i)"
      />
      <app-comic-card-v2 [comic]="comic" />
    </div>
  </div>
</ng-template>

<!-- Comic Detail Popup -->
<app-popup-detail-comic
  *ngIf="hoverComic && isBrowser"
  class="comic-popup"
  [comic]="hoverComic"
/>
