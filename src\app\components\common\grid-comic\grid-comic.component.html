<div class="mt-3 mb-3">
  <div *ngIf="showCheckboxes" class="flex gap-2 justify-end mb-3">
    <!-- <button >Select All</button> -->
    <button
      (click)="selectAllComics(!isSelectAll)"
      type="button"
      class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-center rounded-lg focus:outline-none dark:bg-primary-100 dark:hover:bg-primary-200 dark:text-white"
      [ngClass]="{
        'bg-primary-100 text-white ': selectedComics.size,
        'text-secondary-100 bg-gray-200 hover:bg-gray-300': !selectedComics.size
      }"
      title="Select All"
    >
      Ch<PERSON><PERSON> t<PERSON>t cả
      <span
        class="inline-flex items-center justify-center w-4 h-4 ms-2 text-xs font-semibold text-pbg-primary-50 rounded-full"
        [ngClass]="{
          'bg-primary-50 text-white': selectedComics.size,
          'bg-white text-secondary-100': !selectedComics.size
        }"
      >
        <p class="p-1 text-xs">{{ selectedComics.size }}</p>
      </span>
    </button>
    <button
      type="button"
      [ngClass]="{ disabled: selectedComics.size === 0 }"
      (click)="deleteSelectedComics()"
      title="Delete Selected"
      class="text-primary-100 font-medium focus:ring-4 focus:outline-none focus:ring-primary-50 rounded-lg text-sm px-3 py-2.5 text-center inline-flex items-center me-2"
    >
      <svg
        class="w-3.5 h-3.5 me-2"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" />
        <line x1="4" y1="7" x2="20" y2="7" />
        <line x1="10" y1="11" x2="10" y2="17" />
        <line x1="14" y1="11" x2="14" y2="17" />
        <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12" />
        <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3" />
      </svg>

      Xóa
    </button>
  </div>

  <div class="flex flex-row justify-between gap-6">
    <div
      class="flex items-center px-1 rounded-sm dark:text-white"
    >
      <ng-content> </ng-content>

      <p class="block-title">{{ title }}</p>
    </div>
    <!-- Modern Grid Type Switch -->
    <div class="flex justify-end">
      <div class="grid-switch-container">
        <!-- Switch Background -->
        <div class="switch-background">
          <div
            class="switch-indicator"
            [class.switch-indicator-grid]="girdType === 0"
            [class.switch-indicator-list]="girdType === 1"
          ></div>
        </div>

        <!-- List View Button -->
        <button
          type="button"
          class="switch-button"
          [class.switch-button-active]="girdType === 1"
          (click)="ChangeGridType($event.target, 1)"
          title="Xem dạng danh sách"
          aria-label="Chuyển sang chế độ xem danh sách"
        >
          <svg class="switch-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="3" y="4" width="18" height="4" rx="1"/>
            <rect x="3" y="10" width="18" height="4" rx="1"/>
            <rect x="3" y="16" width="18" height="4" rx="1"/>
          </svg>
        </button>

        <!-- Grid View Button -->
        <button
          type="button"
          class="switch-button"
          [class.switch-button-active]="girdType === 0"
          (click)="ChangeGridType($event.target, 0)"
          title="Xem dạng lưới"
          aria-label="Chuyển sang chế độ xem lưới"
        >
          <svg class="switch-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="3" y="3" width="7" height="7" rx="1"/>
            <rect x="14" y="3" width="7" height="7" rx="1"/>
            <rect x="3" y="14" width="7" height="7" rx="1"/>
            <rect x="14" y="14" width="7" height="7" rx="1"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
<ng-container *ngIf="girdType === 0; else grid2">
  <div class="{{ _class }}">
    <div *ngFor="let comic of listComics; let i = index; trackBy: trackById;">
      <input
        *ngIf="showCheckboxes"
        [id]="'checkbox-' + i"
        class="absolute z-10 m-2 w-4 h-4 rounded-lg box-border border-2 border-white"
        type="checkbox"
        (change)="toggleSelectComic(i)"
        [checked]="selectedComics.has(i)"
      />

      <app-comic-card
        [comic]="comic"
        (clickEvent)="clickEvent.emit([comic.id])"
        [eventName]="eventName"
        (comicHover)="onHoverComic($event)"
      ></app-comic-card>
    </div>
  </div>
</ng-container>
<ng-template #grid2>
  <div class="grid gap-2 grid-cols-1 xl:grid-cols-2">
    <div *ngFor="let comic of listComics; let i = index; trackBy: trackById;" class="px-2 lg:px-0">
      <input
        *ngIf="showCheckboxes"
        [id]="'checkbox-grid2-' + i"
        class="absolute z-10 m-2 w-4 h-4 rounded-lg box-border border-2 border-white"
        type="checkbox"
        (change)="toggleSelectComic(i)"
        [checked]="selectedComics.has(i)"
      />

      <app-comic-card-v2 [comic]="comic"></app-comic-card-v2>
    </div>
  </div>
</ng-template>

<app-popup-detail-comic
  *ngIf="hoverComic && isBrowser"
  class="mt-2 z-20 fixed duration-100"
  [comic]="hoverComic"
>
</app-popup-detail-comic>
