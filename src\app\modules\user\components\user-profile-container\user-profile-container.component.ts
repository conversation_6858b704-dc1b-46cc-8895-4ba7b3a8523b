import { Component, OnInit, ChangeDetectionStrategy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive, Router } from '@angular/router';
import { AccountService } from '@services/account.service';
import { IUser } from '@schema';

interface NavigationItem {
  path: string;
  label: string;
  icon: string;
  description: string;
  badge?: number;
}

@Component({
  selector: 'app-user-profile-container',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive],
  templateUrl: './user-profile-container.component.html',
  styleUrl: './user-profile-container.component.scss',
})
export class UserProfileContainerComponent implements OnInit {
  private accountService = inject(AccountService);
  private router = inject(Router);

  user: IUser | null = null;
  isLoading = true;

  navigationItems: NavigationItem[] = [
    {
      path: '/tai-khoan/profile',
      label: 'Thông tin cá nhân',
      icon: 'user',
      description: 'Quản lý thông tin tài khoản'
    },
    {
      path: '/tai-khoan/favorites',
      label: 'Truyện yêu thích',
      icon: 'heart',
      description: 'Danh sách truyện đã lưu',
      badge: 0
    },
    {
      path: '/tai-khoan/history',
      label: 'Lịch sử đọc',
      icon: 'clock',
      description: 'Truyện đã đọc gần đây'
    },
    {
      path: '/tai-khoan/stats',
      label: 'Thống kê',
      icon: 'chart',
      description: 'Thống kê hoạt động đọc truyện'
    },
    {
      path: '/tai-khoan/reviews',
      label: 'Đánh giá',
      icon: 'star',
      description: 'Truyện đã đánh giá'
    },
    {
      path: '/tai-khoan/lists',
      label: 'Danh sách đọc',
      icon: 'list',
      description: 'Danh sách tự tạo'
    },
    {
      path: '/tai-khoan/achievements',
      label: 'Thành tích',
      icon: 'trophy',
      description: 'Huy hiệu và thành tựu'
    },
    {
      path: '/tai-khoan/settings',
      label: 'Cài đặt',
      icon: 'settings',
      description: 'Tùy chỉnh giao diện và thông báo'
    }
  ];

  ngOnInit() {
    this.loadUserData();
    
  }

  private loadUserData() {
    this.accountService.GetUserInfo().subscribe({
      next: (response: any) => {
        

        if (response.status) {
          this.user = response.data;
          this.updateNavigationBadges();
        }
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  private updateNavigationBadges() {
    // Update badges based on user data
    // This would be implemented based on actual data structure
  }

  trackByPath(index: number, item: NavigationItem): string {
    return item.path;
  }
}
