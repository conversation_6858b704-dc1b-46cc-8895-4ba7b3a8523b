using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Quest
{
    public class UserRewardInventory
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, <PERSON>umn("userid")]
        public int UserID { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(50), <PERSON><PERSON><PERSON>("rewardtype")]
        public string RewardType { get; set; } = string.Empty;

        [Column("amount")]
        public int Amount { get; set; } = 0;

        [MaxLength(100), Column("item")]
        public string? Item { get; set; }

        [Column("earnedat")]
        public DateTime EarnedAt { get; set; } = DateTime.UtcNow;

        [Column("questid")]
        public int? QuestID { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual UserQuest? Quest { get; set; }
    }
}
