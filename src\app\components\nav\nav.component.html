<header class="nav-header">
  <div class="nav-header-container">
    <!-- Logo or brand name -->
    <div class="nav-logo-section">
      <a [routerLink]="['/']" class="nav-logo-link">
        <img
          loading="eager"
          class="nav-logo-image"
          src="/30-4-logo.png"
          alt="logo"
        />
      </a>
    </div>
    <div *ngIf="isBrowser" class="nav-actions">
      <app-list-search-comic />

      <!-- Dark Mode Toggler -->
      <div class="theme-toggle-container">
        <input
          aria-labelledby="theme-toggle"
          aria-label="Toggle theme"
          id="theme-toggle"
          (change)="themeService.ToggleTheme()"
          type="checkbox"
          class="theme-toggle-input"
        />
        <span
          [ngClass]="{
            'theme-toggle-slider-light': !isDarkMode,
            'theme-toggle-slider-dark': isDarkMode
          }"
          class="theme-toggle-slider"
        >
          <span class="theme-icon-light">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7.99992 12.6666C10.5772 12.6666 12.6666 10.5772 12.6666 7.99992C12.6666 5.42259 10.5772 3.33325 7.99992 3.33325C5.42259 3.33325 3.33325 5.42259 3.33325 7.99992C3.33325 10.5772 5.42259 12.6666 7.99992 12.6666Z"
              ></path>
              <path
                d="M8.00008 15.3067C7.63341 15.3067 7.33342 15.0334 7.33342 14.6667V14.6134C7.33342 14.2467 7.63341 13.9467 8.00008 13.9467C8.36675 13.9467 8.66675 14.2467 8.66675 14.6134C8.66675 14.9801 8.36675 15.3067 8.00008 15.3067ZM12.7601 13.4267C12.5867 13.4267 12.4201 13.3601 12.2867 13.2334L12.2001 13.1467C11.9401 12.8867 11.9401 12.4667 12.2001 12.2067C12.4601 11.9467 12.8801 11.9467 13.1401 12.2067L13.2267 12.2934C13.4867 12.5534 13.4867 12.9734 13.2267 13.2334C13.1001 13.3601 12.9334 13.4267 12.7601 13.4267ZM3.24008 13.4267C3.06675 13.4267 2.90008 13.3601 2.76675 13.2334C2.50675 12.9734 2.50675 12.5534 2.76675 12.2934L2.85342 12.2067C3.11342 11.9467 3.53341 11.9467 3.79341 12.2067C4.05341 12.4667 4.05341 12.8867 3.79341 13.1467L3.70675 13.2334C3.58008 13.3601 3.40675 13.4267 3.24008 13.4267ZM14.6667 8.66675H14.6134C14.2467 8.66675 13.9467 8.36675 13.9467 8.00008C13.9467 7.63341 14.2467 7.33342 14.6134 7.33342C14.9801 7.33342 15.3067 7.63341 15.3067 8.00008C15.3067 8.36675 15.0334 8.66675 14.6667 8.66675ZM1.38675 8.66675H1.33341C0.966748 8.66675 0.666748 8.36675 0.666748 8.00008C0.666748 7.63341 0.966748 7.33342 1.33341 7.33342C1.70008 7.33342 2.02675 7.63341 2.02675 8.00008C2.02675 8.36675 1.75341 8.66675 1.38675 8.66675ZM12.6734 3.99341C12.5001 3.99341 12.3334 3.92675 12.2001 3.80008C11.9401 3.54008 11.9401 3.12008 12.2001 2.86008L12.2867 2.77341C12.5467 2.51341 12.9667 2.51341 13.2267 2.77341C13.4867 3.03341 13.4867 3.45341 13.2267 3.71341L13.1401 3.80008C13.0134 3.92675 12.8467 3.99341 12.6734 3.99341ZM3.32675 3.99341C3.15341 3.99341 2.98675 3.92675 2.85342 3.80008L2.76675 3.70675C2.50675 3.44675 2.50675 3.02675 2.76675 2.76675C3.02675 2.50675 3.44675 2.50675 3.70675 2.76675L3.79341 2.85342C4.05341 3.11342 4.05341 3.53341 3.79341 3.79341C3.66675 3.92675 3.49341 3.99341 3.32675 3.99341ZM8.00008 2.02675C7.63341 2.02675 7.33342 1.75341 7.33342 1.38675V1.33341C7.33342 0.966748 7.63341 0.666748 8.00008 0.666748C8.36675 0.666748 8.66675 0.966748 8.66675 1.33341C8.66675 1.70008 8.36675 2.02675 8.00008 2.02675Z"
              ></path>
            </svg>
          </span>
          <span class="theme-icon-dark">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.3533 10.62C14.2466 10.44 13.9466 10.16 13.1999 10.2933C12.7866 10.3667 12.3666 10.4 11.9466 10.38C10.3933 10.3133 8.98659 9.6 8.00659 8.5C7.13993 7.53333 6.60659 6.27333 6.59993 4.91333C6.59993 4.15333 6.74659 3.42 7.04659 2.72666C7.33993 2.05333 7.13326 1.7 6.98659 1.55333C6.83326 1.4 6.47326 1.18666 5.76659 1.48C3.03993 2.62666 1.35326 5.36 1.55326 8.28666C1.75326 11.04 3.68659 13.3933 6.24659 14.28C6.85993 14.4933 7.50659 14.62 8.17326 14.6467C8.27993 14.6533 8.38659 14.66 8.49326 14.66C10.7266 14.66 12.8199 13.6067 14.1399 11.8133C14.5866 11.1933 14.4666 10.8 14.3533 10.62Z"
              ></path>
            </svg>
          </span>
        </span>
      </div>
      <!-- Dark Mode Toggler -->

      <app-notify *ngIf="shouldShowNotifications" />

      <div
        *ngIf="!isAuthenticated"
        class="guest-user-menu"
        (appClickOutside)="showAccount = false"
      >
        <button
          (click)="onUserClick()"
          aria-label="Avatar User"
          class="guest-user-button"
        >
          <svg
            class="guest-user-icon"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        </button>
        <div
          [ngClass]="{
            'guest-dropdown-hidden': !showAccount,
            'guest-dropdown-visible': showAccount
          }"
          appFadeIn
          [duration]="300"
          class="guest-dropdown"
        >
          <div class="guest-dropdown-content">
            <span class="guest-user-info">
              <svg
                class="guest-avatar-icon"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
              <div class="guest-username">
                {{ userDisplayName }}
              </div>
              <span class="guest-divider"></span>
            </span>
            <button class="auth-button auth-button-login" (click)="onLoginClick()">
              Đăng nhập
            </button>
            <button class="auth-button auth-button-register" (click)="onRegisterClick()">
              Đăng ký
            </button>
          </div>
        </div>
        <!-- <div class="fixed bg-black opacity-50 top-0 left-0 bottom-0 right-0 z-0"></div> -->
      </div>

      <div
        *ngIf="isAuthenticated"
        (click)="onUserClick()"
        class="authenticated-user-menu"
      >
        <img
          loading="lazy"
          class="user-avatar"
          [src]="avatar"
          onerror="this.src='/default_avatar.jpg'"
          alt="user photo"
        />
        <div class="user-dropdown-container" [ngClass]="{ 'user-dropdown-hidden': !showAccount }">
          <div
            appFadeIn
            class="user-dropdown"
          >
            <div class="user-info-section">
              <span class="user-display-name">
                {{ userDisplayName }}
              </span>
            </div>
            <ul class="user-menu-list">
              <li>
                <a
                  [routerLink]="['/tai-khoan']"
                  class="user-menu-item"
                  >Trang cá nhân</a
                >
              </li>
              <li>
                <a
                  [routerLink]="['/theo-doi']"
                  class="user-menu-item"
                  >Theo dõi</a
                >
              </li>
              <li>
                <button
                  (click)="onSettingClick()"
                  class="user-menu-button"
                  >Cài đặt</button
                >
              </li>
              <li>
                <a
                  href="/"
                  (click)="onLogoutClick()"
                  class="user-menu-item"
                  >Đăng xuất</a
                >
              </li>
            </ul>
          </div>
          <div class="user-dropdown-overlay"></div>
        </div>
      </div>
    </div>
  </div>
</header>
<!-- Navigation links -->

<nav class="nav-main">
  <div class="nav-mobile-header">
    <div class="nav-mobile-home">
      <svg
        class="nav-mobile-home-icon"
        width="14"
        height="14"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" />
        <polyline points="5 12 3 12 12 3 21 12 19 12" />
        <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
        <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />
      </svg>
      <span class="nav-mobile-home-text">
        <a [routerLink]="['/']">Trang chủ</a>
      </span>
    </div>

    <div class="nav-mobile-toggle" (click)="toggleSidebar()">
      <svg
        *ngIf="!showSidebar"
        class="nav-toggle-icon"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16M4 18h7"
        />
      </svg>
      <svg
        *ngIf="showSidebar"
        class="nav-toggle-icon"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    </div>
  </div>
  <ul class="nav-bar-ul container hidden md:flex">
    <li class="nav-bar-li-pc">
      <a
        aria-label="Trang chủ"
        routerLink="/"
        class="px-2 lg:px-3 h-full hover:bg-white hover:fill-black items-center flex fill-white rounded-t-lg"
        ><svg
          xmlns="http://www.w3.org/2000/svg"
          height="16"
          width="18"
          viewBox="0 0 576 512"
        >
          <path
            d="M575.8 255.5c0 18-15 32.1-32 32.1h-32l.7 160.2c0 2.7-.2 5.4-.5 8.1V472c0 22.1-17.9 40-40 40H456c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1H416 392c-22.1 0-40-17.9-40-40V448 384c0-17.7-14.3-32-32-32H256c-17.7 0-32 14.3-32 32v64 24c0 22.1-17.9 40-40 40H160 128.1c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2H104c-22.1 0-40-17.9-40-40V360c0-.9 0-1.9 .1-2.8V287.6H32c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z"
          ></path>
        </svg>
      </a>
    </li>
    <li class="nav-bar-li-pc">
      <a
        [routerLink]="['/truyen-hot']"
        class="px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black hover:fill-black fill-white gap-1 rounded-t-lg"
      >
        <span class="flex items-center font-semibold gap-1">
          <svg
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            height="14"
            width="12.25"
            viewBox="0 0 448 512"
          >
            <path
              d="M159.3 5.4c7.8-7.3 19.9-7.2 27.7 .1c27.6 25.9 53.5 53.8 77.7 84c11-14.4 23.5-30.1 37-42.9c7.9-7.4 20.1-7.4 28 .1c34.6 33 63.9 76.6 84.5 118c20.3 40.8 33.8 82.5 33.8 111.9C448 404.2 348.2 512 224 512C98.4 512 0 404.1 0 276.5c0-38.4 17.8-85.3 45.4-131.7C73.3 97.7 112.7 48.6 159.3 5.4zM225.7 416c25.3 0 47.7-7 68.8-21c42.1-29.4 53.4-88.2 28.1-134.4c-4.5-9-16-9.6-22.5-2l-25.2 29.3c-6.6 7.6-18.5 7.4-24.7-.5c-16.5-21-46-58.5-62.8-79.8c-6.3-8-18.3-8.1-24.7-.1c-33.8 42.5-50.8 69.3-50.8 99.4C112 375.4 162.6 416 225.7 416z"
            />
          </svg>
          Hot</span
        >
      </a>
    </li>
    <li class="nav-bar-li-pc">
      <div
        class="cursor-pointer px-2 lg:px-3 h-full hover:fill-black fill-white items-center flex hover:text-black relative group gap-1"
      >
        <span class="flex items-center font-semibold px-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            height="14"
            width="14"
            viewBox="0 0 512 512"
          >
            <path
              d="M345 39.1L472.8 168.4c52.4 53 52.4 138.2 0 191.2L360.8 472.9c-9.3 9.4-24.5 9.5-33.9 .2s-9.5-24.5-.2-33.9L438.6 325.9c33.9-34.3 33.9-89.4 0-123.7L310.9 72.9c-9.3-9.4-9.2-24.6 .2-33.9s24.6-9.2 33.9 .2zM0 229.5V80C0 53.5 21.5 32 48 32H197.5c17 0 33.3 6.7 45.3 18.7l168 168c25 25 25 65.5 0 90.5L277.3 442.7c-25 25-65.5 25-90.5 0l-168-168C6.7 262.7 0 246.5 0 229.5zM144 144a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"
            />
          </svg>
          Thể loại</span
        >
        <div
          *ngIf="listGenres && isBrowser"
          class="absolute top-full left-0 hidden group-hover:flex z-[40] shadow-sm border border-solid dark:border-gray-950"
        >
          <app-genre-catagories
            [listGenres]="listGenres"
          ></app-genre-catagories>
        </div>
      </div>
    </li>
    <li class="nav-bar-li-pc">
      <a
        [routerLink]="['/theo-doi']"
        class="px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black hover:fill-black fill-white rounded-t-lg"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="14"
          width="15.75"
          viewBox="0 0 576 512"
        >
          <path
            d="M47.6 300.4L228.3 469.1c7.5 7 17.4 10.9 27.7 10.9s20.2-3.9 27.7-10.9l2.6-2.4C267.2 438.6 256 404.6 256 368c0-97.2 78.8-176 176-176c28.3 0 55 6.7 78.7 18.5c.9-6.5 1.3-13 1.3-19.6v-5.8c0-69.9-50.5-129.5-119.4-141C347 36.5 300.6 51.4 268 84L256 96 244 84c-32.6-32.6-79-47.5-124.6-39.9C50.5 55.6 0 115.2 0 185.1v5.8c0 41.5 17.2 81.2 47.6 109.5zM432 512a144 144 0 1 0 0-288 144 144 0 1 0 0 288zm16-208v48h48c8.8 0 16 7.2 16 16s-7.2 16-16 16H448v48c0 8.8-7.2 16-16 16s-16-7.2-16-16V384H368c-8.8 0-16-7.2-16-16s7.2-16 16-16h48V304c0-8.8 7.2-16 16-16s16 7.2 16 16z"
          />
        </svg>
        Theo dõi</a
      >
    </li>
    <li class="nav-bar-li-pc">
      <a
        [routerLink]="['/xep-hang']"
        class="px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black fill-white hover:fill-black rounded-t-lg"
        ><svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-4 h-4 mr-1"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z"
          ></path></svg
        >Xếp hạng</a
      >
    </li>
    <li class="nav-bar-li-pc">
      <a
        routerLink="/tim-truyen"
        class="px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black rounded-t-lg"
        ><svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="2.5"
          stroke="currentColor"
          class="w-4 h-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"
          ></path></svg
        >Tìm kiếm nâng cao</a
      >
    </li>
    <li class="nav-bar-li-pc">
      <a
        routerLink="lich-su"
        class="px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black hover:fill-black fill-white gap-1 rounded-t-lg"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="14"
          width="14"
          viewBox="0 0 512 512"
        >
          <path
            d="M75 75L41 41C25.9 25.9 0 36.6 0 57.9V168c0 13.3 10.7 24 24 24H134.1c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24V256c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65V152c0-13.3-10.7-24-24-24z"
          />
        </svg>
        Lịch sử</a
      >
    </li>
    <li class="nav-bar-li-pc">
      <span
        (click)="onFeedbackClick()"
        class="px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black hover:fill-black fill-white gap-1 rounded-t-lg cursor-pointer"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          fill="currentColor"
          class="size-5"
          version="1.1"
          id="Layer_1"
          viewBox="0 0 512 512"
          xml:space="preserve"
        >
          <g>
            <g>
              <path
                d="M461.913,72.348H50.087C21.236,72.348,0,96.108,0,122.435v267.13c0,27.618,22.469,50.087,50.087,50.087h411.826    c27.618,0,50.087-22.469,50.087-50.087v-267.13C512,95.682,490.385,72.348,461.913,72.348z M50.087,105.739h411.826    c3.755,0,7.58,1.382,10.529,3.814L267.792,314.204c-6.502,6.501-17.083,6.5-23.583,0L39.558,109.554    C42.483,107.144,46.298,105.739,50.087,105.739z M478.609,389.565c0,9.206-7.49,16.696-16.696,16.696H50.087    c-9.206,0-16.696-7.49-16.696-16.696V150.609l187.206,187.206c19.521,19.518,51.282,19.521,70.804,0l187.207-187.206V389.565z"
              />
            </g>
          </g>
        </svg>
        Góp ý</span
      >
    </li>
  </ul>
  <ul
    class="overflow-hidden w-full text-black bg-white dark:bg-dark-bg border-t transition-[height] duration-200 dark:text-white dark:border-gray-600 flex md:hidden flex-col"
    style="height: 0px"
    #dropdownNavbar
  >
    <li class="nav-bar-item-mobie">
      <a
        [routerLink]="['/truyen-hot']"
        class="px-2 lg:px-3 h-full w-full items-center flex gap-1"
      >
        <svg
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
          />
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2.5"
            d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"
          />
        </svg>

        Hot
      </a>
    </li>
    <li class="nav-bar-item-mobie">
      <div
        class="cursor-pointer px-2 lg:px-3 h-full dark:fill-white items-center flex hover:text-black dark:hover:text-white group gap-1 w-full relative"
      >
        <span class="flex items-center">
          <svg
            class="h-6 w-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path
              d="M11 3L20 12a1.5 1.5 0 0 1 0 2L14 20a1.5 1.5 0 0 1 -2 0L3 11v-4a4 4 0 0 1 4 -4h4"
            />
            <circle cx="9" cy="9" r="2" />
          </svg>
          Thể loại</span
        >
        <div
          *ngIf="listGenres && isBrowser"
          class="absolute inset-0 top-full left-0 hidden group-hover:flex z-[40] shadow-sm border border-solid dark:border-gray-950"
        >
          <app-genre-catagories
            [listGenres]="listGenres"
          ></app-genre-catagories>
        </div>
      </div>
    </li>
    <li class="nav-bar-item-mobie">
      <a
        [routerLink]="['/theo-doi']"
        class="px-2 lg:px-3 h-full w-full items-center flex gap-1"
      >
        <svg
          class="h-6 w-6"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <path
            d="M12 20l-7 -7a4 4 0 0 1 6.5 -6a.9 .9 0 0 0 1 0a4 4 0 0 1 6.5 6l-7 7"
          />
        </svg>
        Theo dõi</a
      >
    </li>
    <li class="nav-bar-item-mobie">
      <a
        [routerLink]="['/xep-hang']"
        class="px-2 lg:px-3 h-full w-full items-center flex gap-1"
      >
        <svg
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>

        Xếp hạng</a
      >
    </li>
    <li class="nav-bar-item-mobie">
      <a
        [routerLink]="['/tim-truyen']"
        class="px-2 lg:px-3 h-full w-full items-center flex"
      >
        <svg
          class="h-6 w-6"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="11" cy="11" r="8" />
          <line x1="21" y1="21" x2="16.65" y2="16.65" />
        </svg>
        Tìm kiếm nâng cao</a
      >
    </li>
    <li class="nav-bar-item-mobie">
      <a
        [routerLink]="['/lich-su']"
        class="px-2 lg:px-3 h-full w-full items-center flex gap-1"
      >
        <svg
          class="h-6 w-6"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <polyline points="12 8 12 12 14 14" />
          <path d="M3.05 11a9 9 0 1 1 .5 4m-.5 5v-5h5" />
        </svg>
        Lịch sử</a
      >
    </li>
    <li class="nav-bar-item-mobie">
      <span
        (click)="onFeedbackClick()"
        class="px-2 lg:px-3 h-full w-full items-center flex gap-1"
      >
        <svg
          class="size-5"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          version="1.1"
          id="Layer_1"
          fill="currentColor"
          viewBox="0 0 512 512"
          xml:space="preserve"
        >
          <g>
            <g>
              <path
                d="M461.913,72.348H50.087C21.236,72.348,0,96.108,0,122.435v267.13c0,27.618,22.469,50.087,50.087,50.087h411.826    c27.618,0,50.087-22.469,50.087-50.087v-267.13C512,95.682,490.385,72.348,461.913,72.348z M50.087,105.739h411.826    c3.755,0,7.58,1.382,10.529,3.814L267.792,314.204c-6.502,6.501-17.083,6.5-23.583,0L39.558,109.554    C42.483,107.144,46.298,105.739,50.087,105.739z M478.609,389.565c0,9.206-7.49,16.696-16.696,16.696H50.087    c-9.206,0-16.696-7.49-16.696-16.696V150.609l187.206,187.206c19.521,19.518,51.282,19.521,70.804,0l187.207-187.206V389.565z"
              />
            </g>
          </g>
        </svg>
        Góp ý</span
      >
    </li>
  </ul>
</nav>
