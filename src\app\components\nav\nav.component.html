<header class="nav-header">
  <div class="nav-container">
    <a [routerLink]="['/']" class="nav-logo">
      <img loading="eager" src="/30-4-logo.png" alt="logo" />
    </a>

    <div *ngIf="isBrowser" class="nav-actions">
      <app-list-search-comic />

      <div class="theme-toggle">
        <input
          aria-label="Toggle theme"
          id="theme-toggle"
          (change)="themeService.ToggleTheme()"
          type="checkbox"
          class="theme-input"
        />
        <span [class.active]="isDarkMode" class="theme-slider">
          <svg class="theme-icon sun" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 12.667A4.667 4.667 0 1 0 8 3.333a4.667 4.667 0 0 0 0 9.334z"/>
            <path d="M8 15.307a.667.667 0 0 1-.667-.667v-.053a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.72zm4.76-1.88a.667.667 0 0 1-.473-.194l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.134zm-9.52 0a.667.667 0 0 1-.473-1.134l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.194zM14.667 8.667h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm-13.28 0h-.054a.667.667 0 1 1 0-1.334.667.667 0 0 1 0 1.334zm11.286-4.674a.667.667 0 0 1-.473-.193.667.667 0 0 1 0-.94l.087-.087a.667.667 0 1 1 .94.94l-.087.087a.667.667 0 0 1-.467.193zm-9.346 0a.667.667 0 0 1-.473-.193l-.087-.087a.667.667 0 1 1 .94-.94l.087.087a.667.667 0 0 1-.467 1.133zM8 2.027a.667.667 0 0 1-.667-.667V1.333a.667.667 0 1 1 1.334 0 .667.667 0 0 1-.667.694z"/>
          </svg>
          <svg class="theme-icon moon" viewBox="0 0 16 16" fill="currentColor">
            <path d="M14.353 10.62c-.107-.18-.407-.46-1.153-.327-.414.073-.834.107-1.254.087-1.553-.067-2.96-.78-3.94-1.88-.866-.967-1.4-2.227-1.406-3.587 0-.76.146-1.493.446-2.187.294-.673.087-1.027-.06-1.173-.153-.154-.513-.367-1.22-.067C3.04 2.627 1.353 5.36 1.553 8.287c.2 2.76 2.133 6.113 4.693 7 .614.213 1.26.34 1.927.367.067.006.174.013.28.013 2.234 0 4.327-1.053 5.647-2.847.447-.62.327-1.013.213-1.193z"/>
          </svg>
        </span>
      </div>

      <app-notify *ngIf="shouldShowNotifications" />

      <app-user-menu
        [user]="user"
        [userStats]="userStats"
        [isAuthenticated]="isAuthenticated"
        [notificationCount]="notificationCount"
        [config]="userMenuConfig"
        (menuToggle)="onUserMenuToggle($event)"
        (menuItemClick)="onUserMenuItemClick($event)"
        (quickActionClick)="onUserQuickAction($event)"
        (logoutClick)="onLogoutClick()"
        (settingsClick)="onSettingClick()"
        (loginClick)="onGuestLogin()"
        (registerClick)="onGuestRegister()"
      />
    </div>
  </div>
</header>

<nav class="nav-main">
  <div class="nav-mobile">
    <div class="nav-home">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
      </svg>
      <a [routerLink]="['/']">Trang chủ</a>
    </div>
    <button class="nav-toggle" (click)="toggleSidebar()">
      <svg *ngIf="!showSidebar" class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"/>
      </svg>
      <svg *ngIf="showSidebar" class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
      </svg>
    </button>
  </div>

  <ul class="nav-desktop">
    <li><a routerLink="/" aria-label="Trang chủ" class="nav-link">
      <svg class="nav-icon" viewBox="0 0 576 512" fill="currentColor">
        <path d="M575.8 255.5c0 18-15 32.1-32 32.1h-32l.7 160.2c0 2.7-.2 5.4-.5 8.1V472c0 22.1-17.9 40-40 40H456c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1H416 392c-22.1 0-40-17.9-40-40V448 384c0-17.7-14.3-32-32-32H256c-17.7 0-32 14.3-32 32v64 24c0 22.1-17.9 40-40 40H160 128.1c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2H104c-22.1 0-40-17.9-40-40V360c0-.9 0-1.9 .1-2.8V287.6H32c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z"/>
      </svg>
    </a></li>
    
    <li><a [routerLink]="['/truyen-hot']" class="nav-link">
      <svg class="nav-icon" viewBox="0 0 448 512" fill="currentColor">
        <path d="M159.3 5.4c7.8-7.3 19.9-7.2 27.7 .1c27.6 25.9 53.5 53.8 77.7 84c11-14.4 23.5-30.1 37-42.9c7.9-7.4 20.1-7.4 28 .1c34.6 33 63.9 76.6 84.5 118c20.3 40.8 33.8 82.5 33.8 111.9C448 404.2 348.2 512 224 512C98.4 512 0 404.1 0 276.5c0-38.4 17.8-85.3 45.4-131.7C73.3 97.7 112.7 48.6 159.3 5.4zM225.7 416c25.3 0 47.7-7 68.8-21c42.1-29.4 53.4-88.2 28.1-134.4c-4.5-9-16-9.6-22.5-2l-25.2 29.3c-6.6 7.6-18.5 7.4-24.7-.5c-16.5-21-46-58.5-62.8-79.8c-6.3-8-18.3-8.1-24.7-.1c-33.8 42.5-50.8 69.3-50.8 99.4C112 375.4 162.6 416 225.7 416z"/>
      </svg>
      Hot
    </a></li>
    
    <li class="nav-dropdown">
      <div class="nav-link">
        <svg class="nav-icon" viewBox="0 0 512 512" fill="currentColor">
          <path d="M345 39.1L472.8 168.4c52.4 53 52.4 138.2 0 191.2L360.8 472.9c-9.3 9.4-24.5 9.5-33.9 .2s-9.5-24.5-.2-33.9L438.6 325.9c33.9-34.3 33.9-89.4 0-123.7L310.9 72.9c-9.3-9.4-9.2-24.6 .2-33.9s24.6-9.2 33.9 .2zM0 229.5V80C0 53.5 21.5 32 48 32H197.5c17 0 33.3 6.7 45.3 18.7l168 168c25 25 25 65.5 0 90.5L277.3 442.7c-25 25-65.5 25-90.5 0l-168-168C6.7 262.7 0 246.5 0 229.5zM144 144a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"/>
        </svg>
        Thể loại
      </div>
      <div *ngIf="listGenres && isBrowser" class="nav-dropdown-menu">
        <app-genre-catagories [listGenres]="listGenres" />
      </div>
    </li>
    
    <li><a [routerLink]="['/theo-doi']" class="nav-link">
      <svg class="nav-icon" viewBox="0 0 576 512" fill="currentColor">
        <path d="M47.6 300.4L228.3 469.1c7.5 7 17.4 10.9 27.7 10.9s20.2-3.9 27.7-10.9l2.6-2.4C267.2 438.6 256 404.6 256 368c0-97.2 78.8-176 176-176c28.3 0 55 6.7 78.7 18.5c.9-6.5 1.3-13 1.3-19.6v-5.8c0-69.9-50.5-129.5-119.4-141C347 36.5 300.6 51.4 268 84L256 96 244 84c-32.6-32.6-79-47.5-124.6-39.9C50.5 55.6 0 115.2 0 185.1v5.8c0 41.5 17.2 81.2 47.6 109.5zM432 512a144 144 0 1 0 0-288 144 144 0 1 0 0 288zm16-208v48h48c8.8 0 16 7.2 16 16s-7.2 16-16 16H448v48c0 8.8-7.2 16-16 16s-16-7.2-16-16V384H368c-8.8 0-16-7.2-16-16s7.2-16 16-16h48V304c0-8.8 7.2-16 16-16s16 7.2 16 16z"/>
      </svg>
      Theo dõi
    </a></li>
    
    <li><a [routerLink]="['/xep-hang']" class="nav-link">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z"/>
      </svg>
      Xếp hạng
    </a></li>
    
    <li><a routerLink="/tim-truyen" class="nav-link">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>
      </svg>
      Tìm kiếm nâng cao
    </a></li>
    
    <li><a routerLink="lich-su" class="nav-link">
      <svg class="nav-icon" viewBox="0 0 512 512" fill="currentColor">
        <path d="M75 75L41 41C25.9 25.9 0 36.6 0 57.9V168c0 13.3 10.7 24 24 24H134.1c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24V256c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65V152c0-13.3-10.7-24-24-24z"/>
      </svg>
      Lịch sử
    </a></li>
    
    <li><span (click)="onFeedbackClick()" class="nav-link">
      <svg class="nav-icon" viewBox="0 0 512 512" fill="currentColor">
        <path d="M461.913,72.348H50.087C21.236,72.348,0,96.108,0,122.435v267.13c0,27.618,22.469,50.087,50.087,50.087h411.826c27.618,0,50.087-22.469,50.087-50.087v-267.13C512,95.682,490.385,72.348,461.913,72.348z M50.087,105.739h411.826c3.755,0,7.58,1.382,10.529,3.814L267.792,314.204c-6.502,6.501-17.083,6.5-23.583,0L39.558,109.554C42.483,107.144,46.298,105.739,50.087,105.739z M478.609,389.565c0,9.206-7.49,16.696-16.696,16.696H50.087c-9.206,0-16.696-7.49-16.696-16.696V150.609l187.206,187.206c19.521,19.518,51.282,19.521,70.804,0l187.207-187.206V389.565z"/>
      </svg>
      Góp ý
    </span></li>
  </ul>

  <ul class="nav-mobile-menu" style="height: 0px" #dropdownNavbar>
    <li><a [routerLink]="['/truyen-hot']" class="nav-mobile-link">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"/>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"/>
      </svg>
      Hot
    </a></li>
    
    <li class="nav-mobile-dropdown">
      <div class="nav-mobile-link">
        <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <path d="M11 3L20 12a1.5 1.5 0 0 1 0 2L14 20a1.5 1.5 0 0 1 -2 0L3 11v-4a4 4 0 0 1 4 -4h4"/>
          <circle cx="9" cy="9" r="2"/>
        </svg>
        Thể loại
      </div>
      <div *ngIf="listGenres && isBrowser" class="nav-mobile-dropdown-menu">
        <app-genre-catagories [listGenres]="listGenres" />
      </div>
    </li>
    
    <li><a [routerLink]="['/theo-doi']" class="nav-mobile-link">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path d="M12 20l-7 -7a4 4 0 0 1 6.5 -6a.9 .9 0 0 0 1 0a4 4 0 0 1 6.5 6l-7 7"/>
      </svg>
      Theo dõi
    </a></li>
    
    <li><a [routerLink]="['/xep-hang']" class="nav-mobile-link">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
      </svg>
      Xếp hạng
    </a></li>
    
    <li><a [routerLink]="['/tim-truyen']" class="nav-mobile-link">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <circle cx="11" cy="11" r="8"/>
        <line x1="21" y1="21" x2="16.65" y2="16.65"/>
      </svg>
      Tìm kiếm nâng cao
    </a></li>
    
    <li><a [routerLink]="['/lich-su']" class="nav-mobile-link">
      <svg class="nav-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
        <polyline points="12 8 12 12 14 14"/>
        <path d="M3.05 11a9 9 0 1 1 .5 4m-.5 5v-5h5"/>
      </svg>
      Lịch sử
    </a></li>
    
    <li><span (click)="onFeedbackClick()" class="nav-mobile-link">
      <svg class="nav-icon" viewBox="0 0 512 512">
        <path d="M461.913,72.348H50.087C21.236,72.348,0,96.108,0,122.435v267.13c0,27.618,22.469,50.087,50.087,50.087h411.826c27.618,0,50.087-22.469,50.087-50.087v-267.13C512,95.682,490.385,72.348,461.913,72.348z M50.087,105.739h411.826c3.755,0,7.58,1.382,10.529,3.814L267.792,314.204c-6.502,6.501-17.083,6.5-23.583,0L39.558,109.554C42.483,107.144,46.298,105.739,50.087,105.739z M478.609,389.565c0,9.206-7.49,16.696-16.696,16.696H50.087c-9.206,0-16.696-7.49-16.696-16.696V150.609l187.206,187.206c19.521,19.518,51.282,19.521,70.804,0l187.207-187.206V389.565z"/>
      </svg>
      Góp ý
    </span></li>
  </ul>
</nav>
