import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { FadeInDirective } from '@directives/fade-in.directive';
import { IUser } from '@schema';

export interface UserMenuItem {
  id: string;
  label: string;
  type: 'link' | 'button';
  route?: string;
  action?: string;
  iconPath: string;
  secondaryIconPath?: string;
  disabled?: boolean;
}

export interface UserStats {
  readCount: number;
  favoriteCount?: number;
  followingCount?: number;
}

export interface UserMenuConfig {
  showPreview?: boolean;
  showQuickActions?: boolean;
  customMenuItems?: UserMenuItem[];
}

@Component({
  selector: 'app-user-menu',
  templateUrl: './user-menu.component.html',
  styleUrl: './user-menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, RouterLink, ClickOutsideDirective, FadeInDirective]
})
export class UserMenuComponent implements OnInit, OnDestroy {
  @Input() user: IUser | undefined = undefined;
  @Input() userStats: UserStats | null = null;
  @Input() isAuthenticated = false;
  @Input() notificationCount = 0;
  @Input() statusText = 'Đang hoạt động';
  @Input() config: UserMenuConfig = {};

  @Output() menuToggle = new EventEmitter<boolean>();
  @Output() menuItemClick = new EventEmitter<UserMenuItem>();
  @Output() quickActionClick = new EventEmitter<string>();
  @Output() logoutClick = new EventEmitter<void>();
  @Output() settingsClick = new EventEmitter<void>();
  @Output() loginClick = new EventEmitter<void>();
  @Output() registerClick = new EventEmitter<void>();

  isOpen = false;
  showPreview = true;
  showQuickActions = true;

  defaultMenuItems: UserMenuItem[] = [
    {
      id: 'profile',
      label: 'Trang cá nhân',
      type: 'link',
      route: '/tai-khoan',
      iconPath: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
    },
    {
      id: 'following',
      label: 'Theo dõi',
      type: 'link',
      route: '/theo-doi',
      iconPath: 'M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z'
    },
    {
      id: 'settings',
      label: 'Cài đặt',
      type: 'button',
      action: 'settings',
      iconPath: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
      secondaryIconPath: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z'
    }
  ];

  menuItems: UserMenuItem[] = [];

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.initializeConfig();
    this.setupMenuItems();
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  private initializeConfig(): void {
    this.showPreview = this.config.showPreview ?? true;
    this.showQuickActions = this.config.showQuickActions ?? true;
  }

  private setupMenuItems(): void {
    this.menuItems = this.config.customMenuItems || this.defaultMenuItems;
  }

  toggleMenu(): void {
    this.isOpen = !this.isOpen;
    this.menuToggle.emit(this.isOpen);
  }

  closeMenu(): void {
    if (this.isOpen) {
      this.isOpen = false;
      this.menuToggle.emit(false);
    }
  }

  onMenuItemClick(item: UserMenuItem): void {
    if (item.disabled) {
      return;
    }

    this.menuItemClick.emit(item);

    if (item.type === 'link' && item.route) {
      this.router.navigate([item.route]);
      this.closeMenu();
    } else if (item.type === 'button' && item.action) {
      this.handleButtonAction(item.action);
    }
  }

  private handleButtonAction(action: string): void {
    switch (action) {
      case 'settings':
        this.settingsClick.emit();
        this.closeMenu();
        break;
      default:
        console.warn(`Unknown action: ${action}`);
    }
  }

  onQuickAction(action: string): void {
    this.quickActionClick.emit(action);
    
    // Handle common quick actions
    switch (action) {
      case 'notifications':
        // Navigate to notifications or open notifications panel
        break;
      case 'favorites':
        this.router.navigate(['/yeu-thich']);
        this.closeMenu();
        break;
      case 'history':
        this.router.navigate(['/lich-su']);
        this.closeMenu();
        break;
    }
  }

  onLogout(): void {
    this.logoutClick.emit();
    this.closeMenu();
  }

  onLogin(): void {
    this.loginClick.emit();
    this.closeMenu();
  }

  onRegister(): void {
    this.registerClick.emit();
    this.closeMenu();
  }

  trackByMenuItem(_: number, item: UserMenuItem): string {
    return item.id;
  }

  // Public methods for external control
  public openMenu(): void {
    this.isOpen = true;
    this.menuToggle.emit(true);
  }

  public getMenuState(): boolean {
    return this.isOpen;
  }



  public updateNotificationCount(count: number): void {
    this.notificationCount = count;
  }

  public updateUserStats(stats: UserStats): void {
    this.userStats = stats;
  }

  public addMenuItem(item: UserMenuItem): void {
    this.menuItems.push(item);
  }

  public removeMenuItem(itemId: string): void {
    this.menuItems = this.menuItems.filter(item => item.id !== itemId);
  }

  public updateMenuItem(itemId: string, updates: Partial<UserMenuItem>): void {
    const index = this.menuItems.findIndex(item => item.id === itemId);
    if (index !== -1) {
      this.menuItems[index] = { ...this.menuItems[index], ...updates };
    }
  }
}
