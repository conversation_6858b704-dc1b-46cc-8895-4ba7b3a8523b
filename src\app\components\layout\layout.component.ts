import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FooterComponent } from '@components/footer/footer.component';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';
import { NavComponent } from '@components/nav/nav.component';
// import { OverlayContainer } from '@angular/cdk/overlay';

import { ThemeService } from '@services/theme.service';
import { BehaviorSubject, Observable } from 'rxjs';


@Component({
    selector: 'app-content-layout',
    templateUrl: './layout.component.html',
    styleUrl: './layout.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule,FooterComponent,NavComponent,RouterModule, ChatBoxBubbleComponent],
})
export class LayoutComponent implements OnInit {
  currentTheme: Observable<boolean> = new BehaviorSubject<boolean>(false);

  constructor(private themeService: ThemeService,
    @Inject(PLATFORM_ID) private platformId: object) {
  }
  ngOnInit() {

    this.currentTheme = this.themeService.getDarkTheme();
  }

}
