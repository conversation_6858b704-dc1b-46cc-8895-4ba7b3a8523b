// Modern Toggle Component - Comic Website Design
.setting-toggle-container {
  @apply flex flex-col gap-2;
}

.setting-toggle-header {
  @apply flex items-center justify-between;
}

.setting-toggle-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-200;
  @apply cursor-pointer;
}

.setting-toggle-wrapper {
  @apply relative inline-flex items-center;
}

.setting-toggle-input {
  @apply sr-only;

  &:checked + .setting-toggle-slider {
    @apply bg-primary-100;

    &::before {
      @apply translate-x-5;
    }
  }

  &:disabled + .setting-toggle-slider {
    @apply opacity-50 cursor-not-allowed;
  }

  &:focus + .setting-toggle-slider {
    @apply ring-2 ring-primary-100 ring-offset-2;
  }
}

.setting-toggle-slider {
  @apply relative inline-flex h-6 w-11 items-center rounded-full;
  @apply bg-gray-200 dark:bg-neutral-700;
  @apply transition-colors duration-200;
  @apply cursor-pointer;

  &::before {
    content: '';
    @apply inline-block h-4 w-4 transform rounded-full;
    @apply bg-white shadow-lg;
    @apply transition-transform duration-200;
    @apply translate-x-1;
  }

  &:hover {
    @apply bg-gray-300 dark:bg-neutral-600;
  }
}

.setting-toggle-description {
  @apply text-xs text-gray-500 dark:text-gray-400;
  @apply leading-relaxed;
}
