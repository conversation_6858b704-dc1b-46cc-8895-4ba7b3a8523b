<div (appClickOutside)="onClickNotifyBellOutside()" class="notify-container">
  <button
    type="button"
    aria-label="Thông báo"
    class="notify-bell"
    [class.notify-bell-active]="isShowNotify"
    [class.notify-bell-has-unread]="hasUnreadNotifications"
    (click)="onClickNotifyBell()"
  >
    <div class="notify-bell-wrapper">
      <svg
        class="notify-bell-icon"
        height="20"
        width="20"
        viewBox="0 0 1024 1024"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M512 938.666667c47.146667 0 85.333333-38.186667 85.333333-85.333334h-170.666666c0 47.146667 38.186667 85.333333 85.333333 85.333334z m256-256V469.333333c0-131.2-69.76-240.64-192-269.653333V170.666667c0-35.413333-28.586667-64-64-64s-64 28.586667-64 64v29.013333c-122.24 29.013333-192 138.453333-192 269.653333v213.333334l-85.333333 85.333333v42.666667h682.666666v-42.666667l-85.333333-85.333333z"
        />
      </svg>
      <span [ngClass]="notificationBadgeClass" class="notify-badge">
        {{ lengthnotifyReaded }}
      </span>
    </div>
  </button>

  <div *ngIf="isShowNotify" class="notify-panel">
    <!-- Enhanced Header -->
    <div class="notify-header">
      <div class="notify-header-content">
        <div class="notify-title-section">
          <svg class="notify-title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
            <path d="M13.73 21a2 2 0 0 1-3.46 0" />
          </svg>
          <h3 class="notify-title">Thông báo</h3>
        </div>

        <div class="notify-actions">
          <button
            class="notify-action-btn"
            (click)="onToggleOptionGeneral()"
            [class.active]="isShowOptionGeneral"
            title="Tùy chọn"
          >
            <svg class="notify-action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="3" />
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Enhanced Dropdown Menu -->
      <div *ngIf="isShowOptionGeneral" class="notify-dropdown-menu">
        <div class="notify-dropdown-arrow"></div>
        <div class="notify-dropdown-content">
          <button
            (click)="onReadNotify(null, 'all')"
            class="notify-dropdown-item"
            [disabled]="!hasUnreadNotifications"
          >
            <svg
              class="notify-dropdown-icon success"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path d="M9 12l2 2 4-4" />
              <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.5 0 4.77 1.02 6.41 2.66" />
            </svg>
            <span>Đánh dấu tất cả đã đọc</span>
          </button>

          <button
            (click)="onDeleteNotify(-1)"
            class="notify-dropdown-item danger"
            [disabled]="!hasNotifications"
          >
            <svg class="notify-dropdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M3 6h18" />
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
              <line x1="10" y1="11" x2="10" y2="17" />
              <line x1="14" y1="11" x2="14" y2="17" />
            </svg>
            <span>Xóa tất cả thông báo</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Enhanced Filter Tabs -->
    <div class="notify-filter-tabs">
      <div class="notify-tab-container">
        <button
          [class.active]="optionNotify === 0"
          class="notify-tab"
          (click)="toggleOptionNotify(0)"
        >
          <span class="notify-tab-text">Tất cả</span>
          <span class="notify-tab-count" *ngIf="lengthnotifyData > 0">{{ lengthnotifyData }}</span>
        </button>
        <button
          [class.active]="optionNotify === 1"
          class="notify-tab"
          (click)="toggleOptionNotify(1)"
        >
          <span class="notify-tab-text">Chưa đọc</span>
          <span class="notify-tab-count unread" *ngIf="lengthnotifyReaded > 0">{{
            lengthnotifyReaded
          }}</span>
        </button>
      </div>
      <div class="notify-tab-indicator" [class.unread-active]="optionNotify === 1"></div>
    </div>

    <!-- Enhanced Empty State -->
    <div *ngIf="shouldShowEmptyState" class="notify-empty-state">
      <app-empty class=" notify-empty-icon"></app-empty>
      <p class="notify-empty-text">Bạn không có thông báo nào</p>
    </div>

    <!-- Enhanced Notification List -->
    <div *ngIf="shouldShowNotificationList" class="notify-list  scrollbar-style-1">
      <div class="notify-list-container">
        <div
          *ngFor="let notify of listnotify; let i = index; trackBy: trackByNotificationId"
          class="notify-item"
          [class.notify-item-unread]="!notify.isRead"
          [class.notify-item-hovered]="hoveredIndexNotify === i"
          (mouseenter)="onMouseEnterNotification(i)"
          (mouseleave)="onMouseLeaveNotifications()"
        >
          <!-- Unread Indicator -->
          <div class="notify-unread-indicator" *ngIf="!notify.isRead"></div>

          <!-- Notification Content -->
          <div class="notify-item-content">
            <a
              (click)="onReadNotify(notify.id, 'read', notify.isRead)"
              [routerLink]="getNotificationLink(notify)"
              class="notify-item-image"
            >
              <img
                loading="lazy"
                class="notify-item-image-src"
                [src]="notify.image"
                alt="Notification image"
              />
            </a>

            <a
              (click)="onReadNotify(notify.id, 'read', notify.isRead)"
              [routerLink]="getNotificationLink(notify)"
              [ngClass]="{
                'notify-item-info-read': notify.isRead
              }"
              class="notify-item-info"
            >
              <span class="notify-item-info-content" [innerHTML]="notify.content"> </span>
              <span
                [ngClass]="{
                  'notify-item-info-timestamp-unread': !notify.isRead
                }"
                class="notify-item-info-timestamp"
                >{{ notify.timestamp | dateAgo: "trước" : 7*3600  }}</span
              >
            </a>
          </div>

          <div
            *ngIf="hoveredIndexNotify === i || isShowOption === i"
            (click)="onClickOption(i)"
            class="notify-item-option"
          >
            <svg
              class="notify-item-option-icon"
              fill="#5E5E5E"
              version="1.1"
              id="Capa_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="15px"
              height="15px"
              viewBox="0 0 992 992"
              xml:space="preserve"
            >
              <g>
                <circle cx="144.3" cy="496" r="144.3" />
                <circle cx="496" cy="496" r="144.3" />
                <circle cx="847.7" cy="496" r="144.3" />
              </g>
            </svg>
          </div>

          <div *ngIf="isShowOption === i" class="notify-item-option-dropdown">
            <svg
              aria-hidden="true"
              height="12"
              viewBox="0 0 21 12"
              width="21"
              class="notify-item-option-dropdown-icon"
              fill="var(--card-background)"
              style="transform: scale(-1, -1) translate(0px, 0px)"
            >
              <path
                d="M20.685.12c-2.229.424-4.278 1.914-6.181 3.403L5.4 10.94c-2.026 2.291-5.434.62-5.4-2.648V.12h20.684z"
              ></path>
            </svg>
            <div
              (click)="onReadNotify(notify.id, 'optionNotify', notify.isRead)"
              class="notify-item-option-dropdown-item"
            >
              <svg
                fill="#06b6d4"
                class="notify-item-option-dropdown-item-icon"
                height="15px"
                width="15px"
                version="1.1"
                id="Capa_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                viewBox="0 0 490 490"
                xml:space="preserve"
              >
                <polygon
                  points="452.253,28.326 197.831,394.674 29.044,256.875 0,292.469 207.253,461.674 490,54.528 "
                />
              </svg>
              <span class="notify-item-option-dropdown-item-content">{{
                notify.isRead ? 'Đánh dấu chưa đọc' : ' Đánh dấu đã đọc'
              }}</span>
            </div>
            <div (click)="onDeleteNotify(notify.id)" class="notify-item-option-dropdown-item">
              <svg
                class="notify-item-option-dropdown-item-icon"
                fill="#ef4444"
                height="15px"
                width="15px"
                version="1.1"
                id="Capa_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                viewBox="0 0 460.775 460.775"
                xml:space="preserve"
              >
                <path
                  d="M285.08,230.397L456.218,59.27c6.076-6.077,6.076-15.911,0-21.986L423.511,4.565c-2.913-2.911-6.866-4.55-10.992-4.55
              c-4.127,0-8.08,1.639-10.993,4.55l-171.138,171.14L59.25,4.565c-2.913-2.911-6.866-4.55-10.993-4.55
              c-4.126,0-8.08,1.639-10.992,4.55L4.558,37.284c-6.077,6.075-6.077,15.909,0,21.986l171.138,171.128L4.575,401.505
              c-6.074,6.077-6.074,15.911,0,21.986l32.709,32.719c2.911,2.911,6.865,4.55,10.992,4.55c4.127,0,8.08-1.639,10.994-4.55
              l171.117-171.12l171.118,171.12c2.913,2.911,6.866,4.55,10.993,4.55c4.128,0,8.081-1.639,10.992-4.55l32.709-32.719
              c6.074-6.075,6.074-15.909,0-21.986L285.08,230.397z"
                />
              </svg>
              <span class="notify-item-option-dropdown-item-content">Xóa</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
