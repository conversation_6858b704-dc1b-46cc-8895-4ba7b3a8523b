import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-user-reviews',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="user-reviews-container">
      <div class="page-header">
        <h2 class="page-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
          </svg>
          <PERSON><PERSON>h giá
        </h2>
        <p class="page-description">Xem lại các truyện bạn đã đánh giá</p>
      </div>

      <div class="content-card">
        <div class="empty-state">
          <div class="empty-illustration">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
            </svg>
          </div>
          <h3 class="empty-title">Chưa có đánh giá</h3>
          <p class="empty-description">
            Bạn chưa đánh giá truyện nào. Hãy chia sẻ cảm nhận về những truyện bạn đã đọc!
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .user-reviews-container {
      @apply space-y-6;
    }

    .page-header {
      @apply space-y-2;
    }

    .page-title {
      @apply flex items-center gap-3 text-2xl font-bold text-gray-900 dark:text-white;
    }

    .title-icon {
      @apply w-8 h-8 text-yellow-500;
      fill: none;
      stroke: currentColor;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .page-description {
      @apply text-gray-600 dark:text-gray-400;
    }

    .content-card {
      @apply bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm p-12;
      backdrop-filter: blur(20px);
      background: rgba(255, 255, 255, 0.95);
      
      .dark & {
        background: rgba(31, 41, 55, 0.95);
      }
    }

    .empty-state {
      @apply text-center space-y-6;
    }

    .empty-illustration {
      @apply flex justify-center;
    }

    .empty-icon {
      @apply w-24 h-24 text-gray-300 dark:text-gray-600;
      fill: none;
      stroke: currentColor;
      stroke-width: 1;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .empty-title {
      @apply text-xl font-semibold text-gray-900 dark:text-white;
    }

    .empty-description {
      @apply text-gray-600 dark:text-gray-400 max-w-md mx-auto leading-relaxed;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserReviewsComponent {
}
