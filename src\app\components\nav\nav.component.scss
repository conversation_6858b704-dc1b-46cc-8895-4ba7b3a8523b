
// Navigation Styles - Using Tailwind @apply and responsive utilities

// Header
.nav-header {
  @apply bg-white dark:bg-neutral-900 p-3 border-b border-primary-100;
}

.nav-container {
  @apply max-w-7xl mx-auto flex justify-between items-center;
  @apply md:px-4;
}

// Logo
.nav-logo {
  @apply flex items-center justify-center w-32 text-primary-100 no-underline;
  @apply hover:scale-105 transition-transform duration-200;

  img {
    @apply w-full h-auto;
  }
}

// Actions
.nav-actions {
  @apply flex gap-2 items-center;
}

// Theme Toggle
.theme-toggle {
  @apply relative w-12 h-7 rounded-full bg-gray-200 dark:bg-neutral-700;
}

.theme-input {
  @apply absolute inset-0 opacity-0 cursor-pointer z-10;
}

.theme-slider {
  @apply absolute top-1/2 left-1 w-5 h-5 bg-white rounded-full;
  @apply -translate-y-1/2 transition-transform duration-200;
  @apply flex items-center justify-center;

  &.active {
    @apply translate-x-5;
  }
}

.theme-icon {
  @apply w-4 h-4 text-primary-100;

  &.sun {
    @apply block;
  }

  &.moon {
    @apply hidden;
  }

  .theme-slider.active & {
    &.sun {
      @apply hidden;
    }

    &.moon {
      @apply block;
    }
  }
}



// Main Navigation
.nav-main {
  @apply relative bg-primary-100 dark:bg-neutral-800 text-white text-sm py-0.5;
}

// Mobile Header
.nav-mobile {
  @apply flex items-center justify-between px-3 h-10;
  @apply md:hidden;
}

.nav-home {
  @apply flex items-center gap-1 text-white;

  a {
    @apply text-inherit no-underline text-sm font-medium;
  }
}

.nav-toggle {
  @apply bg-transparent border-2 border-transparent rounded-lg cursor-pointer p-1;
  @apply hover:border-white/30;
}

.nav-icon {
  @apply w-6 h-6 text-current;
}

// Desktop Navigation
.nav-desktop {
  @apply hidden md:container md:flex items-center h-10 max-w-7xl mx-auto;
  @apply list-none p-0 gap-2;

  li {
    @apply h-full;
  }
}

.nav-link {
  @apply flex items-center gap-2 h-full px-3 text-white no-underline;
  @apply rounded-t-lg cursor-pointer font-semibold;
  @apply hover:bg-white hover:text-black;

  &:hover .nav-icon {
    @apply text-black;
  }

  .nav-icon {
    @apply w-4 h-4 shrink-0;
  }
}

.nav-dropdown {
  @apply relative;

  &:hover .nav-dropdown-menu {
    @apply block;
  }
}

.nav-dropdown-menu {
  @apply hidden absolute top-full left-0 z-40;
  @apply border border-gray-700 dark:border-gray-900 text-black dark:text-white;
}

// Mobile Navigation Menu
.nav-mobile-menu {
  @apply overflow-hidden w-full bg-white dark:bg-neutral-900 text-black dark:text-white;
  @apply border-t border-primary-100 dark:border-neutral-700;
  @apply transition-[height] duration-200 flex flex-col list-none p-0 m-0;
  @apply md:hidden;

  li {
    @apply border-b border-gray-100 dark:border-gray-700;
  }
}

.nav-mobile-link {
  @apply flex items-center gap-3 p-3 text-inherit no-underline w-full h-10 cursor-pointer;
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;

  .nav-icon {
    @apply w-6 h-6 shrink-0;
  }
}

.nav-mobile-dropdown {
  @apply relative;

  &:hover .nav-mobile-dropdown-menu {
    @apply block;
  }
}

.nav-mobile-dropdown-menu {
  @apply hidden absolute top-full left-0 right-0 z-40;
  @apply border border-gray-700 dark:border-gray-900;
}

