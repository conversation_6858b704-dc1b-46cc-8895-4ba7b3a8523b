
// Navigation Header Styles
.nav-header {
  @apply bg-white dark:bg-dark-bg p-3 border-b-primary-100;
}

.nav-header-container {
  @apply md:container mx-auto flex justify-between;
}

// Logo Section
.nav-logo-section {
  @apply flex;
}

.nav-logo-link {
  @apply text-primary-100 shrink-0 text-3xl w-32 hover:scale-125 duration-300 cursor-pointer ease-in-out flex items-center justify-center;
}

.nav-logo-image {
  @apply flex w-32;
}

// Navigation Actions
.nav-actions {
  @apply flex space-x-2 items-center;
}

// Theme Toggle
.theme-toggle-container {
  @apply flex items-center justify-center relative m-0 h-7 w-12 rounded-full bg-neutral-200 dark:bg-neutral-700;
}

.theme-toggle-input {
  @apply absolute top-0 z-50 m-0 h-full w-full cursor-pointer opacity-0;
}

.theme-toggle-slider {
  @apply shadow-slate-600 dark:shadow-orange-500 shadow-sm absolute left-1 top-1/2 flex h-5 w-5 -translate-y-1/2 items-center justify-center rounded-full bg-white duration-75 ease-linear;

  &.theme-toggle-slider-light {
    @apply translate-x-full;
  }

  &.theme-toggle-slider-dark {
    @apply translate-x-0;
  }
}

.theme-icon-light {
  @apply dark:hidden text-primary-100;
}

.theme-icon-dark {
  @apply hidden dark:inline-block text-primary-100;
}



// Main Navigation
.nav-main {
  @apply relative bg-primary-100 dark:bg-neutral-800 text-sm flex-col text-white items-center py-0.5;
}

// Mobile Navigation Header
.nav-mobile-header {
  @apply nav-bar-ul px-3 flex md:hidden;
}

.nav-mobile-home {
  @apply flex items-center gap-1 text-white;
}

.nav-mobile-home-icon {
  @apply h-6 w-6;
}

.nav-mobile-home-text {
  @apply text-sm font-medium;
}

.nav-mobile-toggle {
  @apply ml-auto border-2 rounded-lg cursor-pointer;

  &:hover {
    @apply border-gray-300 dark:border-gray-500;
  }
}

.nav-toggle-icon {
  @apply size-6 text-white;
}

// Desktop Navigation
.nav-bar-ul {
  @apply h-10 items-center mx-auto w-full;
}

.nav-bar-li-pc {
  @apply h-full hover:bg-white font-semibold rounded;

  a {
    @apply px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black hover:fill-black fill-white rounded-t-lg transition-colors duration-150;
  }
}

// Mobile Navigation Items
.nav-bar-item-mobie {
  @apply flex h-10 text-sm border-b-[0.5px] dark:border-gray-800;

  a, span {
    @apply px-2 lg:px-3 h-full w-full items-center flex gap-1 transition-colors duration-150;
  }
}

