
// Navigation Header Styles
.nav-header {
  @apply bg-white dark:bg-dark-bg p-3 border-b-primary-100;
}

.nav-header-container {
  @apply md:container mx-auto flex justify-between;
}

// Logo Section
.nav-logo-section {
  @apply flex;
}

.nav-logo-link {
  @apply text-primary-100 shrink-0 text-3xl w-32 hover:scale-125 duration-300 cursor-pointer ease-in-out flex items-center justify-center;
}

.nav-logo-image {
  @apply flex w-32;
}

// Navigation Actions
.nav-actions {
  @apply flex space-x-2 items-center;
}

// Theme Toggle
.theme-toggle-container {
  @apply flex items-center justify-center relative m-0 h-7 w-12 rounded-full bg-neutral-200 dark:bg-neutral-700;
}

.theme-toggle-input {
  @apply absolute top-0 z-50 m-0 h-full w-full cursor-pointer opacity-0;
}

.theme-toggle-slider {
  @apply shadow-slate-600 dark:shadow-orange-500 shadow-sm absolute left-1 top-1/2 flex h-5 w-5 -translate-y-1/2 items-center justify-center rounded-full bg-white duration-75 ease-linear;

  &.theme-toggle-slider-light {
    @apply translate-x-full;
  }

  &.theme-toggle-slider-dark {
    @apply translate-x-0;
  }
}

.theme-icon-light {
  @apply dark:hidden text-primary-100;
}

.theme-icon-dark {
  @apply hidden dark:inline-block text-primary-100;
}

// Guest User Menu
.guest-user-menu {
  @apply relative flex items-center text-white h-8;
}

.guest-user-button {
  @apply size-8 justify-center flex items-center bg-gray-200 dark:bg-neutral-600 rounded-full text-secondary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-neutral-600;

  &:hover {
    @apply bg-gray-300 dark:bg-neutral-500;
  }
}

.guest-user-icon {
  @apply size-6 dark:text-white;
}

.guest-dropdown {
  @apply absolute block top-10 right-0 bg-slate-50 dark:bg-neutral-700 rounded-lg shadow-lg border-[1px] p-4 space-y-1 min-w-40 z-[100];

  &.guest-dropdown-hidden {
    @apply hidden;
  }

  &.guest-dropdown-visible {
    @apply block;
  }
}

.guest-dropdown-content {
  @apply space-y-2;
}

.guest-user-info {
  @apply text-black flex dark:text-white flex-col items-center justify-center font-medium select-none w-full mb-4;
}

.guest-avatar-icon {
  @apply size-10;
}

.guest-username {
  @apply text-center font-bold text-xl line-clamp-1 mx-2 break-all flex-shrink;
}

.guest-divider {
  @apply border-b border-gray-200 w-full h-2 flex;
}

// Authentication Buttons
.auth-button {
  @apply flex justify-center w-full items-center border rounded-lg shadow-md px-3 py-2 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-150;

  &.auth-button-login {
    @apply bg-neutral-800 border-gray-300 dark:border-neutral-700 text-white hover:bg-gray-700 dark:hover:bg-neutral-900;
  }

  &.auth-button-register {
    @apply bg-white dark:bg-neutral-600 dark:border-neutral-600 dark:text-white dark:hover:text-primary-100 border-gray-300 text-gray-800 hover:text-primary-100 mt-1;
  }
}

// Authenticated User Menu
.authenticated-user-menu {
  @apply flex cursor-pointer text-sm relative bg-gray-800 rounded-full md:me-0 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600;

  &:hover {
    @apply ring-2 ring-gray-400 dark:ring-gray-500;
  }
}

.user-avatar {
  @apply w-10 h-10 rounded-full transition-transform duration-150;

  &:hover {
    @apply scale-105;
  }
}

.user-dropdown-container {
  @apply relative;

  &.user-dropdown-hidden {
    @apply hidden;
  }
}

.user-dropdown {
  @apply transition-all min-w-48 overflow-hidden z-[100] mt-12 absolute right-0 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-neutral-800 dark:divide-gray-600;
}

.user-info-section {
  @apply px-4 py-3;
}

.user-display-name {
  @apply block text-gray-900 dark:text-white font-bold;
}

.user-menu-list {
  @apply py-2;
}

.user-menu-item {
  @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white transition-colors duration-150;
}

.user-menu-button {
  @apply w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white transition-colors duration-150;
}

.user-dropdown-overlay {
  @apply fixed bg-black opacity-50 top-0 left-0 w-full h-full z-40;
}

// Main Navigation
.nav-main {
  @apply relative bg-primary-100 dark:bg-neutral-800 text-sm flex-col text-white items-center;
}

// Mobile Navigation Header
.nav-mobile-header {
  @apply nav-bar-ul px-3 flex md:hidden;
}

.nav-mobile-home {
  @apply flex items-center gap-1 text-white;
}

.nav-mobile-home-icon {
  @apply h-6 w-6;
}

.nav-mobile-home-text {
  @apply text-sm font-medium;
}

.nav-mobile-toggle {
  @apply ml-auto border-2 rounded-lg cursor-pointer;

  &:hover {
    @apply border-gray-300 dark:border-gray-500;
  }
}

.nav-toggle-icon {
  @apply size-6 text-white;
}

// Desktop Navigation
.nav-bar-ul {
  @apply h-10 items-center mx-auto w-full;
}

.nav-bar-li-pc {
  @apply h-full hover:bg-white font-semibold rounded;

  a {
    @apply px-2 lg:px-3 h-full hover:bg-white items-center flex hover:text-black hover:fill-black fill-white rounded-t-lg transition-colors duration-150;
  }
}

// Mobile Navigation Items
.nav-bar-item-mobie {
  @apply flex h-10 text-sm border-b-[0.5px] dark:border-gray-800;

  a, span {
    @apply px-2 lg:px-3 h-full w-full items-center flex gap-1 transition-colors duration-150;
  }
}

