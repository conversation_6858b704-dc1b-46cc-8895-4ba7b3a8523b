import { Injectable } from '@angular/core';


import { audioSpeedSetting, autoNextAudioSetting, autoZoomOutSetting, bgColorSetting, fixedHeaderSetting, fixedToolbarSetting, fontSetting, fontSizeSetting, languageSetting, lineHeightSetting, primaryColorSetting, scrollSpeedSetting, textureSetting, themeSetting, volumeSetting } from '../core/constants';
import { BehaviorSubject } from 'rxjs';
import { StorageService } from './storage.service';
import { SettingType } from '@schema';
import { SettingOption } from '../dataSource/schema/SettingOption';




@Injectable({
  providedIn: 'root'
})
export class SettingService {
  settingData: Map<SettingType, SettingOption> = new Map<SettingType, SettingOption>();
  SettingSubjects: Map<SettingType, BehaviorSubject<SettingOption>> = new Map<SettingType, BehaviorSubject<SettingOption>>();
  OnSettingChange(settingType: SettingType): BehaviorSubject<SettingOption> {
    if (!this.SettingSubjects.has(settingType)) {
      this.SettingSubjects.set(settingType, new BehaviorSubject<SettingOption>(this.settingData.get(settingType)!));
    }
    return this.SettingSubjects.get(settingType)!;
  }
  constructor(private storageService: StorageService) {
    this.settingData.set(SettingType.Theme, themeSetting);
    this.settingData.set(SettingType.Language, languageSetting);
    this.settingData.set(SettingType.BgColor, bgColorSetting);
    this.settingData.set(SettingType.PrimaryColor, primaryColorSetting);
    this.settingData.set(SettingType.FixedHeader, fixedHeaderSetting);
    this.settingData.set(SettingType.Texture, textureSetting);
    this.settingData.set(SettingType.FontFamily, fontSetting);
    this.settingData.set(SettingType.FontSize, fontSizeSetting);
    this.settingData.set(SettingType.LineHeight, lineHeightSetting);
    this.settingData.set(SettingType.Volume, volumeSetting);
    this.settingData.set(SettingType.AudioSpeed, audioSpeedSetting);
    this.settingData.set(SettingType.ScrollSpeed, scrollSpeedSetting);
    this.settingData.set(SettingType.AutoNextChapter, autoNextAudioSetting);
    this.settingData.set(SettingType.AutoZoomAudio, autoZoomOutSetting);
    this.settingData.set(SettingType.FixedToolbar, fixedToolbarSetting);
    this.storageService.LoadSetting(this.settingData);
    this.settingData.forEach((value, key) => this.OnSettingChange(key).next(value));

  }

  IsDarkTheme() {
    let setting = this.Get(SettingType.Theme)
    if (!setting) return false;
    return setting.value == 'dark';
  }

  Set(key: SettingType, value: any) {
    let setting = this.settingData.get(key);
    if (!setting) return;
    setting.value = value;
    this.OnSettingChange(key).next(this.settingData.get(key)!);

    this.storageService.SaveSetting(this.settingData);

  }

  OnChange(key: SettingType) {
    this.OnSettingChange(key).next(this.settingData.get(key)!);
  }

  Get(key: SettingType) {
    return this.settingData.get(key);
  }

  GetSettingByGroup(group: number) {
    let result: SettingOption[] = [];
    this.settingData.forEach((value, key) => {
      if (value.group == group) result.push(value);
    });
    return result;
  }


}


