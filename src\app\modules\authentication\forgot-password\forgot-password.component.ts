import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AccountService } from '@services/account.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
    selector: 'app-forgot-password',
    templateUrl: './forgot-password.component.html',
    styleUrl: './forgot-password.component.scss',
    standalone: false
})
export class ForgotPasswordComponent {
  form!: FormGroup;
  submitted = false;
  siteKey = '6Lc2K0QqAAAAAN_xThVy-qbyJfSowBoEmUuLK_TX';
  isSuccess = false;
  constructor(

    private formBuilder: FormBuilder,

    private accountService: AccountService,
    private toastService: ToastService,

  ) {
    this.form = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],

    });
  }
  isControlInvalid(control: string): boolean {
    const ctrl = this.form.get(control);
    return ctrl
      ? (ctrl.invalid && (ctrl.dirty || ctrl.touched)) || this.submitted
      : false;
  }
  onSubmit() {
    this.accountService.ForgetPassword(this.form.value.email).subscribe((res: any) => {
      if (res.status === 1) {
        this.isSuccess = true
        this.submitted = false
      }
      else {
        this.submitted = true;
        this.isSuccess = false
        this.toastService.show(ToastType.Error, res.message);
      }
    })
  }
}
