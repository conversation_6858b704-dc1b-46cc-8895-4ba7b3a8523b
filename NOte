ALTER TABLE notification ADD COLUMN expires_at TIMESTAMP;

ALTER TABLE notification
ALTER COLUMN params TYPE JSONB
USING params::JSON<PERSON>;



ALTER TABLE USER_NOTIFICATION
DROP CONSTRAINT user_notification_ntfid_fkey, -- <PERSON><PERSON><PERSON> ràng buộc cũ
ADD CONSTRAINT user_notification_ntfid_fkey
FOREIGN KEY (NtfID) REFERENCES NOTIFICATION(ID) ON DELETE CASCADE;


-- --delete None
-- SELECT * FROM chapter  where pages = None


-- ALTER TABLE chapter
-- ALTER COLUMN pages TYPE JSONB
-- USING pages::JSONB;
-- UPDATE chapter
-- SET pages = REPLACE(pages, '''', '"');




-- DROP FUNCTION IF EXISTS get_latest_chapter(INT);
-- CREATE OR REPLACE FUNCTION get_latest_chapter(comic_id INT)
-- RETURNS TABLE (
--     ID INT,
--     ComicID INT,
--     Title VARCHAR,
--     URL REAL,
--     ViewCount INT,
--     UpdateAt TIMESTAMP,
--     Pages JSONB
-- ) AS $$
-- BEGIN
--     RETURN QUERY
--     SELECT *
--     FROM CHAPTER
--     WHERE CHAPTER.ComicID = comic_id
--     ORDER BY CHAPTER.url DESC
--     LIMIT 1;
-- END;
-- $$ LANGUAGE plpgsql;
