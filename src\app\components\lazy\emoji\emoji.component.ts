import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { EMOJI_CONTENTS } from './utils/constants';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
    selector: 'app-emoji',
    templateUrl: './emoji.component.html',
    styleUrl: './emoji.component.scss',
    standalone: true,
    imports: [CommonModule],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class EmojiComponent implements IPopupComponent {

  emoji_contents = EMOJI_CONTENTS;
  activate = 1;
  isVisible = false;
  constructor(private cd: ChangeDetectorRef) { }

  @Output() emojiSelect = new EventEmitter<{ name: string; path: string }>();

  selectPacket(activate_id: number): void {
    this.activate = activate_id;
    if (this.isVisible) {
      this.togglePopover();
    }
  }
  show(object: any): Promise<any> {
    this.setVisible(true);
    return new Promise((resolve) => {
      resolve({});
    });
  }
  setVisible(isVisible: boolean): void {
    this.isVisible = isVisible;
    this.cd.detectChanges();
  }

  selectEmoji(name: string, path: string): void {
    this.emojiSelect.emit({ name, path });
  }

  handleEmojiClick(index: number): void {
    const activeEmoji = this.emoji_contents.find(
      (content) => content.id === this.activate
    );
    if (!activeEmoji) return;

    const name = this.getAttrTarget(activeEmoji.name, index);
    const path = this.getEmojiPath(this.activate, index);

    this.selectEmoji(name, path);
  }

  getAttrTarget(name: string, index: number): string {
    return `${name}_${index}`;
  }

  togglePopover(event?: Event): void {
    if (event) {
      event.stopPropagation(); // Ngăn sự kiện lan lên cha
    }
    this.setVisible(!this.isVisible);
  }

  getEmojiPath(activate: number, index: number): string {
    const activeEmoji = this.emoji_contents.find(
      (content) => content.id === activate
    );
    if (!activeEmoji || index <= 0 || index > activeEmoji.length) return '';

    return `/emoji/data/${activeEmoji.name}/${index}.gif`;
  }

  getEmojiList(): number[] {
    const activeEmoji = this.emoji_contents.find(
      (content) => content.id === this.activate
    );
    return activeEmoji
      ? Array.from({ length: activeEmoji.length }, (_, i) => i + 1)
      : [];
  }
}
