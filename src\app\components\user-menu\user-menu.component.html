<div class="user-menu-wrapper">
  <!-- Authenticated User Trigger -->
  <button
    *ngIf="isAuthenticated"
    (appClickOutside)="closeMenu()"
    (click)="toggleMenu()"
    class="user-menu-trigger authenticated"
    [class.active]="isOpen"
    aria-label="User menu"
    [attr.aria-expanded]="isOpen"
  >
    <div class="user-avatar-container">
      <img
        loading="lazy"
        class="user-avatar"
        [src]="user?.avatar || 'default_avatar.jpg'"
        onerror="this.src='/default_avatar.jpg'"
        alt="User avatar"
      />
      <div class="user-status-indicator"></div>
    </div>

  </button>

  <!-- Guest User Trigger -->
  <button
    *ngIf="!isAuthenticated"
    (appClickOutside)="closeMenu()"
    (click)="toggleMenu()"
    class="user-menu-trigger guest"
    [class.active]="isOpen"
    aria-label="Guest user menu"
    [attr.aria-expanded]="isOpen"
  >
    <div class="guest-avatar-container">
      <svg class="guest-avatar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
      </svg>
    </div>
  </button>

  <!-- Dropdown Container -->
  <div
    class="user-dropdown-container"
    [class.visible1]="isOpen"
    [class.hidden]="!isOpen"
  >
    <div
      appFadeIn
      class="user-dropdown"
      [class.authenticated-dropdown]="isAuthenticated"
      [class.guest-dropdown]="!isAuthenticated"
    >
      <!-- Authenticated User Content -->
      <ng-container *ngIf="isAuthenticated">
        <!-- User Profile Header -->
        <div class="user-profile-header">
          <div class="user-avatar-large">
            <img
              loading="lazy"
              [src]="user?.avatar || 'default_avatar.jpg'"
              onerror="this.src='/default_avatar.jpg'"
              alt="User avatar"
            />
            <div class="user-status-badge">
              <span class="status-dot"></span>
              <span class="status-text">{{ statusText }}</span>
            </div>
          </div>
          <div class="user-profile-info">
            <h3 class="user-full-name">{{ user?.firstName }}</h3>
            <p class="user-email">{{ user?.email }}</p>
            <div class="user-stats" *ngIf="userStats">
              <span class="stat-item">
                <svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
                <span>{{ userStats.readCount || 0 }} truyện đã đọc</span>
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions" *ngIf="showQuickActions">
          <button
            class="quick-action-btn"
            title="Thông báo"
            (click)="onQuickAction('notifications')"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5L6 8h4v13l4.5-4.5"/>
            </svg>
            <span class="notification-badge" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
          </button>
          <button
            class="quick-action-btn"
            title="Yêu thích"
            (click)="onQuickAction('favorites')"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
          </button>
          <button
            class="quick-action-btn"
            title="Lịch sử"
            (click)="onQuickAction('history')"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </button>
        </div>

        <!-- Navigation Menu -->
        <nav class="user-navigation">
          <ul class="nav-menu-list">
            <li class="nav-menu-item" *ngFor="let item of menuItems; trackBy: trackByMenuItem">
              <a
                *ngIf="item.type === 'link'; else buttonTemplate"
                [routerLink]="item.route"
                class="nav-link"
                (click)="onMenuItemClick(item)"
              >
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path [attr.d]="item.iconPath" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
                </svg>
                <span class="nav-text">{{ item.label }}</span>
                <svg class="nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
              </a>
              <ng-template #buttonTemplate>
                <button
                  (click)="onMenuItemClick(item)"
                  class="nav-link nav-button"
                >
                  <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path [attr.d]="item.iconPath" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
                    <path *ngIf="item.secondaryIconPath" [attr.d]="item.secondaryIconPath" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
                  </svg>
                  <span class="nav-text">{{ item.label }}</span>
                  <svg class="nav-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                  </svg>
                </button>
              </ng-template>
            </li>
          </ul>
        </nav>

        <!-- Divider -->
        <div class="menu-divider"></div>

        <!-- Logout Section -->
        <div class="logout-section">
          <button
            (click)="onLogout()"
            class="logout-button"
          >
            <svg class="logout-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
            </svg>
            <span>Đăng xuất</span>
          </button>
        </div>
      </ng-container>

      <!-- Guest User Content -->
      <ng-container *ngIf="!isAuthenticated">
        <!-- Guest Profile Header -->
        <div class="guest-profile-header">
          <div class="guest-avatar-large">
            <svg class="guest-avatar-icon-large" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
          </div>
          <div class="guest-profile-info">
            <h3 class="guest-welcome-title">Chào mừng bạn!</h3>
            <p class="guest-welcome-subtitle">Đăng nhập để trải nghiệm đầy đủ tính năng</p>
            <div class="guest-features">
              <div class="feature-item">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                </svg>
                <span>Lưu truyện yêu thích</span>
              </div>
              <div class="feature-item">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Theo dõi lịch sử đọc</span>
              </div>
              <div class="feature-item">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5L6 8h4v13l4.5-4.5"/>
                </svg>
                <span>Nhận thông báo mới</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Authentication Buttons -->
        <div class="auth-section">
          <button
            (click)="onLogin()"
            class="auth-button login-button"
          >
            <svg class="auth-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
            </svg>
            <span>Đăng nhập</span>
          </button>
          <button
            (click)="onRegister()"
            class="auth-button register-button"
          >
            <svg class="auth-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
            </svg>
            <span>Đăng ký miễn phí</span>
          </button>
        </div>

        <!-- Guest Quick Links -->
        <div class="guest-quick-links">
          <div class="quick-links-title">Khám phá ngay</div>
          <div class="quick-links-grid">
            <a href="/truyen-hot" class="quick-link-item" (click)="closeMenu()">
              <svg class="quick-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 18.657A8 8 0 106.343 5.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"/>
              </svg>
              <span>Truyện Hot</span>
            </a>
            <a href="/xep-hang" class="quick-link-item" (click)="closeMenu()">
              <svg class="quick-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              <span>Xếp hạng</span>
            </a>
            <a href="/tim-truyen" class="quick-link-item" (click)="closeMenu()">
              <svg class="quick-link-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
              <span>Tìm kiếm</span>
            </a>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="dropdown-backdrop" (click)="closeMenu()"></div>
  </div>
</div>
