// Modern Grid Comic Component - Comic Website Design
.block-title {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

// Modern Grid Type Switch
.grid-switch-container {
  @apply relative flex items-center bg-gray-100 dark:bg-neutral-700 rounded-md p-0.5 shadow-sm border border-gray-200 dark:border-neutral-600;
}

.switch-background {
  @apply absolute inset-1 pointer-events-none;
}

.switch-indicator {
  @apply w-1/2 h-full bg-neutral-700 rounded-md shadow-sm transition-all duration-300 ease-in-out;

  &.switch-indicator-list {
    @apply translate-x-0;
  }

  &.switch-indicator-grid {
    @apply translate-x-full;
  }
}

.switch-button {
  @apply relative flex items-center gap-2 p-2 min-w-10 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-all duration-200 rounded-md border-none cursor-pointer z-10 justify-center;

  &.switch-button-active {
    @apply text-white;
  }
}

.switch-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}
