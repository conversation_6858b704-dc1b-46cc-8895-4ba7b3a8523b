<ol class="breadcrumb-list">
  <li *ngFor="let link of Links; let i = index" class="breadcrumb-item">
    <a
      [ngClass]="{
        'breadcrumb-item-last': i === Links.length - 1
      }"
      class="breadcrumb-name"
      [routerLink]="[link.url]"
    >
      {{ link.label }}
    </a>
    <svg
      *ngIf="i < Links.length - 1"
      class="breadcrumb-divider"
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <path d="m9 18 6-6-6-6"></path>
    </svg>
  </li>
</ol>
