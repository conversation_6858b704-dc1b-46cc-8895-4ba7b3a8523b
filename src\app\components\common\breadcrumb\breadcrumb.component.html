<!-- Modern Breadcrumb Component - Comic Website Design -->
<nav class="breadcrumb-container"
     [class.breadcrumb-transparent]="style === 'transparent'"
     [class.breadcrumb-default]="style === 'default'"
     aria-label="Breadcrumb navigation">
  <!-- Breadcrumb Header -->
  <div class="breadcrumb-header">
    <div class="breadcrumb-icon-wrapper">
      <svg class="breadcrumb-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
        <polyline points="9,22 9,12 15,12 15,22"/>
      </svg>
    </div>

    <!-- Breadcrumb List -->
    <ol class="breadcrumb-list" itemscope itemtype="https://schema.org/BreadcrumbList">
      <li
        *ngFor="let link of Links; let i = index; trackBy: trackByLink"
        class="breadcrumb-item"
        [class.breadcrumb-item-current]="i === Links.length - 1"
        itemprop="itemListElement"
        itemscope
        itemtype="https://schema.org/ListItem"
      >
        <!-- Breadcrumb Link -->
        <a
          *ngIf="i < Links.length - 1; else currentPage"
          class="breadcrumb-link"
          [routerLink]="[link.url]"
          [title]="link.label"
          itemprop="item"
        >
          <span class="breadcrumb-text" itemprop="name">{{ link.label }}</span>
        </a>

        <!-- Current Page (non-clickable) -->
        <ng-template #currentPage>
          <span class="breadcrumb-current" itemprop="name">
            {{ link.label }}
          </span>
        </ng-template>

        <!-- Schema.org position -->
        <meta itemprop="position" [content]="i + 1">

        <!-- Divider -->
        <div *ngIf="i < Links.length - 1" class="breadcrumb-divider">
          <svg class="divider-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polyline points="9,18 15,12 9,6"/>
          </svg>
        </div>
      </li>
    </ol>
  </div>
</nav>
