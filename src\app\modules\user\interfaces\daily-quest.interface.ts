export interface DailyQuest {
  id: string;
  title: string;
  description: string;
  type: QuestType;
  target: number;
  current: number;
  reward: QuestRew<PERSON>;
  status: QuestStatus;
  icon: string;
  difficulty: QuestDifficulty;
  expiresAt: Date;
  completedAt?: Date;
}

export interface QuestReward {
  type: RewardType;
  amount: number;
  item?: string;
  description: string;
  itemTemplate?: ItemTemplate; // New: reference to item template
}

export interface ItemTemplate {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  category: string; // Simplified: string instead of object
  rarity: ItemRarity;
  isActive: boolean;
}

export interface UserInventoryItem {
  id: number;
  userId: number;
  itemTemplateId: number;
  quantity: number;
  obtainedAt: Date;
  expiresAt?: Date;
  metadata?: string;
  itemTemplate: ItemTemplate;
}

export interface UserEquippedItem {
  userId: number;
  itemTemplateId: number;
  slotType: EquipmentSlotType;
  equippedAt: Date;
  itemTemplate: ItemTemplate;
}

export interface WeeklyQuest extends DailyQuest {
  weekNumber: number;
  resetDay: string;
}

export interface QuestProgress {
  questId: string;
  progress: number;
  lastUpdated: Date;
}

export interface UserQuestStats {
  totalCompleted: number;
  streakDays: number;
  weeklyCompleted: number;
  monthlyCompleted: number;
  totalRewardsEarned: number;
  level: number;
  experience: number;
  nextLevelExp: number;
}

export enum QuestType {
  READ_CHAPTERS = 'read_chapters',
  READ_COMICS = 'read_comics',
  ADD_FAVORITES = 'add_favorites',
  WRITE_COMMENTS = 'write_comments',
  RATE_COMICS = 'rate_comics',
  SHARE_COMICS = 'share_comics',
  LOGIN_STREAK = 'login_streak',
  DISCOVER_NEW = 'discover_new',
  COMPLETE_SERIES = 'complete_series',
  WEEKLY_READING = 'weekly_reading'
}

export enum QuestStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
  LOCKED = 'locked'
}

export enum QuestDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  LEGENDARY = 'legendary'
}

export enum RewardType {
  COINS = 'coins',
  EXPERIENCE = 'experience',
  BADGE = 'badge',
  AVATAR_FRAME = 'avatar_frame',
  TITLE = 'title',
  PREMIUM_DAYS = 'premium_days',
  ITEM = 'item' // New: for item-based rewards
}

export enum ItemRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

export enum EquipmentSlotType {
  AVATAR_FRAME = 'avatar_frame',
  TITLE = 'title',
  BADGE = 'badge'
}
