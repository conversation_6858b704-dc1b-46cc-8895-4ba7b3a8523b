<ng-container>
  <app-breadcrumb
    class="z-10 my-2 md:container mx-auto flex"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: '<PERSON>ịch sử', url: '' }
    ]"
  >
  </app-breadcrumb>

  <div class="md:container mx-auto">
    <div class="text-start" *ngIf="isLoading; else loadedContent">
      <app-spinner [sizeSpinner]="'40'"></app-spinner>
    </div>
    <ng-template #loadedContent>
      <app-grid-comic
        *ngIf="comics.length > 0; else empty"
        [listComics]="comics"
        [nPreview]="comicPerPage"
        [title]="'Truyện đã xem'"
        [_class]="
          'grid gap-[12px] grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 mx-3 lg:mx-0'
        "
        [showCheckboxes]="true"
        (clickEvent)="onRemoveSelectedComics($event)"
        [eventName]="'Xóa'"
      >
        <svg
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6 mr-1"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="1.5"
          ></circle>
          <path
            d="M12 8V12L14.5 14.5"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></path>
        </svg>
      </app-grid-comic>

      <app-pagination
        *ngIf="totalpage > 1 && !ssr()"
        [totalpage]="totalpage"
        [currentPage]="page"
        (OnChange)="onChangePage($event)"
      >
      </app-pagination>
      <ng-template #empty>
        <div
          class="w-1/2 mt-20 lg:min-h-1/2 lg:w-40 flex justify-center items-center mx-auto"
        >
          <app-empty [message]="'Không có truyện đã xem'"></app-empty>
        </div>
      </ng-template>
    </ng-template>
  </div>
</ng-container>
