// src/app/toast/toast.component.ts
import {
    animate,
    state,
    style,
    transition,
    trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, ViewEncapsulation } from '@angular/core';
import { ToastService } from '@services/toast.service';


@Component({
    selector: 'app-toast',
    templateUrl: './toast.component.html',
    styleUrl: './toast.component.scss',
    animations: [
        trigger('toastAnimation', [
            state('void', style({
                opacity: 0,
                transform: 'translateY(100%)',
            })),
            state('enter', style({
                opacity: 1,
                transform: 'translateY(0)',
            })),
            state('leave', style({
                opacity: 0,
                height: 0,
            })),
            transition('void => enter', [animate('300ms ease-in')]),
            transition('enter => leave', [animate('300ms ease-out')]),
        ]),
    ],
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    encapsulation: ViewEncapsulation.None,
    imports: [CommonModule],
})
export class ToastComponent {
    cd = inject(ChangeDetectorRef);
    constructor(public toastService: ToastService) { }
    ngAfterViewInit() {
        this.toastService.toasts$.subscribe(() =>
            this.cd.detectChanges()
        );
    }
}   
