import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Genre } from '@schema';

@Component({
    selector: 'app-genre-catagories',
    imports: [CommonModule, RouterModule],
    templateUrl: './genre-catagories.component.html',
    styleUrl: './genre-catagories.component.scss',
    standalone: true,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush
    
})
export class GenreCatagoriesComponent implements OnInit {
  @Input() listGenres: Genre[] = new Array<Genre>();
  @Input() routerLinkGenres = true;
  @Input() statusGenres: any = {};
  @Output() clickGenres: EventEmitter<any> = new EventEmitter<any>();
  catagoriesGenre = new Map<string, Genre[]>();
  filteredCatagoriesGenre = new Map<string, Genre[]>();
  searchTerm = '';
  genreHovered: Genre | null = null;

  ngOnInit(): void {
    this.updateCategories();
  }

  private updateCategories(): void {
    const [genreCountry, genreCommon] = this.partition(this.listGenres, (genre: Genre) =>
      ['Manga', 'Manhua', 'Manhwa'].includes(genre.title),
    );

    this.catagoriesGenre.set('countries', genreCountry);
    this.catagoriesGenre.set('genreCommon', genreCommon);

    this.filteredCatagoriesGenre = new Map(this.catagoriesGenre);
  }
  partition(array: any = [], predicate: (i: any) => boolean) {
    const truthy: any[] = [];
    const falsy: any[] = [];

    array.forEach((item: any) => {
      if (predicate(item)) {
        truthy.push(item); // If the condition is true, add to the truthy array
      } else {
        falsy.push(item); // Otherwise, add to the falsy array
      }
    });

    return [truthy, falsy]; // Return both arrays
  }
  public clickGenre(id: any): void {
    this.clickGenres.emit(id);
    // console.log(this.statusGenres[genre.id]);
  }

  filterGenres(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchTerm = input.value;

    if (!this.searchTerm) {
      this.filteredCatagoriesGenre = new Map(this.catagoriesGenre);
      return;
    }

    const filtered = new Map<string, Genre[]>();

    for (const [category, genres] of this.catagoriesGenre) {
      const filteredGenres = genres.filter((genre) =>
        genre.title.toLowerCase().includes(this.searchTerm.toLowerCase()),
      );
      if (filteredGenres.length > 0) {
        filtered.set(category, filteredGenres);
      }
    }

    this.filteredCatagoriesGenre = filtered;
  }

  // OnSearchChange(e: Event) {
  //   clearTimeout(this.typingTimer);
  //   this.typingTimer = setTimeout(() => {
  //     this.searchText = this.SearchField.nativeElement.value;
  //   }, this.doneTypingInterval);
  // }
  // searchCategories(searchText?: string): void {
  //   if (!searchText?.trim()) {
  //     // If search text is empty, reset to show all categories
  //     this.filteredCatagoriesGenre = new Map(this.catagoriesGenre);
  //     return;
  //   }

  //   this.filteredCatagoriesGenre = new Map();
  //   for (const [category, genres] of this.catagoriesGenre) {
  //     const filteredGenres = genres.filter((genre) =>
  //       genre.title.toLowerCase().includes(searchText.toLowerCase()),
  //     );
  //     if (filteredGenres.length > 0) {
  //       this.filteredCatagoriesGenre.set(category, filteredGenres);
  //     }
  //   }
  // }
}
