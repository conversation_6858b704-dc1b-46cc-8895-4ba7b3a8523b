// Simplified Reading History Component Styles
.reading-history-container {
  @apply space-y-6;
}

// Page Header
.page-header {
  @apply space-y-2 mb-6;
}

.page-title {
  @apply flex items-center gap-3 text-2xl font-bold text-gray-900 dark:text-white;
}

.title-icon {
  @apply w-8 h-8 text-blue-500;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

// Loading State
.loading-state {
  @apply text-center py-12;
}

// Empty State
.empty-state {
  @apply bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm p-12;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);

  .dark & {
    background: rgba(31, 41, 55, 0.95);
  }
}

.empty-actions {
  @apply mt-6 text-center;
}

.explore-btn {
  @apply inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &:focus {
    @apply outline-none ring-2 ring-blue-500/50;
  }
}

.btn-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Responsive Design
@media (max-width: 768px) {
  .page-title {
    @apply text-xl;
  }

  .title-icon {
    @apply w-6 h-6;
  }
}

// Accessibility
.explore-btn:focus {
  @apply outline-none;
}

// High Contrast Mode
@media (prefers-contrast: high) {
  .empty-state {
    @apply border-2 border-black dark:border-white;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  .explore-btn {
    @apply transition-none;
  }
}

// Performance Optimizations
.empty-state {
  transform: translateZ(0);
  backface-visibility: hidden;
}
