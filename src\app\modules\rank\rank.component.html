<app-breadcrumb
  class="z-10 mt-3 mb-5 md:container mx-auto flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Xếp hạng', url: '' }
  ]"
>
</app-breadcrumb>
<div id="content" class="md:container mx-auto mt-5 dark:text-white">
  <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
    <div>
      <h4 class="text-lg font-bold bg-secondary-100 text-white py-1 px-4 inline-flex rounded-sm">
        Bảng xếp hạng
      </h4>
    </div>
    <div class="flex justify-end gap-2">
      <div class="w-full xl:w-1/3">
        <app-selection-2
          [options]="dataView.sorts"
          (selectedValueChange)="OnSortOptionChange($event)"
          [selectedValue]="selectOptions.sorts.value"
        >
        </app-selection-2>
      </div>
      <div class="w-full xl:w-1/4">
        <app-selection-2
          [options]="dataView.status"
          (selectedValueChange)="OnStatusOptionChange($event)"
          [selectedValue]="selectOptions.status.value"
        >
        </app-selection-2>
      </div>

      <!-- <div class="flex py-2 gap-2 justify-center items-center mt-3"> -->
    </div>
  </div>

  <div class="mx-auto p-2 xl:p-4">
    <div class="gap-2 xl:gap-4 grid grid-cols-3 xl:grid-cols-5" *ngIf="listTopComics">
      <!-- TOP 2 -->
      <div class="col-span-1 xl:col-start-2">
        <div class="bg-white dark:bg-neutral-800 p-4 xl:p-8 rounded-lg shadow-md h-full">
          <div class="flex justify-center mb-2 xl:h-50">
            <a
              class="relative w-full shrink-0 mt-3 xl:mb-2 hover:-translate-y-2 duration-300 ease-in-out"
              [title]="listTopComics[1].title"
              [routerLink]="['/truyen-tranh', listTopComics[1].url + '-' + listTopComics[1].id]"
              (mouseover)="showDetails(listTopComics[1])"
            >
              <div
                class="absolute left-[calc(50%-12px)] -top-4 rounded-full w-8 h-8 bg-secondary-100 text-gray-400 text-xl xl:text-2xl mx-auto font-bold flex items-center justify-center"
              >
                <svg
                  width="27"
                  height="27"
                  viewBox="0 0 27 27"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.5 16.875C17.8492 16.875 21.375 13.3492 21.375 9C21.375 4.65076 17.8492 1.125 13.5 1.125C9.15076 1.125 5.625 4.65076 5.625 9C5.625 13.3492 9.15076 16.875 13.5 16.875Z"
                    stroke="#848484"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M9.23625 15.6262L7.875 25.875L13.5 22.5L19.125 25.875L17.7637 15.615"
                    stroke="#848484"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M10.8755 13C10.6069 13 10.4727 12.8836 10.4727 12.6509V12.3931C10.4727 12.2069 10.5586 12.0225 10.7305 11.8398L12.3364 10.1479C12.8198 9.6359 13.262 9.18115 13.6631 8.78369C14.0641 8.38265 14.2646 7.97803 14.2646 7.56982C14.2646 7.16162 14.1626 6.86263 13.9585 6.67285C13.758 6.47949 13.502 6.38281 13.1904 6.38281C12.6963 6.38281 12.3525 6.4974 12.1592 6.72656C11.9658 6.95573 11.8262 7.20101 11.7402 7.4624C11.6579 7.71663 11.5075 7.80794 11.2891 7.73633L10.7466 7.55371C10.5711 7.49284 10.521 7.32454 10.5962 7.04883C10.7358 6.53678 11.0205 6.09814 11.4502 5.73291C11.8799 5.3641 12.5173 5.17969 13.3623 5.17969C14.2432 5.17969 14.8823 5.40706 15.2798 5.86182C15.6772 6.31299 15.876 6.80355 15.876 7.3335C15.876 8.02816 15.5859 8.63867 15.0059 9.16504C14.4258 9.68783 13.8994 10.1981 13.4268 10.6958L12.5137 11.6572V11.6895C12.7285 11.6751 12.9076 11.668 13.0508 11.668H15.5591C15.8276 11.668 15.9619 11.7843 15.9619 12.0171V12.6509C15.9619 12.8836 15.8276 13 15.5591 13H10.8755Z"
                    fill="#848484"
                  />
                </svg>
                <!-- <div class="text-lg rounded-full text-center p-0.5">2</div> -->
              </div>
              <img
                loading="lazy"
                class="h-full w-full rounded object-cover shadow-sm"
                [src]="listTopComics[1].coverImage"
                [alt]="listTopComics[1].title"
                onerror="this.src='/option2.png'"
              />
            </a>
          </div>
          <div class="text-center">
            <h2 class="text-sm xl:text-lg font-bold uppercase">
              {{ listTopComics[1].title }}
            </h2>
            <p class="text-gray-500 font-light uppercase text-xs">
              {{ listTopComics[1].author || 'Đang cập nhật' }}
            </p>
            <div
              class="text-sm text-center uppercase flex justify-center gap-1 mx-auto text-primary-100 font-semibold"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                class="feather feather-eye icon small text-icon-contrast text-undefined"
                viewBox="0 0 24 24"
              >
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {{ listTopComics[1].viewCount | numeral }}
            </div>
          </div>
        </div>
      </div>

      <div class="col-span-1 xl:col-start-3">
        <div class="bg-white dark:bg-neutral-800 p-4 xl:p-4 rounded-lg shadow-md">
          <div class="flex justify-center mb-2 xl:h-50 h-full">
            <a
              class="relative w-full shrink-0 mt-3 xl:mb-2 hover:-translate-y-2 duration-300 ease-in-out"
              [title]="listTopComics[0].title"
              [routerLink]="['/truyen-tranh', listTopComics[0].url + '-' + listTopComics[0].id]"
              (mouseover)="showDetails(listTopComics[0])"
            >
              <div
                class="absolute left-[calc(50%-12px)] -top-4 rounded-full w-9 h-9 bg-secondary-100 text-yellow-400 text-xl xl:text-2xl mx-auto font-bold flex items-center justify-center"
              >
                <svg
                  width="27"
                  height="28"
                  viewBox="0 0 27 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect width="27" height="28" fill="none" />
                  <path
                    d="M13.5 6.54839C14.0855 6.54839 14.6469 6.29859 15.0609 5.85394C15.4749 5.4093 15.7075 4.80624 15.7075 4.17742C15.7075 3.5486 15.4749 2.94553 15.0609 2.50089C14.6469 2.05625 14.0855 1.80645 13.5 1.80645C12.9145 1.80645 12.3531 2.05625 11.9391 2.50089C11.5251 2.94553 11.2925 3.5486 11.2925 4.17742C11.2925 4.80624 11.5251 5.4093 11.9391 5.85394C12.3531 6.29859 12.9145 6.54839 13.5 6.54839ZM9.12184 6.15322C8.28943 6.15322 7.68238 6.78548 7.48462 7.48195C7.11671 8.77611 5.99457 9.71462 4.67009 9.71462C4.2102 9.71462 3.7779 9.60101 3.39619 9.40343C3.04667 9.22066 2.62817 9.24042 2.29245 9.4627C1.75898 9.81341 1.58422 10.5543 1.89694 11.1372L6.21532 19.1935H8.76312L4.94602 12.0757C6.80858 11.967 8.409 10.7964 9.22761 9.12187C10.2394 10.4457 11.7754 11.2903 13.4954 11.2903C15.2154 11.2903 16.7514 10.4457 17.7632 9.12187C18.5818 10.8013 20.1822 11.972 22.0448 12.0757L18.2369 19.1935H20.7847L25.1031 11.1372C25.4158 10.5593 25.241 9.81834 24.7076 9.46764C24.3718 9.24536 23.9533 9.2256 23.6038 9.40836C23.2175 9.60595 22.7898 9.71955 22.3299 9.71955C21.0054 9.71955 19.8833 8.78105 19.5154 7.48689C19.3176 6.78548 18.7106 6.15322 17.8782 6.15322C17.2113 6.15322 16.6687 6.57308 16.3881 7.10655C15.8179 8.19324 14.7371 8.91935 13.5 8.91935C12.2629 8.91935 11.1821 8.1883 10.6119 7.10655C10.3313 6.57308 9.79328 6.15322 9.12184 6.15322ZM7.85254 23.1452H19.1475L19.9109 24.7258H7.09371L7.85713 23.1452H7.85254ZM20.8997 21.6287C20.6467 21.1051 20.1408 20.7742 19.5936 20.7742H7.40644C6.85457 20.7742 6.35329 21.1051 6.10035 21.6287L4.88164 24.1578C4.74367 24.4443 4.67009 24.7604 4.67009 25.0814C4.67009 26.1928 5.50709 27.0968 6.54644 27.0968H20.4582C21.4929 27.0968 22.3345 26.1978 22.3345 25.0814C22.3345 24.7604 22.2609 24.4443 22.123 24.1578L20.9043 21.6287H20.8997Z"
                    fill="#FEC446"
                  />
                  <path
                    d="M15.0761 12.6063V19.879H13.5385V14.0658H13.4959L11.8304 15.1099V13.7462L13.6308 12.6063H15.0761Z"
                    fill="#FEC446"
                  />
                </svg>
              </div>
              <img
                loading="lazy"
                class="h-full w-full rounded object-cover shadow-sm"
                [src]="listTopComics[0].coverImage"
                [alt]="listTopComics[0].title"
                onerror="this.src='/option2.png'"
              />
            </a>
          </div>
          <div class="text-center">
            <h2 class="text-sm xl:text-lg font-bold uppercase">
              {{ listTopComics[0].title }}
            </h2>
            <p class="text-gray-500 font-light uppercase text-xs">
              {{ listTopComics[0].author || 'Đang cập nhật' }}
            </p>
            <div
              class="text-sm text-center uppercase flex justify-center gap-1 mx-auto text-primary-100 font-semibold"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                class="feather feather-eye icon small text-icon-contrast text-undefined"
                viewBox="0 0 24 24"
              >
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {{ listTopComics[0].viewCount | numeral }}
            </div>
          </div>
        </div>
      </div>

      <!-- Right item -->
      <div class="col-span-1 xl:col-start-4">
        <div class="bg-white dark:bg-neutral-800 p-4 xl:p-8 rounded-lg shadow-md">
          <div class="flex justify-center mb-2 xl:h-50 h-full">
            <a
              class="relative w-full shrink-0 mt-3 xl:mb-2 hover:-translate-y-2 duration-300 ease-in-out"
              [title]="listTopComics[2].title"
              [routerLink]="['/truyen-tranh', listTopComics[2].url + '-' + listTopComics[2].id]"
              (mouseover)="showDetails(listTopComics[2])"
            >
              <div
                class="absolute left-[calc(50%-12px)] -top-4 rounded-full w-8 h-8 bg-secondary-100 text-primary-100 flex items-center justify-center text-xl xl:text-2xl mx-auto font-bold"
              >
                <svg
                  width="27"
                  height="27"
                  viewBox="0 0 27 27"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.5 16.875C17.8492 16.875 21.375 13.3492 21.375 9C21.375 4.65076 17.8492 1.125 13.5 1.125C9.15076 1.125 5.625 4.65076 5.625 9C5.625 13.3492 9.15076 16.875 13.5 16.875Z"
                    stroke="#FE6E1A"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M9.23625 15.6262L7.875 25.875L13.5 22.5L19.125 25.875L17.7637 15.615"
                    stroke="#FE6E1A"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M10.7144 12.4521C10.5747 12.334 10.564 12.1657 10.6821 11.9473L10.9292 11.4961C11.0474 11.2777 11.2389 11.2616 11.5039 11.4478C11.7259 11.6053 11.9766 11.7342 12.2559 11.8345C12.5352 11.9312 12.8467 11.9795 13.1904 11.9795C13.6631 11.9795 14.0015 11.8524 14.2056 11.5981C14.4097 11.3403 14.5117 11.0521 14.5117 10.7334C14.5117 10.4076 14.39 10.1157 14.1465 9.85791C13.9066 9.59652 13.5664 9.46582 13.126 9.46582H12.7124C12.4797 9.46582 12.3633 9.37093 12.3633 9.18115V8.59033C12.3633 8.39339 12.4797 8.29492 12.7124 8.29492H13.0347C13.3641 8.29492 13.6541 8.19287 13.9048 7.98877C14.159 7.78467 14.2861 7.52865 14.2861 7.2207C14.2861 6.98079 14.202 6.78206 14.0337 6.62451C13.869 6.46338 13.5986 6.38281 13.2227 6.38281C12.8682 6.38281 12.562 6.43473 12.3042 6.53857C12.0464 6.64242 11.8423 6.76774 11.6919 6.91455C11.4771 7.12581 11.2926 7.11686 11.1387 6.8877L10.8271 6.43115C10.709 6.25928 10.7448 6.08382 10.9346 5.90479C11.1924 5.65771 11.5254 5.4751 11.9336 5.35693C12.3418 5.23877 12.741 5.17969 13.1313 5.17969C14.0516 5.17969 14.7355 5.3641 15.1831 5.73291C15.6307 6.09814 15.8545 6.54574 15.8545 7.07568C15.8545 7.46956 15.7005 7.82943 15.3926 8.15527C15.0846 8.47754 14.7588 8.69954 14.415 8.82129V8.84277C14.8232 8.94303 15.21 9.16325 15.5752 9.50342C15.9404 9.84001 16.123 10.259 16.123 10.7603C16.123 11.5444 15.876 12.1442 15.3818 12.5596C14.8877 12.9749 14.134 13.1826 13.1206 13.1826C12.6945 13.1826 12.2523 13.1182 11.7939 12.9893C11.3392 12.8568 10.9793 12.6777 10.7144 12.4521Z"
                    fill="#FE6E1A"
                  />
                </svg>
              </div>
              <img
                loading="lazy"
                class="h-full w-full rounded object-cover shadow-sm"
                [src]="listTopComics[2].coverImage"
                [alt]="listTopComics[2].title"
                onerror="this.src='/option2.png'"
              />
            </a>
          </div>
          <div class="text-center">
            <h2 class="text-sm xl:text-lg font-bold uppercase">
              {{ listTopComics[2].title }}
            </h2>
            <p class="text-gray-500 font-light uppercase text-xs">
              {{ listTopComics[2].author || 'Đang cập nhật' }}
            </p>
            <div
              class="text-sm text-center uppercase flex justify-center gap-1 mx-auto text-primary-100 font-semibold"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                class="feather feather-eye icon small text-icon-contrast text-undefined"
                viewBox="0 0 24 24"
              >
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {{ listTopComics[2].viewCount | numeral }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="m-2" id="details-top-comic" *ngIf="selectedComic">
      <div class="flex justify-center gap-3">
        <div class="text-sm text-center flex gap-1 items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            class="feather feather-star icon rel"
            viewBox="0 0 24 24"
            style="color: currentcolor"
          >
            <path
              d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01z"
            ></path></svg
          >{{ selectedComic.rating }}
        </div>
        <div class="text-sm text-center flex gap-1 items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="none"
            viewBox="0 0 24 24"
            class="icon small text-icon-contrast text-undefined"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"
            ></path>
          </svg>
          {{ selectedComic.rating }}
        </div>
        <div class="text-sm text-center uppercase flex gap-1 items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            class="feather feather-eye icon small text-icon-contrast text-undefined"
            viewBox="0 0 24 24"
          >
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          {{ selectedComic.viewCount | numeral }}
        </div>

        <!---->
      </div>
      <div class="flex justify-center space-x-1">
        <a
          *ngFor="let tag of selectedComic?.genres | slice : 0 : 3; index as i"
          class="tag bg-accent"
        >
          @if (i === 0) {
          <span
            class="bg-primary-100 cursor-pointer text-xs font-bold rounded shadow-sm px-2 uppercase text-white"
            >{{ tag.title }}</span
          >
          } @else {
          <span
            class="bg-gray-100 text-[0.7rem] cursor-pointer font-semibold rounded shadow-sm px-2 uppercase text-black"
          >
            {{ tag.title }}
          </span>
          }
        </a>
      </div>
      <div class="mt-2 flex justify-center">
        <p
          class="line-clamp-3 text-sm"
          [innerHTML]="
            selectedComic.description
              | fillDescription : selectedComic.id : selectedComic.title : selectedComic.url
          "
        ></p>
      </div>
    </div>
  </div>

  <ul
    class="flex flex-wrap gap-2 py-2 mx-1 overflow-hidden border-b border-b-accent-10 dark:border-b-neutral-700"
  ></ul>

  <div class="grid grid-cols-1 xl:grid-cols-4 gap-4" id="listComic">
    <div id="comics" class="col-span-1 xl:col-span-full row-start-1">
      <app-grid-comic
        [listComics]="listComics || []"
        [nPreview]="35"
        [title]="'Xếp hạng'"
        [_class]="
          'grid gap-[12px] grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 mx-3 lg:mx-0'
        "
      >
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6">
          <path
            d="M2 12C2 7.28595 2 4.92893 3.46447 3.46447C4.92893 2 7.28595 2 12 2C16.714 2 19.0711 2 20.5355 3.46447C22 4.92893 22 7.28595 22 12C22 16.714 22 19.0711 20.5355 20.5355C19.0711 22 16.714 22 12 22C7.28595 22 4.92893 22 3.46447 20.5355C2 19.0711 2 16.714 2 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          ></path>
          <path
            d="M7 18L7 15"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          ></path>
          <path d="M17 18V9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
          <path
            d="M12 18V12"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          ></path>
        </svg>
      </app-grid-comic>
    </div>
  </div>
  <app-pagination
    (OnChange)="OnChangePage($event)"
    [currentPage]="currentPage"
    [totalpage]="totalpage"
  >
  </app-pagination>
</div>
