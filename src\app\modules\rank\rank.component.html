<app-breadcrumb
  class="z-10 mt-3 mb-5 md:container mx-auto flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Xếp hạng', url: '' }
  ]"
>
</app-breadcrumb>

<div id="content" class="md:container mx-auto mt-5 dark:text-white">
  <!-- Enhanced Ranking Container -->
  <div class="ranking-container">
    <!-- Subtle Decorative Elements -->
    <div class="ranking-decoration star opacity-5">⭐</div>

    <!-- Enhanced Header Section -->
    <div class="ranking-header">
      <div>
        <h1 class="ranking-title">Bảng Xếp Hạng Truyện Tranh</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          Khám phá những bộ truyện tranh được yêu thích nhất
        </p>
      </div>

      <!-- Enhanced Filter Controls -->
      <div class="ranking-filters">
        <div class="filter-group">
          <label class="filter-label">Sắp xếp theo</label>
          <app-selection-2
            class="filter-select"
            [options]="dataView.sorts"
            (selectedValueChange)="OnSortOptionChange($event)"
            [selectedValue]="selectOptions.sorts.value"
          >
          </app-selection-2>
        </div>

        <div class="filter-group">
          <label class="filter-label">Trạng thái</label>
          <app-selection-2
            class="filter-select"
            [options]="dataView.status"
            (selectedValueChange)="OnStatusOptionChange($event)"
            [selectedValue]="selectOptions.status.value"
          >
          </app-selection-2>
        </div>
      </div>
    </div>

    <!-- Enhanced Top 3 Podium Section -->
    <div class="podium-container" *ngIf="listTopComics && listTopComics.length >= 3">
      <div class="text-center mb-5">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">
          Top 3 Truyện Tranh Hàng Đầu
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Những bộ truyện được yêu thích nhất
        </p>
      </div>

      <div class="podium-grid">
        <!-- Second Place -->
        <div class="podium-item second-place">
          <div class="podium-card" (click)="showDetails(listTopComics[1])">
            <div class="rank-badge">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
              </svg>
            </div>
            <a [routerLink]="['/truyen-tranh', listTopComics[1].url + '-' + listTopComics[1].id]">
              <img
                loading="lazy"
                class="comic-cover"
                [src]="listTopComics[1].coverImage"
                [alt]="listTopComics[1].title"
                onerror="this.src='/option2.png'"
              />
            </a>
            <h3 class="comic-title">{{ listTopComics[1].title }}</h3>
            <p class="comic-author">{{ listTopComics[1].author || 'Đang cập nhật' }}</p>
            <div class="comic-stats">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {{ listTopComics[1].viewCount | numeral }}
            </div>
          </div>
        </div>

        <!-- First Place -->
        <div class="podium-item first-place">
          <div class="podium-card" (click)="showDetails(listTopComics[0])">
            <div class="rank-badge">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
              </svg>
            </div>
            <a [routerLink]="['/truyen-tranh', listTopComics[0].url + '-' + listTopComics[0].id]">
              <img
                loading="lazy"
                class="comic-cover"
                [src]="listTopComics[0].coverImage"
                [alt]="listTopComics[0].title"
                onerror="this.src='/option2.png'"
              />
            </a>
            <h3 class="comic-title">{{ listTopComics[0].title }}</h3>
            <p class="comic-author">{{ listTopComics[0].author || 'Đang cập nhật' }}</p>
            <div class="comic-stats">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {{ listTopComics[0].viewCount | numeral }}
            </div>
          </div>
        </div>

        <!-- Third Place -->
        <div class="podium-item third-place">
          <div class="podium-card" (click)="showDetails(listTopComics[2])">
            <div class="rank-badge">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
              </svg>
            </div>
            <a [routerLink]="['/truyen-tranh', listTopComics[2].url + '-' + listTopComics[2].id]">
              <img
                loading="lazy"
                class="comic-cover"
                [src]="listTopComics[2].coverImage"
                [alt]="listTopComics[2].title"
                onerror="this.src='/option2.png'"
              />
            </a>
            <h3 class="comic-title">{{ listTopComics[2].title }}</h3>
            <p class="comic-author">{{ listTopComics[2].author || 'Đang cập nhật' }}</p>
            <div class="comic-stats">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {{ listTopComics[2].viewCount | numeral }}
            </div>
          </div>
        </div>
    </div>

    <!-- Enhanced Selected Comic Details -->
    <div class="selected-comic-details" *ngIf="selectedComic">
      <div class="text-center mb-6">
        <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {{ selectedComic.title }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400">
          Tác giả: {{ selectedComic.author || 'Đang cập nhật' }}
        </p>
      </div>

      <div class="comic-stats-row">
        <div class="stat-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          <span>{{ selectedComic.viewCount | numeral }} lượt xem</span>
        </div>

        <div class="stat-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
          </svg>
          <span>{{ selectedComic.rating || 0 }} điểm</span>
        </div>

        <div class="stat-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14,2 14,8 20,8"></polyline>
          </svg>
          <span>{{ selectedComic.chapters?.length || 0 }} chapter</span>
        </div>
      </div>

      <div class="genre-tags" *ngIf="selectedComic.genres && selectedComic.genres.length > 0">
        <span
          *ngFor="let genre of selectedComic.genres; let i = index"
          class="genre-tag"
          [class.primary]="i < 2"
          [class.secondary]="i >= 2"
        >
          {{ genre.title }}
        </span>
      </div>

      <p class="comic-description" *ngIf="selectedComic.description">
        {{ selectedComic.description }}
      </p>
    </div>
  </div>

    <!-- Enhanced Ranking List Section -->
    <div class="ranking-list-section">
      <div class="ranking-list-header">
        <svg class="ranking-list-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M2 12C2 7.28595 2 4.92893 3.46447 3.46447C4.92893 2 7.28595 2 12 2C16.714 2 19.0711 2 20.5355 3.46447C22 4.92893 22 7.28595 22 12C22 16.714 22 19.0711 20.5355 20.5355C19.0711 22 16.714 22 12 22C7.28595 22 4.92893 22 3.46447 20.5355C2 19.0711 2 16.714 2 12Z"
            stroke="currentColor"
            stroke-width="1.5"
          ></path>
          <path
            d="M7 18L7 15"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          ></path>
          <path d="M17 18V9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
          <path
            d="M12 18V12"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          ></path>
        </svg>
        <h2 class="ranking-list-title">Bảng Xếp Hạng Chi Tiết</h2>
        <div class="flex-1"></div>
        <span class="text-sm text-gray-500 dark:text-gray-400">
          {{ listComics?.length || 0 }} truyện tranh
        </span>
      </div>

      <!-- Enhanced Grid Layout -->
      <div class="ranking-grid" *ngIf="listComics && listComics.length > 0; else emptyState">
        <div
          *ngFor="let comic of listComics; let i = index"
          class="ranking-comic-card"
          (click)="showDetails(comic)"
        >
          <!-- Rank Badge -->
          <div class="comic-rank-badge">
            {{ i + 4 }}
          </div>

          <!-- Comic Cover Container -->
          <div class="comic-cover-container">
            <a [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]">
              <img
                loading="lazy"
                class="comic-cover-image"
                [src]="comic.coverImage"
                [alt]="comic.title"
                onerror="this.src='/option2.png'"
              />
            </a>

            <!-- Hover Overlay -->
            <div class="comic-info-overlay">
              <h3 class="comic-overlay-title">{{ comic.title }}</h3>
              <div class="comic-overlay-stats">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                <span>{{ comic.viewCount | numeral }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div class="ranking-loading" *ngIf="!listComics">
        <div class="loading-spinner"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-400">Đang tải dữ liệu...</span>
      </div>

      <!-- Empty State -->
      <ng-template #emptyState>
        <div class="ranking-empty">
          <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="empty-title">Không có dữ liệu</h3>
          <p class="empty-description">Hiện tại chưa có truyện tranh nào trong bảng xếp hạng này.</p>
        </div>
      </ng-template>

      <!-- Enhanced Pagination -->
      <div class="ranking-pagination">
        <app-pagination
          (OnChange)="OnChangePage($event)"
          [currentPage]="currentPage"
          [totalpage]="totalpage"
        >
        </app-pagination>
      </div>
    </div>
  </div>
</div>
