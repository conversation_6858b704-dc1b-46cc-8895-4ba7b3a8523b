<div
  *ngIf="isVisible"
  class="fixed inset-0 flex items-center justify-center bg-neutral-800 bg-opacity-50 z-50"
>
  <div
    class="bg-white p-6 rounded-lg shadow-lg relative dark:bg-neutral-800"
  >
    <button
      class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
      (click)="this.setVisible(false)"
    >
      <svg
        class="h-5 w-5 text-gray-400"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <line x1="18" y1="6" x2="6" y2="18" />
        <line x1="6" y1="6" x2="18" y2="18" />
      </svg>
    </button>
    <h3 class="text-lg font-semibold mb-2">Nêu góp ý để team cải thiện nhé!</h3>
    <div>
      <form
        [formGroup]="errorForm"
        (ngSubmit)="sendFeedback()"
        class="space-y-2"
      >
        <div class="flex flex-col">
          <textarea
            id="message"
            formControlName="message"
            type="text"
            maxlength="100"
            class="min-h-40 bg-gray-100 appearance-none border dark:bg-neutral-700 dark:text-white dark:border-neutral-600 dark:focus:border-primary-100 border-gray-200 rounded w-full py-2 px-4 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-primary-100"
          ></textarea>

          <!-- Error message for required field -->
          <div
            *ngIf="
              errorForm.controls['message'].errors?.['required'] &&
              errorForm.controls['message'].touched
            "
            class="text-red-600 text-xs font-light mt-1 flex justify-start"
          >
            Vui lòng nhập thông tin
          </div>
          <div
            *ngIf="
              errorForm.controls['message'].errors?.['maxlength'] &&
              errorForm.controls['message'].touched
            "
            class="text-red-600 text-xs font-light mt-1 flex justify-start"
          >
            Không được vượt quá 1000 ký tự.
          </div>
        </div>

        <div>
          <button
            class="bg-primary-100 hover:bg-primary-200 text-white px-4 py-2 rounded mr-2"
            type="submit"
            [disabled]="!errorForm.valid"
          >
            Gửi
          </button>
          <button
            class="bg-gray-200 text-gray-700 hover:bg-neutral-300 px-4 py-2 rounded"
            (click)="this.setVisible(false)"
          >
            Hủy
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
