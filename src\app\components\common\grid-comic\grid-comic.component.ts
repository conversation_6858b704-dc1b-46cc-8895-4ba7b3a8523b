import { CommonModule, isPlatformServer } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  PLATFORM_ID,
  SimpleChanges,
  ViewEncapsulation
} from '@angular/core';
import { Comic } from '@schema';
import { StorageService } from '@services/storage.service';
import { OptimizedBaseComponent } from '../base/optimized-base.component';
import { ComicCardComponent } from '../comic-card/Ver1/comic-card.component';
import { ComicCardV2Component } from '../comic-card/Ver2/comic-card-v2.component';
import { PopupDetailComicComponent } from '../../lazy/popup-detail-comic/popup-detail-comic.component';

@Component({
  selector: 'app-grid-comic',
  templateUrl: './grid-comic.component.html',
  styleUrl: './grid-comic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, ComicCardComponent, ComicCardV2Component, PopupDetailComicComponent],
})
export class GridComicComponent extends OptimizedBaseComponent implements OnInit, OnChanges, OnDestroy {
  @Input() nPreview = 40;
  @Input() listComics!: Comic[];
  @Input() _class!: string;
  @Input() title!: string;
  @Input() showCheckboxes = false;
  @Output() clickEvent = new EventEmitter<number[]>();
  @Input() eventName!: string;

  hoverComic?: Comic;
  girdType = 0;
  selectedComics = new Set<number>();
  isSelectAll = false;

  // Performance optimizations
  private resizeObserver?: ResizeObserver | null;
  private debouncedResize: Function;

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object,
    private storageService: StorageService,
  ) {
    super(cd, platformId);
    this.listComics = Array(this.nPreview).fill(undefined);
    this.girdType = this.storageService.GetGridType();

    // Debounced resize handler
    this.debouncedResize = this.debounce(() => {
      this.safeMarkForCheck();
    }, 250);
  }

  ngOnInit(): void {
    this.setupResizeObserver();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['listComics']) {
      const currentComics = changes['listComics'].currentValue;
      if (!currentComics || currentComics.length === 0) {
        this.listComics = Array(this.nPreview).fill(undefined);
      }
      this.safeMarkForCheck();
    }
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  private setupResizeObserver(): void {
    this.resizeObserver = this.createResizeObserver(() => {
      this.debouncedResize();
    });
  }

  // ngOnInit(): void {

  // }
  // ngAfterViewInit(): void { }
  // ngOnDestroy(): void { }
  // ngDoCheck(): void {
  //   console.log('ngDoCheck');

  // }

  onHoverComic(comic?: Comic) {
    this.hoverComic = comic;
  }

  @HostListener('window:scroll', ['$event'])
  onScroll() {
    this.hoverComic = undefined;
  }



  ChangeGridType(target: any, type: number) {
    if (this.girdType == type) return;
    this.storageService.SetGridType(type);
    this.girdType = type;
  }

  toggleSelectComic(index: number) {
    if (this.selectedComics.has(index)) {
      this.selectedComics.delete(index);
    } else {
      this.selectedComics.add(index);
    }
  }

  selectAllComics(stage: boolean) {
    this.isSelectAll = stage;
    if (!this.isSelectAll) {
      this.selectedComics.clear();
    } else {
      this.listComics.forEach((_, index) => this.selectedComics.add(index));
    }
  }
  trackByFn(index: number) {
    return index;
  }

  deleteSelectedComics() {
    if (!this.selectedComics.size) {
      return;
    }
    const selectedComicIds = Array.from(this.selectedComics).map(
      (index) => this.listComics[index].id,
    );
    this.clickEvent.emit(selectedComicIds);
  }

}
