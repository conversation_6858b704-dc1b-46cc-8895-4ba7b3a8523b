import {
  isPlatformServer
} from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Comic, ComicList, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
@Component({
  selector: 'app-home',
  templateUrl: './home-page.component.html',
  styleUrl: './home-page.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class HomePageComponent implements OnInit {
  listComics: Comic[] = [];
  totalpage!: number;
  currentPage = 1;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private comicService: ComicService,
    private seoService: SeoService,
    private urlService: UrlService,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Document,
  ) {

    const title = 'MeTruyenMoi - Đọc truyện tranh Manhwa, Manga, Manhua nhanh nhất'
    const description = 'MeTruyenMoi - Đọc truyện tranh online miễn phí, được cập nhật mỗi ngày. Với các thể loại từ hành động, ngôn tình, phiêu lưu, hài hước, lãng mạn và nhiều hơn nữa.'
    this.seoService.setTitle(title);
    this.seoService.addTags([
      { name: 'description', content: description, },
      { name: 'keywords', content: 'truyen tranh, truyện tranh, mê truyện mới, truyen tranh online, đọc truyện tranh, truyện tranh hot, truyện tranh hay, truyện ngôn tình, manga, manhua, manhwa, metruyenmoi, me truyen moi, truyenqq, cmanga, nettruyen' },
      { property: 'og:description', content: description },
      { property: 'og:title', content: title },
      { property: 'og:url', content: `${this.urlService.BASE_URL}` },
      { property: 'og:type', content: 'website' },
      { property: 'og:site_name', content: 'MeTruyenMoi' },
      { itemprop: 'name', content: title },
      { itemprop: 'description', content: description },
    ]);

    this.seoService.updateLink('canonical', `${this.urlService.BASE_URL}`);

  }

  ngOnInit(): void {
    
    this.route.queryParams.subscribe((params) => {
      const page = Number(params['page']) || 1;
      this.currentPage = page;
      this.RefreshPage(page);

    });
  }

  ngAfterViewInit() {
    // this.popupService.createDynamicComponent<AppSettingComponent>(AppSettingComponent).instance.show({group: 1 });
  }

  RefreshPage(page: number, size = 30): void {
    this.listComics = [];
    this.comicService.getComics({
      step: size.toString(),
      genre: '-1',
      page: page.toString(),
      sort: '1',
      status: '-1',
    }).subscribe((res: IServiceResponse<ComicList>) => {
      this.totalpage = res.data!.totalpage;
      this.listComics = res.data!.comics;
      if (this.ssr()) {
        this.listComics = this.listComics.slice(0, 10);
      }
      this.cd.detectChanges();
    });
  }
  OnChangePage(page: number) {
    this.router.navigate([''], {
      queryParams: { page: page },
      fragment: 'comics',
    });
  }
  ssr() {
    return isPlatformServer(this.platformId);
  }
}
