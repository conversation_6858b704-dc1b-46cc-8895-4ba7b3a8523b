.genre-container {
  @apply bg-white dark:bg-neutral-800 dark:text-white border dark:border-gray-950 w-full md:w-[30rem] lg:w-[42rem] xl:w-[44rem] 2xl:w-[50rem] max-h-96 lg:max-h-[32rem] overflow-y-auto py-2 px-4 z-50 shadow-2xl shadow-black/50 space-y-4 top-full;
}

.genre-title {
  @apply text-sm italic font-semibold flex;
}


.genre-label {
  @apply ml-2 text-xs not-italic;
}

.genre-search {
  @apply flex items-center relative mb-4;
}

.genre-search-icon {
  @apply absolute left-2 p-2 rounded-lg;
}

.genre-input {
  @apply bg-gray-100 dark:bg-neutral-700 text-sm w-full h-8 rounded-lg pl-10 pr-4 focus:border-[1px] focus:border-primary-100 dark:focus:bg-neutral-600 outline-none focus:bg-white;

}

.genre-list {
  @apply space-y-1;
}

.genre-list-header {
  @apply flex gap-2 items-center;
}

.genre-list-title {
  @apply capitalize text-lg select-none;
}

.genre-list-divider {
  @apply border-[1px] my-4 flex-grow dark:border-neutral-700;
}

.genre-list-items {
  @apply flex gap-2 flex-wrap;
}

.genre-list-item {
  @apply cursor-pointer rounded-md px-1 flex items-center gap-1;
}

.genre-list-item-normal {
  @apply outline-1 outline-gray-300 bg-gray-100 dark:outline-gray-600 dark:bg-neutral-900 outline-dashed hover:outline-primary-100 hover:text-primary-100;
}

.genre-list-item-active {
  @apply border border-primary-100 bg-primary-50 text-primary-100;
}

.genre-list-item-disabled {
  @apply text-red-500 bg-red-300 border border-dashed border-red-500;
}

.genre-list-item-link {
  @apply px-1 my-auto text-center;
}

.genre-list-item-name {
  @apply my-auto select-none text-xs font-medium relative bottom-[1px];
}

.genre-hover-info {
  @apply space-y-1 transition-all ease-in-out duration-700;
}

.genre-hover-divider {
  @apply border-[1px] my-2 dark:border-neutral-700;
}

.genre-hover-content {
  @apply flex gap-1;
}

.genre-hover-icon {
  @apply capitalize text-lg select-none;
}

.genre-hover-desc {
  @apply text-[0.75rem] font-medium text-gray-700 dark:text-gray-300 text-start;
}