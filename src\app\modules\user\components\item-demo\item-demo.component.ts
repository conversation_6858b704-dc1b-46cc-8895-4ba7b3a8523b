import { 
  Component, 
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ItemService } from '../../services/item.service';
import { UserInventoryComponent } from '../user-inventory/user-inventory.component';
import { EquippedItemsComponent } from '../equipped-items/equipped-items.component';
import { 
  ItemCategory,
  ItemRarity,
  ITEM_CATEGORY_INFO,
  ITEM_RARITY_INFO
} from '../../interfaces/item.interface';

@Component({
  selector: 'app-item-demo',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule, 
    UserInventoryComponent, 
    EquippedItemsComponent
  ],
  templateUrl: './item-demo.component.html',
  styleUrl: './item-demo.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ItemDemoComponent extends OptimizedBaseComponent implements OnInit {
  // Demo controls
  selectedItemId = 1;
  selectedQuantity = 1;
  
  // Constants for template
  readonly ItemCategory = ItemCategory;
  readonly ItemRarity = ItemRarity;
  readonly ITEM_CATEGORY_INFO = ITEM_CATEGORY_INFO;
  readonly ITEM_RARITY_INFO = ITEM_RARITY_INFO;

  // Available items for demo
  availableItems = [
    { id: 1, name: 'Xu', category: 'Currency' },
    { id: 2, name: 'Kim cương', category: 'Currency' },
    { id: 3, name: 'Điểm kinh nghiệm', category: 'Experience' },
    { id: 4, name: 'Huy hiệu Độc giả', category: 'Badge' },
    { id: 5, name: 'Huy hiệu Bình luận viên', category: 'Badge' },
    { id: 6, name: 'Huy hiệu Khám phá', category: 'Badge' },
    { id: 7, name: 'Khung Avatar Đồng', category: 'Avatar Frame' },
    { id: 8, name: 'Khung Avatar Bạc', category: 'Avatar Frame' },
    { id: 9, name: 'Khung Avatar Vàng', category: 'Avatar Frame' },
    { id: 10, name: 'Khung Avatar Huyền thoại', category: 'Avatar Frame' },
    { id: 11, name: 'Độc giả mới', category: 'Title' },
    { id: 12, name: 'Độc giả chăm chỉ', category: 'Title' },
    { id: 13, name: 'Bậc thầy bình luận', category: 'Title' },
    { id: 14, name: 'Nhà khám phá', category: 'Title' },
    { id: 15, name: 'Huyền thoại', category: 'Title' },
    { id: 16, name: 'Thuốc tăng EXP', category: 'Consumable' },
    { id: 17, name: 'Thuốc tăng EXP cấp cao', category: 'Consumable' },
    { id: 18, name: 'Premium 1 ngày', category: 'Premium' },
    { id: 19, name: 'Premium 7 ngày', category: 'Premium' },
    { id: 20, name: 'Premium 30 ngày', category: 'Premium' }
  ];

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private itemService: ItemService
  ) {
    super(cdr, platformId);
  }

  ngOnInit(): void {
    // Initialize item data
    this.itemService.initializeItemData();
  }

  // Demo actions
  addItemToInventory(): void {
    this.itemService.addMockItemToInventory(this.selectedItemId, this.selectedQuantity);
    this.showMessage(`Added ${this.selectedQuantity} items to inventory!`);
  }

  clearEquippedItems(): void {
    this.itemService.clearAllEquippedItems();
    this.showMessage('Cleared all equipped items!');
  }

  giveRandomItems(): void {
    // Give some random items
    const randomItems = [
      { id: 1, quantity: Math.floor(Math.random() * 500) + 100 }, // Xu
      { id: 2, quantity: Math.floor(Math.random() * 50) + 10 },   // Kim cương
      { id: 3, quantity: Math.floor(Math.random() * 1000) + 500 }, // EXP
      { id: 16, quantity: Math.floor(Math.random() * 5) + 1 },    // Thuốc EXP
      { id: 18, quantity: Math.floor(Math.random() * 3) + 1 }     // Premium
    ];

    randomItems.forEach(item => {
      this.itemService.addMockItemToInventory(item.id, item.quantity);
    });

    this.showMessage('Added random items to inventory!');
  }

  resetToDefault(): void {
    // This would reset to the default mock data
    window.location.reload();
  }

  private showMessage(message: string): void {
    // Simple alert for demo - in real app would use toast/snackbar
    alert(message);
  }

  // TrackBy functions
  trackByItemId = (index: number, item: any): number => {
    return item.id;
  };
}
