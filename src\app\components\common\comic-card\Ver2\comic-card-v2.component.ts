import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, ChangeDetectorRef, Inject, PLATFORM_ID } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { ComicDescriptionlPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic, ComicStatus } from '@schema';
import { OptimizedBaseComponent } from '../../base/optimized-base.component';

@Component({
    selector: 'app-comic-card-v2',
    templateUrl: './comic-card-v2.component.html',
    styleUrl: './comic-card-v2.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule, RouterLink, NumeralPipe, DateAgoPipe, ComicDescriptionlPipe],
})
export class ComicCardV2Component extends OptimizedBaseComponent {
  @Input() comic?: Comic;

  // Computed properties for better performance
  get comicRouterLink(): string[] {
    return this.comic ? ['/truyen-tranh', `${this.comic.url}-${this.comic.id}`] : [];
  }

  get displayGenres(): any[] {
    return this.comic?.genres?.slice(0, 3) || [];
  }

  get isOngoing(): boolean {
    return this.comic?.status === ComicStatus.ONGOING;
  }

  get isCompleted(): boolean {
    return this.comic?.status === ComicStatus.COMPLETED;
  }

  get statusText(): string {
    return this.isOngoing ? 'Đang tiến hành' : 'Đã hoàn thành';
  }

  get filledDescription(): string {
    if (!this.comic) return '';
    return this.comic.description || '';
  }

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
  }

  trackByGenreId = (index: number, genre: any): number => {
    return genre?.id ?? index;
  };
}
