import { isPlatformServer } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, OnChanges, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Comic } from '@schema';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
    selector: 'app-history-tag',
    templateUrl: './history-page.component.html',
    styleUrl: './history-page.component.scss',
    standalone: false
})
export class HistoryPageComponent implements OnInit, OnChanges {
  comics: Comic[] = [];
  page = 1;
  totalpage = 1;
  comicPerPage = 14;
  isLoading = false;
  selectedComics: number[] = [];

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private hisService: HistoryService,
    private toastService: ToastService,
    private popupService: PopupService,
    private seoService: SeoService,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: object,
  ) {
    this.seoService.setTitle('Lịch sử đã xem');
    if (this.ssr()) return;
    this.route.queryParams.subscribe((params) => {
      const page = Number(params['page']) || 1;
      this.page = page;

      this.refreshPage();
    });
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.updatePage();
  }
  ssr() {
    return isPlatformServer(this.platformId);
  }
  ngOnChanges() {
    this.updatePage();
  }

  refreshPage() {
    let his = this.hisService.GetHistorys();
    if (his.length === 0) {
      this.comics = [];
      this.isLoading = false;
      return;
    }
    this.totalpage = Math.floor((his.length - 1) / this.comicPerPage) + 1;
    his = his.slice(
      (this.page - 1) * this.comicPerPage,
      this.page * this.comicPerPage,
    );
    const ids = his.map((element) => element.id);
    this.comicService.getComicsByIds(ids).subscribe((res: any) => {
      this.comics = res.data;
      this.isLoading = false;
      this.cd.detectChanges();

    });
  }

  onChangePage(page: number) {
    // this.comics = [];
    if (page === this.page) {
      this.refreshPage();
      return;
    }

    this.router.navigate([], {
      queryParams: { page: page },
      queryParamsHandling: 'merge',
    });
  }

  onRemoveSelectedComics(ids: number[]) {
    if (!ids.length) {
      return;
    }
    this.selectedComics = ids;
    this.popupService
      .showConfirmPopup({
        title: 'Xóa lịch sử',
        message: this.message,
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
      })
      .then((result: any) => {
        const { isconfirm, isCancel } = result;
        if (isconfirm) {
          this.confirmDelete();
        }
        if (isCancel) {
          this.cancelDelete();
        }
      });
  }

  confirmDelete() {
    this.selectedComics.forEach((id) => this.hisService.RemoveHistory(id));
    this.toastService.show(ToastType.Success, 'Xóa truyện đã xem thành công');
    this.updatePage();
  }

  cancelDelete() {

  }

  get message(): string {
    if (this.selectedComics.length > 1) {
      return `Bạn có chắc chắn muốn xóa <b>${this.selectedComics.length}</b> truyện khỏi danh sách truyện đã xem?`;
    }

    const [comicId] = this.selectedComics;
    const comic = this.comics.find((comic) => comic.id === comicId);
    return comic
      ? `Bạn có chắc chắn muốn xóa <b>${comic?.title}</b> khỏi danh sách truyện đã xem?`
      : '';
  }

  updatePage() {
    const totalpage = Math.ceil(
      this.hisService.GetHistorySize() / this.comicPerPage,
    );

    if (this.totalpage !== totalpage && this.page === this.totalpage) {
      this.totalpage = totalpage;
      this.onChangePage(this.page - 1);
    } else {
      this.onChangePage(this.page);
    }
  }
}
