import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { Comic } from '@schema';
// import { OverlayContainer } from '@angular/cdk/overlay';

import { HistoryService } from '@services/history.service';

@Component({
  selector: 'app-recent-read',
  templateUrl: './recent-read.component.html',
  styleUrl: './recent-read.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [RouterLink, CommonModule, DateAgoPipe],
})
export class RecentReadComponent implements OnInit {
  listComics: Comic[] = [];
  constructor(

    private hisService: HistoryService,
  ) { }
  ngOnInit(): void {
    this.listComics = this.hisService.GetHistorys().slice(0, 3) ?? [];
  }
  // ngOnChanges() { }
}
