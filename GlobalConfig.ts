class GlobalConfig {
  public USE_API: boolean;
  public EnableCache: boolean;
  public BASE_API_URL: string;
  public BASE_CHAT_URL: string;
  public EnableAds: boolean;
  public APP_NAME1: string;
  public APP_NAME2: string;
  public BASE_URL: string;
  public CHATBOT_HOST: string;
  public HOST: string;
  public IS_PRODUCTION: boolean;
  constructor() {
    this.USE_API = true;
    // this.API_HOST = 'http://localhost:5080/api';
    this.EnableCache = true;
    this.EnableAds = false;
    this.BASE_CHAT_URL = 'metruyenmoi.com';
    this.APP_NAME1 = 'metruyenmoi';
    this.APP_NAME2 = 'metruyenmoi.com';
    this.HOST = 'metruyenmoi.com';
    this.BASE_URL = `https://${this.HOST}`;
    // this.BASE_API_URL = 'http://localhost:5080';
    this.BASE_API_URL = 'http://**************:5080';

    this.CHATBOT_HOST = `https://${this.HOST}/v1/chatbot`;

    this.IS_PRODUCTION = false;
  }
  public GetDataHost() {

  }
  public screens: any = {
    xs: '480px',
    sm: '640px',
    md: '768px',
    lg: '940px',
    xl: '1100px',
    '2xl': '1200px',
  };
  public GetScreenSize(type: string) {
    return Number(this.screens[type].replace('px', ''));
  }
}
let globalConfig = new GlobalConfig();
export default globalConfig;
