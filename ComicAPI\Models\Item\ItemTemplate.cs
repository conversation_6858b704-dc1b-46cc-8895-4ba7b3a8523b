using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Item
{
    public class ItemTemplate
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(255), <PERSON>umn("name")]
        public string Name { get; set; } = string.Empty;

        [Column("description")]
        public string? Description { get; set; }

        [Max<PERSON>ength(100), Column("icon")]
        public string? Icon { get; set; }

        [Required, <PERSON><PERSON>ength(50), Column("category")]
        public string Category { get; set; } = string.Empty;

        [Required, <PERSON><PERSON><PERSON><PERSON>(20), Column("rarity")]
        public string Rarity { get; set; } = "common";

        [Column("isactive")]
        public bool IsActive { get; set; } = true;

        [Column("createat")]
        public DateTime CreateAt { get; set; } = DateTime.UtcNow;

        [Column("updateat")]
        public DateTime UpdateAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<UserInventory> UserInventories { get; set; } = new List<UserInventory>();
        public virtual ICollection<UserEquippedItem> UserEquippedItems { get; set; } = new List<UserEquippedItem>();
    }

    public enum ItemRarity
    {
        common,
        uncommon,
        rare,
        epic,
        legendary
    }
}
