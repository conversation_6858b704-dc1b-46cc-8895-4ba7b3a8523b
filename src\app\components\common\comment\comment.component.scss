@keyframes animationHiddenFrame {
  0% {
    height: auto;
  }

  ;

  100% {
    height: 0%;
  }

  ;
}

.HiddenAnimation {

  animation: animationHiddenFrame 2s linear 2s infinite alternate;
}


.comment-block {
  @apply flex w-full;
}

.comment-avatar {
  @apply px-1 md:px-3 pt-1;
}

.comment-avatar-icon {
  @apply flex items-center w-12 md:w-16 cursor-pointer;
}

.comment-avatar-img {
  @apply object-cover p-0.5 w-12 h-12 md:w-16 md:h-16 rounded-full;
}

.comment-content {
  @apply text-sm lg:text-base w-[calc(100%-56px)] md:w-[calc(100%-72px)] flex flex-col bg-white dark:bg-dark-bg space-y-1 rounded leading-normal resize-none py-2 px-3 border border-gray-200 dark:border-gray-600;
}

.comment-header {
  @apply flex justify-between items-center;
}

.comment-username {
  @apply font-semibold text-center text-sm;
}

.comment-date {
  @apply ml-2 text-gray-600 dark:text-gray-400 text-[0.75rem] font-light;
}

.comment-chapter {
  @apply hover:underline hover:text-gray-500 dark:text-gray-400 text-[0.75rem] font-semibold text-gray-400;
}

.comment-text {
  @apply items-center text-gray-600 dark:text-gray-100 text-sm break-words;
}

.comment-actions {
  @apply flex flex-row flex-nowrap items-center;
}

.comment-actions-left {
  @apply flex flex-row flex-nowrap justify-start items-center pr-2;
}

.comment-action-icon {
  @apply h-4 w-4;
}

.comment-action-text {
  @apply mx-1;
}

.comment-reply {
  @apply text-center ml-2 font-semibold text-[0.75rem] text-gray-500 hover:text-gray-800 dark:text-gray-100 dark:hover:text-gray-200;
}

.comment-view-reply {
  @apply text-right font-light hover:underline text-sm text-primary-100;
}

// Performance Optimized Styles
.comment-item {
  @apply flex flex-col gap-1 transition-all;
}

.comment-form {
  @apply mb-5;
}

.comment-form-wrapper {
  @apply w-full mb-2 mt-6 flex;
}

.comment-textarea {
  @apply text-sm dark:bg-dark-bg rounded leading-normal resize-none w-full h-16 py-2 px-3 font-medium placeholder-gray-400 focus:outline-none focus:border focus:border-gray-300 dark:focus:bg-neutral-700;
}

.comment-form-actions {
  @apply w-full flex justify-between;
}

.emoji-picker-container {
  @apply relative;
}

.emoji-button {
  @apply h-8 w-8 text-gray-400 ml-24 hover:bg-gray-200 rounded-full p-1 cursor-pointer;
}

.submit-button {
  @apply w-24 h-8 text-white bg-primary-100 hover:bg-primary-200 rounded inline-flex items-center justify-center gap-1;
}

.submit-icon {
  @apply h-4 w-4 text-gray-100 rotate-45;
}

.reply-form-wrapper {
  @apply w-full flex items-center;
}

.reply-textarea {
  @apply dark:bg-dark-bg dark:focus:bg-neutral-700 text-sm rounded leading-normal resize-none w-full h-12 py-2 px-3 font-medium placeholder-gray-400 focus:outline-none focus:bg-white;
}

.reply-submit-button {
  @apply w-16 h-6 text-white bg-primary-100 hover:bg-primary-200 rounded;
}

.login-prompt {
  @apply m-3 py-2 px-4 mx-auto rounded bg-primary-100 text-white font-semibold hover:bg-primary-200;
}

.comments-count-section {
  @apply mb-4 w-full flex flex-row flex-wrap justify-between items-end border-b border-neutral-300;
}

.comments-count-container {
  @apply border-b-2 -mb-[1.2px] h-full border-primary-100 text-xl font-medium;
}

.comments-icon {
  @apply size-5 inline-block mr-2;
}

.reply-container {
  @apply pl-[52px] md:pl-[72px] gap-1 flex flex-col overflow-hidden;

}