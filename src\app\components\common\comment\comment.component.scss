// Comic-themed animations
@keyframes speechBubbleAppear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02) translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes avatarGlow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(232, 58, 58, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(232, 58, 58, 0.1);
  }
}

@keyframes replySlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
    max-height: 0;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
    max-height: 500px;
  }
}

.HiddenAnimation {
  animation: animationHiddenFrame 2s linear 2s infinite alternate;
}

// Main comment block with speech bubble design
.comment-block {
  @apply flex w-full mb-4 relative;
  animation: speechBubbleAppear 0.4s ease-out;
}

// Enhanced avatar section
.comment-avatar {
  @apply px-2 md:px-4 pt-2 relative;
}

.comment-avatar-icon {
  @apply flex items-center w-14 md:w-18 cursor-pointer relative;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);

    .comment-avatar-img {
      animation: avatarGlow 1.5s ease-in-out;
    }
  }
}

.comment-avatar-img {
  @apply object-cover w-14 h-14 md:w-18 md:h-18 rounded-full border-3 border-white dark:border-gray-700 shadow-lg;
  background: linear-gradient(135deg, #E83A3A 0%, #D1202A 100%);
  padding: 2px;
  transition: all 0.3s ease;
}

// Speech bubble styled content
.comment-content {
  @apply text-sm lg:text-base w-[calc(100%-72px)] md:w-[calc(100%-88px)] flex flex-col space-y-2 relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  @apply dark:bg-gradient-to-br dark:from-neutral-800 dark:to-neutral-900;
  @apply rounded-2xl rounded-tl-sm shadow-lg border border-gray-200 dark:border-gray-600;
  padding: 16px 20px;
  position: relative;

  // Speech bubble tail
  &::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 20px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 8px 8px 0;
    border-color: transparent #ffffff transparent transparent;
    @apply dark:border-r-neutral-800;
  }

  // Subtle inner glow
  &::after {
    content: '';
    position: absolute;
    inset: 1px;
    background: linear-gradient(135deg, rgba(232, 58, 58, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
    @apply dark:bg-gradient-to-br dark:from-neutral-700/20 dark:to-transparent;
    border-radius: inherit;
    pointer-events: none;
  }
}

// Enhanced header with better hierarchy
.comment-header {
  @apply flex flex-wrap items-center gap-2 mb-2;
}

.comment-username {
  @apply font-bold text-base text-gray-800 dark:text-white flex items-center gap-2;

  // User level badge
  &::after {
    content: 'Lv.5';
    @apply bg-gradient-to-r from-primary-100 to-primary-200 text-white text-xs px-2 py-1 rounded-full font-semibold shadow-sm;
  }
}

.comment-date {
  @apply text-gray-500 dark:text-gray-400 text-xs font-medium bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full;
}

.comment-chapter {
  @apply hover:underline hover:text-primary-100 text-xs font-bold text-primary-200 bg-primary-50 dark:bg-primary-900/20 px-2 py-1 rounded-full transition-colors;
  border: 1px solid rgba(232, 58, 58, 0.2);
}

// Enhanced comment text
.comment-text {
  @apply text-gray-700 dark:text-gray-200 text-sm leading-relaxed break-words;
  line-height: 1.6;
}

// Improved action buttons
.comment-actions {
  @apply flex flex-row flex-wrap items-center gap-3 mt-3 pt-2 border-t border-gray-100 dark:border-gray-700;
}

.comment-actions-left {
  @apply flex flex-row items-center gap-4;
}

.comment-action-icon {
  @apply h-5 w-5 transition-colors cursor-pointer;

  &:hover {
    @apply text-primary-100;
  }
}

.comment-action-text {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.comment-reply {
  @apply font-semibold text-sm text-primary-100 hover:text-primary-200 transition-colors cursor-pointer;

  &:hover {
    text-decoration: underline;
  }
}

.comment-view-reply {
  @apply font-medium hover:underline text-sm text-primary-100 hover:text-primary-200 transition-colors bg-primary-50 dark:bg-primary-900/20 px-3 py-1 rounded-full;
}

// Performance Optimized Styles
.comment-item {
  @apply flex flex-col gap-1 transition-all;
}

.comment-form {
  @apply mb-5;
}

.comment-form-wrapper {
  @apply w-full mb-2 mt-6 flex;
}

.comment-textarea {
  @apply text-sm dark:bg-dark-bg rounded leading-normal resize-none w-full h-16 py-2 px-3 font-medium placeholder-gray-400 focus:outline-none focus:border focus:border-gray-300 dark:focus:bg-neutral-700;
}

.comment-form-actions {
  @apply w-full flex justify-between;
}

.emoji-picker-container {
  @apply relative;
}

.emoji-button {
  @apply h-8 w-8 text-gray-400 ml-24 hover:bg-gray-200 rounded-full p-1 cursor-pointer;
}

.submit-button {
  @apply w-24 h-8 text-white bg-primary-100 hover:bg-primary-200 rounded inline-flex items-center justify-center gap-1;
}

.submit-icon {
  @apply h-4 w-4 text-gray-100 rotate-45;
}

.reply-form-wrapper {
  @apply w-full flex items-center;
}

.reply-textarea {
  @apply dark:bg-dark-bg dark:focus:bg-neutral-700 text-sm rounded leading-normal resize-none w-full h-12 py-2 px-3 font-medium placeholder-gray-400 focus:outline-none focus:bg-white;
}

.reply-submit-button {
  @apply w-16 h-6 text-white bg-primary-100 hover:bg-primary-200 rounded;
}

.login-prompt {
  @apply m-3 py-2 px-4 mx-auto rounded bg-primary-100 text-white font-semibold hover:bg-primary-200;
}

.comments-count-section {
  @apply mb-4 w-full flex flex-row flex-wrap justify-between items-end border-b border-neutral-300;
}

.comments-count-container {
  @apply border-b-2 -mb-[1.2px] h-full border-primary-100 text-xl font-medium;
}

.comments-icon {
  @apply size-5 inline-block mr-2;
}

.reply-container {
  @apply pl-[52px] md:pl-[72px] gap-1 flex flex-col overflow-hidden;

}