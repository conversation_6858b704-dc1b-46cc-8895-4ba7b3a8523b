// Chapter Selector Container
.chapter-selector {
  @apply relative inline-block w-auto lg:w-[245px] text-sm font-normal dark:text-white;
}

// Chapter Selector Trigger Button
.chapter-selector-trigger {
  @apply border border-solid border-[#ccc] rounded px-3 cursor-pointer flex justify-between items-center whitespace-nowrap min-w-40;

  &:hover {
    @apply border-gray-400 dark:border-gray-500;
  }

  &:focus {
    @apply outline-none ring-2 ring-primary-100 ring-opacity-50;
  }
}

// Chapter Selector Icon
.chapter-selector-icon {
  @apply h-8 w-8 transition-transform duration-200 text-gray-500;

  &.rotated {
    @apply rotate-180;
  }
}

// Chapter Dropdown Container
.chapter-dropdown {
  @apply absolute w-full bg-white dark:bg-dark-bg border border-solid border-[#ccc] dark:border-gray-600 rounded-lg shadow-lg z-[100] overflow-hidden;
}

// Chapter Search Input
.chapter-search-input {
  @apply w-full px-3 py-2 outline-none border-b dark:border-gray-600 bg-white dark:bg-dark-bg dark:text-white;

  &::placeholder {
    @apply text-gray-400 dark:text-gray-500;
  }
}

// Chapter List Container
.chapter-list-container {
  @apply max-h-64 w-full flex;
}

// Chapter Scroll Component
.chapter-scroll {
  @apply flex w-full;
}

// Chapter Item
.chapter-item {
  @apply mb-1 mx-1 rounded-sm line-clamp-1 px-4 py-1 cursor-pointer dark:text-white transition-colors duration-150;

  &.chapter-item-selected {
    @apply bg-primary-100 text-white;
  }

  &.chapter-item-default {
    @apply text-secondary-100 hover:bg-gray-200 dark:hover:bg-neutral-700;
  }


}

// Loading State
.chapter-loading {
  @apply flex items-center justify-center p-4 text-gray-500 dark:text-gray-400;
}

// Empty State
.chapter-empty {
  @apply flex items-center justify-center p-4 text-gray-500 dark:text-gray-400;
}

// Error State
.chapter-error {
  @apply flex items-center justify-center p-4 text-red-500 dark:text-red-400;
}

