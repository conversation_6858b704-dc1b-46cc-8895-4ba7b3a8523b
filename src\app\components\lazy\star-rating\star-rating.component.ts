import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { AccountService } from '@services/account.service';
import { ToastService, ToastType } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
  templateUrl: './star-rating.component.html',
  styleUrl: './star-rating.component.scss',
  standalone: true,
  imports: [CommonModule],

})
export class StarRatingComponent implements OnChanges, IPopupComponent {
  initialRating = 0;
  comicid = -1;
  selectedStars = 0;
  stars: number[] = [1, 2, 3, 4, 5];
  isVisible = false;
  tempRating = 5;

  constructor(
    private accountService: AccountService,
    private toastService: ToastService,
    private cd: ChangeDetectorRef

  ) { }
  setVisible(isVisible: boolean): void {
    this.isVisible = isVisible;
    this.cd.detectChanges();
  }
  show(object: any): Promise<any> {
    const { comicid, initialRating } = object;
    this.comicid = comicid;
    this.initialRating = initialRating;
    this.tempRating = initialRating;
    this.setVisible(true);
    this.updateRating(initialRating);
    return new Promise((resolve, reject) => { resolve({}) });
  }

  // ngOnInit(): void {

  // }

  ngOnChanges(changes: SimpleChanges) {

  }

  updateRating(newRating: number) {
    this.selectedStars = newRating;
    this.cd.detectChanges();

  }

  rateStar(starIndex: number) {
    if (this.accountService.GetUser() === null) {
      this.toastService.show(ToastType.Error, 'Vui lòng đăng nhập đánh giá truyện');
      return;
    }
    this.tempRating = starIndex;
    this.cd.detectChanges();

  }

  confirmVote() {

    this.accountService
    .voteComic(this.comicid, this.tempRating)
    .subscribe((res: any) => {
      // if (res.data === 1) {
        this.updateRating(this.tempRating);
        this.toastService.show(ToastType.Success, 'Gửi đánh giá thành công');
        // }
      });
    this.setVisible(false);
  }

  cancelVote() {
    this.setVisible(false);
  }

  resetStars() {
    this.selectedStars = this.initialRating;
    this.cd.detectChanges();
  }

  getRatingLabel(rating: number): string {
    const labels = [
      '',
      'Rất tệ',
      'Tệ',
      'Bình thường',
      'Tốt',
      'Xuất sắc'
    ];
    return labels[rating] || '';
  }

  getRatingDescription(rating: number): string {
    const descriptions = [
      '',
      'Truyện này thực sự không phù hợp với bạn',
      'Truyện có nhiều điểm cần cải thiện',
      'Truyện ở mức độ chấp nhận được',
      'Truyện hay và đáng đọc',
      'Truyện tuyệt vời, rất đáng để theo dõi!'
    ];
    return descriptions[rating] || '';
  }
}
