<div id="toast-container" class="toast-container">
  <div
    *ngFor="let toast of toastService.toasts; trackBy: trackByToastId"
    class="toast"
    [class]="'toast--' + toast.type"
    role="alert"
    aria-live="assertive"
    aria-atomic="true"
    [@toastAnimation]="toast.state"
  >
    <!-- Progress Bar -->
    <div class="toast-progress" *ngIf="toast.duration && toast.duration > 0">
      <div
        class="toast-progress-bar"
        [class]="'toast-progress-bar--' + toast.type"
        [style.--duration]="toast.duration + 'ms'"
      ></div>
    </div>

    <!-- Main Content -->
    <div class="toast-main">
      <!-- Icon -->
      <div class="toast-icon" [class]="'toast-icon--' + toast.type">
        <svg
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path [attr.d]="getToastIcon(toast.type)"/>
        </svg>
      </div>

      <!-- Content -->
      <div class="toast-content">
        <div class="toast-title" *ngIf="toast.title">{{ toast.title }}</div>
        <div class="toast-message">{{ toast.message }}</div>

        <!-- Action Button -->
        <button
          *ngIf="toast.action"
          class="toast-action"
          (click)="onAction(toast.action)"
        >
          {{ toast.action.label }}
        </button>
      </div>

      <!-- Dismiss Button -->
      <button
        *ngIf="toast.dismissible"
        class="toast-dismiss"
        (click)="onDismiss(toast.id)"
        aria-label="Đóng thông báo"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>
  </div>
</div>
