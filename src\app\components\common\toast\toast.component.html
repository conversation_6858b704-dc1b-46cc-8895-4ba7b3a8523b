<div id="toast-container" class="toast-container">
  <div
    *ngFor="let toast of toastService.toasts"
    class="toast"
    role="alert"
    aria-live="assertive"
    aria-atomic="true"
    [@toastAnimation]="toast.state"
  >
    <div
      class="toast-background"
      [class]="{
        'toast-background-success': toast.type === 'success',
        'toast-background-error': toast.type === 'error',
        'toast-background-warning': toast.type === 'warning',
        'toast-background-info': toast.type === 'info'
      }"
    ></div>
    <div class="toast-content">
      <div
        class="toast-icon"
        [class]="{
          'toast-icon-success': toast.type === 'success',
          'toast-icon-error': toast.type === 'error',
          'toast-icon-warning': toast.type === 'warning',
          'toast-icon-info': toast.type === 'info'
        }"
      >
        <svg
          width="1.8em"
          height="1.8em"
          viewBox="0 0 16 16"
          class="bi bi-check"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            *ngIf="toast.type === 'success'"
            fill-rule="evenodd"
            d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.236.236 0 0 1 .02-.022z"
          />
          <path
            *ngIf="toast.type === 'info'"
            d="M8.93 6.588l-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588z"
          />
          <circle *ngIf="toast.type === 'info'" cx="8" cy="4.5" r="1" />
          <path
            *ngIf="toast.type === 'warning'"
            d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"
          />

          <path
            *ngIf="toast.type === 'error'"
            fill-rule="evenodd"
            d="M11.854 4.146a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708-.708l7-7a.5.5 0 0 1 .708 0z"
          />
          <path
            *ngIf="toast.type === 'error'"
            fill-rule="evenodd"
            d="M4.146 4.146a.5.5 0 0 0 0 .708l7 7a.5.5 0 0 0 .708-.708l-7-7a.5.5 0 0 0-.708 0z"
          />
        </svg>
      </div>
      <div
        class="toast-message"
        [class]="{
          'toast-message-success': toast.type === 'success',
          'toast-message-error': toast.type === 'error',
          'toast-message-warning': toast.type === 'warning',
          'toast-message-info': toast.type === 'info'
        }"
      >
        {{ toast.message }}
      </div>
    </div>
  </div>
</div>
