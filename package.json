{"name": "comic-client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "ng serve --configuration development", "start:prod": "ng serve --configuration production", "publish": "ng serve --host 0.0.0.0", "build": "ng build --configuration production", "build:analyze": "ng build --configuration production --stats-json && npx webpack-bundle-analyzer dist/comic-client/stats.json", "build:dev": "ng build --configuration development", "build:prod": "ng build --configuration production --verbose", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --watch=false --browsers=ChromeHeadless", "e2e": "ng e2e", "ssr": "node dist/comic-client/server/server.mjs", "ssr:dev": "ng build && ng run comic-client:serve-ssr", "watch:ssr": "nodemon dist/comic-client/server/server.mjs", "buildrun": "ng build --configuration production && node dist/comic-client/server/server.mjs", "lint": "ng lint", "lint:fix": "ng lint --fix", "format": "prettier --write \"src/**/*.{ts,html,scss}\"", "format:check": "prettier --check \"src/**/*.{ts,html,scss}\"", "precommit": "npm run lint && npm run format:check", "clean": "rimraf dist node_modules/.cache", "clean:install": "npm run clean && npm install", "performance": "ng build --configuration production && npx lighthouse http://localhost:4200 --output html --output-path ./lighthouse-report.html"}, "private": true, "dependencies": {"@abacritt/angularx-social-login": "^2.2.0", "@angular/animations": "^19.2.2", "@angular/common": "^19.2.2", "@angular/compiler": "^19.2.2", "@angular/core": "^19.2.2", "@angular/forms": "^19.2.2", "@angular/platform-browser": "^19.2.2", "@angular/platform-browser-dynamic": "^19.2.2", "@angular/platform-server": "^19.2.2", "@angular/router": "^19.2.2", "@angular/ssr": "^19.2.3", "@popperjs/core": "^2.11.8", "@types/event-source-polyfill": "^1.0.5", "event-source-polyfill": "^1.0.31", "express": "^4.21.1", "express-rate-limit": "^7.5.0", "ngx-cookie-service-ssr": "^19.1.2", "rxjs": "~7.8.1", "tslib": "^2.8.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.3", "@angular/cli": "^19.2.3", "@angular/compiler-cli": "^19.2.2", "@angular/localize": "^19.2.2", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/jasmine": "~5.1.4", "@types/node": "^22.7.6", "angular-eslint": "19.4.0", "autoprefixer": "^10.4.20", "eslint": "^9.26.0", "eslint-plugin-unused-imports": "^4.1.4", "jasmine-core": "~5.4.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "~5.5.4", "typescript-eslint": "8.32.0"}}