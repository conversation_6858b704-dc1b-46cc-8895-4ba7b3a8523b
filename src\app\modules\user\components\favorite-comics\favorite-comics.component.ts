import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-favorite-comics',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="favorite-comics-container">
      <div class="page-header">
        <h2 class="page-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
          </svg>
          T<PERSON>y<PERSON>n y<PERSON>u thích
        </h2>
        <p class="page-description"><PERSON><PERSON> sách các truy<PERSON>n bạn đ<PERSON> lưu v<PERSON><PERSON> mụ<PERSON> yê<PERSON> thích</p>
      </div>

      <div class="content-card">
        <div class="empty-state">
          <div class="empty-illustration">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </svg>
          </div>
          <h3 class="empty-title">Chưa có truyện yêu thích</h3>
          <p class="empty-description">
            Bạn chưa lưu truyện nào vào danh sách yêu thích. 
            Hãy khám phá và lưu những truyện bạn thích nhé!
          </p>
          <button class="explore-btn">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            Khám phá truyện
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .favorite-comics-container {
      @apply space-y-6;
    }

    .page-header {
      @apply space-y-2;
    }

    .page-title {
      @apply flex items-center gap-3 text-2xl font-bold text-gray-900 dark:text-white;
    }

    .title-icon {
      @apply w-8 h-8 text-red-500;
      fill: none;
      stroke: currentColor;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .page-description {
      @apply text-gray-600 dark:text-gray-400;
    }

    .content-card {
      @apply bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm p-12;
      backdrop-filter: blur(20px);
      background: rgba(255, 255, 255, 0.95);
      
      .dark & {
        background: rgba(31, 41, 55, 0.95);
      }
    }

    .empty-state {
      @apply text-center space-y-6;
    }

    .empty-illustration {
      @apply flex justify-center;
    }

    .empty-icon {
      @apply w-24 h-24 text-gray-300 dark:text-gray-600;
      fill: none;
      stroke: currentColor;
      stroke-width: 1;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .empty-title {
      @apply text-xl font-semibold text-gray-900 dark:text-white;
    }

    .empty-description {
      @apply text-gray-600 dark:text-gray-400 max-w-md mx-auto leading-relaxed;
    }

    .explore-btn {
      @apply flex items-center gap-2 mx-auto px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;
      
      &:focus {
        @apply outline-none ring-2 ring-blue-500/50;
      }
    }

    .btn-icon {
      @apply w-5 h-5;
      fill: none;
      stroke: currentColor;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FavoriteComicsComponent {
}
