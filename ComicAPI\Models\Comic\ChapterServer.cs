using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ComicApp.Data;

namespace ComicApp.Models
{

    public class ChapterServer
    {
        [Key]
        [Column("id")]
        public int ID { get; set; }

        [ForeignKey("Chapter")]
        [Column("chapter_id")]
        public int ChapterId { get; set; }


        [MaxLength(100)]
        [Column("server_name")]
        public string? ServerName { get; set; }

        [MaxLength(255)]
        [Column("host")]
        public string? Host { get; set; }

        [MaxLength(50)]
        [Column("referer")]
        public string? Referer { get; set; }

        [Column("images")]
        public string[]? Images { get; set; }

        [Column("code")]
        public string? Code { get; set; }

        [Column("status")]
        public int Status { get; set; } = 1;

        [Column("is_default")]
        public int IsDefault { get; set; } = 0;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // public virtual Chapter? Chapter { get; set; }
    }
}




