<!--For pc -->
<div class="desktop-carousel-container">
  <div
    *ngIf="hasCarouselItems"
    class="nav-control-left"
    (click)="next()"
    role="button"
    aria-label="Previous carousel item"
    tabindex="0"
    (keydown.enter)="next()"
    (keydown.space)="next()"
  >
    <svg
      class="nav-icon-left"
      viewBox="0 0 512 512"
      fill="currentColor"
      aria-hidden="true"
    >
      <polygon
        points="352,128.4 319.7,96 160,256 160,256 160,256 319.7,416 352,383.6 224.7,256 "
      />
    </svg>
  </div>
  <div
    *ngIf="shouldShowHoverDetails"
    [@formAnimation]
    class="comic-details-overlay"
  >
    <div class="comic-details-content">
      <div class="comic-header">
        <a class="comic-title">
          <h4>{{ hoverComic?.title }}</h4>
        </a>
        <div class="comic-status-container">
          @if (hoverComic?.status === 0) {
          <div class="status-indicator-ongoing"></div>
          <div><PERSON><PERSON> tiến hành</div>
          } @else {
          <svg
            class="status-indicator-completed"
            xmlns="http://www.w3.org/2000/svg"
            height="6"
            width="6"
            viewBox="0 0 512 512"
          >
            <path
              fill="#2debb2"
              d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"
            />
          </svg>
          <div>Đã hoàn thành</div>
          }
        </div>
      </div>

      <div class="genre-tags-container">
        <a
          *ngFor="let tag of hoverComic?.genres | slice : 0 : 10; index as i"
          class="genre-tag"
        >
          @if (i === 0) {
          <span class="primary-genre-tag">{{ tag.title }}</span>
          } @else {
          <span class="secondary-genre-tag">
            {{ tag.title }}
          </span>
          }
        </a>
      </div>
      <div class="comic-stats-container">
        <div class="rating-stat">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            class="feather feather-star icon rel"
            viewBox="0 0 24 24"
            style="color: currentcolor"
          >
            <path
              d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01z"
            ></path></svg
          >{{ hoverComic?.rating }}
        </div>
        <div class="bookmark-stat">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="none"
            viewBox="0 0 24 24"
            class="stat-icon"
            aria-hidden="true"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"
            ></path>
          </svg>
          {{ hoverComic?.rating }}
        </div>
        <div class="view-count-stat">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            class="feather feather-eye icon small text-icon-contrast text-undefined"
            viewBox="0 0 24 24"
          >
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          {{ hoverComic?.viewCount | numeral }}
        </div>
      </div>
      <div class="comic-description">
        <p
          class="description-text"
          [innerHTML]="
            hoverComic?.description
              | fillDescription
                : hoverComic!.id
                : hoverComic!.title
                : hoverComic!.url
          "
        ></p>
      </div>
    </div>
  </div>
  <div
    *ngIf="hasCarouselItems"
    class="nav-control-right"
    (click)="prev()"
    role="button"
    aria-label="Next carousel item"
    tabindex="0"
    (keydown.enter)="prev()"
    (keydown.space)="prev()"
  >
    <svg
      fill="currentColor"
      class="nav-icon-right"
      viewBox="0 0 512 512"
      aria-hidden="true"
    >
      <polygon
        points="160,128.4 192.3,96 352,256 352,256 352,256 192.3,416 160,383.6 287.3,256 "
      />
    </svg>
  </div>
  <div
    *ngFor="let comics of carouselItems; let i = index; trackBy: trackByCarouselIndex"
    #carouselItemPc
    class="carousel-item-desktop"
    [style.left]="((i-1) * 100) / grid + '%' || '0%'"
  >
    <app-carousel-layout
      [comics]="comics"
      (comicHover)="OnComicHover($event)"
    ></app-carousel-layout>
  </div>
  <div
    *ngIf="!hasCarouselItems"
    class="loading-container"
    role="status"
    aria-label="Loading carousel content"
  >
    <app-spinner [sizeSpinner]="'40'"></app-spinner>
  </div>
</div>
<!--For Phone -->
<div class="mobile-carousel-container">
  <div
    class="mobile-nav-left"
    (click)="next()"
    role="button"
    aria-label="Previous mobile carousel item"
    tabindex="0"
    (keydown.enter)="next()"
    (keydown.space)="next()"
  >
    <svg
      class="mobile-nav-icon-left"
      viewBox="0 0 512 512"
      fill="currentColor"
      aria-hidden="true"
    >
      <polygon
        points="352,128.4 319.7,96 160,256 160,256 160,256 319.7,416 352,383.6 224.7,256 "
      />
    </svg>
  </div>
  <div
    #carouselItemMobile
    *ngFor="let comic of comicList; let i = index; trackBy: trackByComicId"
    [style.left]="((i-1) * 100) / grid + '%' || '0%'"
    [style.width]="100 / grid + '%'"
    class="carousel-item-mobile"
  >
    <a
      [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
      [attr.aria-label]="'Read comic: ' + comic.title"
    >
      <img
        loading="lazy"
        class="mobile-comic-image"
        [src]="comic.coverImage"
        [alt]="comic.title"
        onerror="this.src='/option2.png'"
      />
    </a>
  </div>

  <div
    class="mobile-nav-right"
    (click)="prev()"
    role="button"
    aria-label="Next mobile carousel item"
    tabindex="0"
    (keydown.enter)="prev()"
    (keydown.space)="prev()"
  >
    <svg
      fill="currentColor"
      class="mobile-nav-icon-right"
      viewBox="0 0 512 512"
      aria-hidden="true"
    >
      <polygon
        points="160,128.4 192.3,96 352,256 352,256 352,256 192.3,416 160,383.6 287.3,256 "
      />
    </svg>
  </div>
</div>
<app-swiper
  *ngIf="isBrowser"
  class="swiper-container"
  (nextChange)="SlideChange($event)"
></app-swiper>
