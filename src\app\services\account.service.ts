import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { INotification, IServiceResponse, IUser, VoteInfo } from '@schema';
import { Observable } from 'rxjs';
import { StorageService } from './storage.service';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root',
})
export class AccountService {
  constructor(private httpClient: HttpClient,
    private storageService: StorageService,
    private urlService: UrlService) { }
  AddComment(chapterId: number, content: string, replyfromUser?: number, replyfromCmt?: number | null) {
    return this.httpClient.post(`${this.urlService.API_URL}/user/comment`, {
      chapterId,
      content,
      replyfromUser,
      replyfromCmt
    });
  }
  getAuthorizationToken(): string | undefined {
    return this.GetUser()?.token;
  }
  user?: IUser;
  isAuthenticated(): boolean {
    return !!this.GetUser();
  }

  GetUser() {
    this.user = this.storageService.GetUserData();
    return this.user;
  }
  GetUserInfo() {
    return this.httpClient.get(`${this.urlService.API_URL}/user/me`);
  }
  GetUserById(id: number) {
    return this.httpClient.get<IServiceResponse<IUser>>(`${this.urlService.API_URL}/user/${id}`);
  }
  Follow(comicid: number, isFollow: boolean) {
    return this.httpClient.post<IServiceResponse<number>>(
      `${this.urlService.API_URL}/user/follow?comicid=${comicid}&follow=${isFollow}`,
      {},
    );
  }
  GetFollowedComics(page = 1, size = 28) {
    const searchParams = new URLSearchParams({ page: page.toString(), size: size.toString() }).toString();

    return this.httpClient.get(`${this.urlService.API_URL}/user/followed-comics?${searchParams}`);
  }
  Login(email: string, password: string) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/auth/login?email=${email}&password=${password}`,
    );
  }
  LoginWithSocial(user: any) {

    return this.httpClient.post(
      `${this.urlService.API_URL}/auth/social-login`, user,
    );
  }
  SendEmailConfirm(email: string, userid: number) {
    return this.httpClient.get(`${this.urlService.API_URL}/auth/send-confirm-email?email=${email}&userId=${userid}`)
  }
  GetCommentsByComicId(comicId: number, page: number, step: number) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/comments/comic/${comicId}?page=${page}&size=${step}`,
    );
  }
  Logout() {
    this.storageService.RemoveUserData();
  }
  Register(name: string, email: string, password: string) {
    return this.httpClient.post(`${this.urlService.API_URL}/auth/register`, {
      name,
      email,
      password,
    });
  }
  ForgetPassword(email: string) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/auth/forgot?email=${email}`,
    );
  }
  UpdateAvatar(avatar: FormData) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/update/avatar`,
      avatar,
    );
  }
  SaveUser(user: IUser) {

    this.storageService.SetUserData(user);
  }
  GetUserDeconfirm() {
    return this.storageService.GetUserDeconfirm();
  }
  SaveUserDeconfirm(infoUser: any) {
    this.storageService.SetUserDeconfirm(infoUser);
  }
  GetRememberMeData() {
    return this.storageService.GetRememberMeData();
  }
  SetRememberMeData(rememberMeData: any) {
    this.storageService.SetRememberMeData(rememberMeData);
  }
  UpdateInfo(user: IUser) {
    return this.httpClient.post(`${this.urlService.API_URL}/user/update`, user);
  }
  UpdatePassword(newPassword: string) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/update/password`,
      newPassword,
    );
  }
  UpdateTypeLevel(typeLevel: number) {
    return this.httpClient.post(`${this.urlService.API_URL}/user/update/typelevel/${typeLevel}`, {});
  }
  UpdateMaxim(maxim: string | null) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/update/maxim?maxim=${maxim}`,
      {},
    );
  }

  getUserNotify() {
    return this.httpClient.get<IServiceResponse<INotification[]>>(`${this.urlService.API_URL}/user/notify`);
  }
  updateUserNotify(idNotify: number | null, IsRead: boolean | null) {
    return this.httpClient.post(`${this.urlService.API_URL}/user/notify/update`, {
      ID: idNotify,
      IsRead: IsRead,
    });
  }
  deleteUserNotify(idNotify: number | null) {
    return this.httpClient.delete(
      `${this.urlService.API_URL}/user/notify/delete/${idNotify}`,
    );
  }
  voteComic(
    comicId: number,
    voteValue: number,
  ): Observable<IServiceResponse<VoteInfo>> {
    const url = `${this.urlService.API_URL}/user/vote/update`;
    const body = { comicId, voteValue };
    return this.httpClient.post(url, body) as Observable<
      IServiceResponse<VoteInfo>
    >;
  }

  getVoteInfo(comicId: number): Observable<IServiceResponse<VoteInfo>> {
    const endpoint = `${this.urlService.API_URL}/user/vote`;
    const params = `?comicid=${comicId}`;
    const url = `${endpoint}${params}`;

    return this.httpClient.get(url) as Observable<IServiceResponse<VoteInfo>>;
  }
  unvoteComic(comicId: number): Observable<IServiceResponse<void>> {
    const apiUrl = `${this.urlService.API_URL}/user/vote/delete`;
    const queryParams = `?comicId=${comicId}`;
    const requestUrl = `${apiUrl}${queryParams}`;

    return this.httpClient.delete(requestUrl) as Observable<
      IServiceResponse<void>
    >;
  }
}
