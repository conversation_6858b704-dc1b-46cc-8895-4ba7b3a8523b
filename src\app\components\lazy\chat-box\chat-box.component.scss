// Modern Chat Box Component - Comic Website Design

// Chat Box Container
.chat-box-container {
  @apply fixed bottom-0 right-0 md:bottom-4 md:right-4 h-[500px] z-50 w-full sm:w-fit;
}

.chat-box-wrapper {
  @apply bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-t-2xl md:rounded-2xl overflow-hidden relative flex h-full gap-0 w-full sm:w-[480px] shadow-2xl;
}

// Chat Menu (Users List)
.chat-menu {
  @apply absolute z-[100] bg-white dark:bg-neutral-800 left-0 top-0 h-full w-[280px] max-w-xs flex-none flex-col lg:flex transition-all duration-300 ease-in-out border-r border-gray-200 dark:border-neutral-700;

  &.chat-menu-hidden {
    @apply w-0 overflow-hidden;
  }
}

.chat-menu-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-neutral-700 bg-gray-50 dark:bg-neutral-700;
}

.chat-menu-title {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.chat-menu-badge {
  @apply flex items-center justify-center w-6 h-6 bg-primary-100 text-white text-xs font-bold rounded-full;
}

.chat-users-container {
  @apply flex-1 overflow-y-auto;
}

.chat-user-item {
  @apply flex w-full items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-neutral-700 transition-colors duration-200 border-none cursor-pointer text-left;

  &.chat-user-active {
    @apply bg-primary-100/10 border-r-2 border-primary-100;
  }
}

.chat-user-avatar-wrapper {
  @apply relative flex-shrink-0;
}

.chat-user-avatar {
  @apply w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-neutral-600;
}

.chat-user-status {
  @apply absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 border-2 border-white dark:border-neutral-800 rounded-full;
}

.chat-user-info {
  @apply flex-1 min-w-0;
}

.chat-user-details {
  @apply flex-1;
}

.chat-user-name {
  @apply text-sm font-semibold text-gray-900 dark:text-white truncate mb-1;
}

.chat-user-preview {
  @apply text-xs text-gray-500 dark:text-gray-400 truncate;
}

.chat-user-meta {
  @apply flex flex-col items-end gap-1;
}

.chat-user-time {
  @apply text-xs text-gray-400 dark:text-gray-500;
}

.chat-user-unread {
  @apply flex items-center justify-center min-w-5 h-5 bg-primary-100 text-white text-xs font-bold rounded-full px-1;
}

// Mobile Overlay
.chat-overlay {
  @apply absolute z-[5] h-full w-full bg-black/60 transition-opacity duration-300;

  &.chat-overlay-hidden {
    @apply hidden;
  }
}

// Chat Main Area
.chat-main-area {
  @apply flex flex-col w-full sm:w-[480px] relative h-full;
}

// Chat Header
.chat-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-neutral-700 bg-white dark:bg-neutral-800;
}

.chat-header-left {
  @apply flex items-center gap-3;
}

.chat-menu-toggle {
  @apply flex items-center justify-center w-8 h-8 text-gray-600 dark:text-gray-400 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200;
}

.chat-menu-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chat-header-user {
  @apply flex items-center gap-3;
}

.chat-header-avatar-wrapper {
  @apply relative;
}

.chat-header-avatar {
  @apply w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-neutral-600;
}

.chat-header-status {
  @apply absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-neutral-800 rounded-full;
}

.chat-header-info {
  @apply flex flex-col;
}

.chat-header-name {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

.chat-header-status-text {
  @apply text-xs text-green-500 font-medium;
}

.chat-header-actions {
  @apply flex items-center gap-2;
}

.chat-action-btn {
  @apply flex items-center justify-center w-8 h-8 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border-none cursor-pointer;

  &.chat-close-btn {
    @apply hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20;
  }
}

.chat-action-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Chat Messages
.chat-messages-container {
  @apply flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-neutral-900;
}

.message-wrapper {
  @apply w-full;
}

.message-item {
  @apply flex items-start gap-3 max-w-full;

  &.message-sent {
    @apply flex-row-reverse;
  }

  &.message-received {
    @apply flex-row;
  }
}

.message-avatar {
  @apply flex-shrink-0;
}

.message-avatar-img {
  @apply w-8 h-8 rounded-full object-cover border border-gray-200 dark:border-neutral-600;
}

.message-content {
  @apply flex flex-col gap-1 max-w-xs;

  .message-sent & {
    @apply items-end;
  }

  .message-received & {
    @apply items-start;
  }
}

.message-bubble {
  @apply px-4 py-2 rounded-2xl text-sm leading-relaxed break-words;

  .message-sent & {
    @apply bg-primary-100 text-white rounded-br-md;
  }

  .message-received & {
    @apply bg-white dark:bg-neutral-700 text-gray-900 dark:text-white border border-gray-200 dark:border-neutral-600 rounded-bl-md;
  }
}

.message-time {
  @apply text-xs text-gray-500 dark:text-gray-400 px-2;
}

// Empty State
.chat-empty-state {
  @apply flex flex-col items-center justify-center h-full text-center p-8;
}

.empty-state-icon {
  @apply w-16 h-16 text-gray-400 dark:text-gray-500 mb-4;

  svg {
    @apply w-full h-full;
    fill: none;
    stroke: currentColor;
    stroke-width: 1.5;
    stroke-linecap: round;
    stroke-linejoin: round;
  }
}

.empty-state-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-2;
}

.empty-state-subtitle {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

// Chat Input
.chat-input-container {
  @apply border-t border-gray-200 dark:border-neutral-700 bg-white dark:bg-neutral-800;
}

.chat-input-wrapper {
  @apply flex items-center gap-3 p-4;
}

.chat-input-actions {
  @apply flex items-center;
}

.chat-input-btn {
  @apply relative flex items-center justify-center w-10 h-10 text-gray-600 dark:text-gray-400 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border-none cursor-pointer;
}

.chat-input-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.emoji-picker-wrapper {
  @apply absolute bottom-full right-0 mb-2 z-50;
}

.emoji-picker {
  @apply shadow-2xl rounded-lg border border-gray-200 dark:border-neutral-700;
}

.chat-input-field-wrapper {
  @apply flex-1;
}

.chat-input-field {
  @apply w-full px-4 py-3 text-sm text-gray-900 dark:text-white bg-gray-100 dark:bg-neutral-700 border border-gray-200 dark:border-neutral-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-100 focus:border-transparent transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400;
}

.chat-send-btn {
  @apply flex items-center justify-center w-10 h-10 bg-primary-100 hover:bg-primary-200 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 border-none cursor-pointer;
}

.chat-send-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Responsive Design
@media (max-width: 768px) {
  .chat-box-container {
    @apply bottom-0 right-0 h-full;
  }

  .chat-box-wrapper {
    @apply rounded-none w-full;
  }

  .chat-menu {
    @apply w-full;
  }

  .chat-main-area {
    @apply w-full;
  }
}

@media (max-width: 640px) {
  .chat-menu-header {
    @apply p-3;
  }

  .chat-user-item {
    @apply p-3;
  }

  .chat-header {
    @apply p-3;
  }

  .chat-input-wrapper {
    @apply p-3 gap-2;
  }

  .chat-input-field {
    @apply py-2 px-3 text-sm;
  }
}

// Accessibility
.chat-user-item:focus,
.chat-menu-toggle:focus,
.chat-action-btn:focus,
.chat-input-btn:focus,
.chat-send-btn:focus {
  @apply outline-none ring-2 ring-primary-100/50;
}

// High Contrast Mode
@media (prefers-contrast: high) {
  .chat-box-wrapper {
    @apply border-2 border-black dark:border-white;
  }

  .chat-user-item,
  .chat-action-btn,
  .chat-input-btn,
  .chat-send-btn {
    @apply border border-gray-600;
  }

  .message-bubble {
    .message-sent & {
      @apply border-2 border-primary-100;
    }

    .message-received & {
      @apply border-2 border-gray-600;
    }
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  .chat-menu,
  .chat-overlay,
  .chat-user-item,
  .chat-action-btn,
  .chat-input-btn,
  .chat-send-btn,
  .chat-input-field {
    @apply transition-none;
  }
}

// Performance Optimizations
.chat-box-wrapper,
.chat-menu,
.chat-user-item,
.chat-action-btn,
.chat-input-btn,
.chat-send-btn {
  transform: translateZ(0);
  backface-visibility: hidden;
}