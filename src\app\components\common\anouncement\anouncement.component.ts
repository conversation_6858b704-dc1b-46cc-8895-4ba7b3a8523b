import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { Announcement, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';

@Component({
    selector: 'app-anouncement',
    templateUrl: './anouncement.component.html',
    styleUrl: './anouncement.component.scss',
    standalone: true,
    imports: [CommonModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
    encapsulation: ViewEncapsulation.None,
})
export class AnouncementComponent implements OnInit {
  anouncements: Announcement[] = []
  constructor(private comicService: ComicService,
    private cd: ChangeDetectorRef,
  ) {
  }
  ngOnInit(): void {
    this.comicService.getAnouncement().subscribe((res: IServiceResponse<Announcement[]>) => {
      this.anouncements = res.data || []
      this.cd.detectChanges();

    })
  }
}
