<!-- Daily Quests Container -->
<div class="daily-quests-container">
  <!-- Header with User Stats -->
  <div class="quest-header" *ngIf="userStats">
    <div class="user-level-card">
      <div class="level-info">
        <div class="level-badge">
          <span class="level-number">{{ userStats.level }}</span>
          <span class="level-label">Level</span>
        </div>
        <div class="experience-info">
          <div class="exp-text">
            <span class="current-exp">{{ userStats.experience }}</span>
            <span class="separator">/</span>
            <span class="next-level-exp">{{ userStats.nextLevelExp }}</span>
            <span class="exp-label">EXP</span>
          </div>
          <div class="exp-bar">
            <div class="exp-progress" [style.width.%]="getExperiencePercentage()"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">🔥</div>
        <div class="stat-info">
          <div class="stat-value">{{ userStats.streakDays }}</div>
          <div class="stat-label">Ngày liên tiếp</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
          <div class="stat-value">{{ userStats.totalCompleted }}</div>
          <div class="stat-label">Nhiệm vụ hoàn thành</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">🪙</div>
        <div class="stat-info">
          <div class="stat-value">{{ userStats.totalRewardsEarned }}</div>
          <div class="stat-label">Xu kiếm được</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="quest-tabs">
    <button
      class="tab-button"
      [class.active]="selectedTab === 'daily'"
      (click)="switchTab('daily')"
    >
      <span class="tab-icon">📅</span>
      <span class="tab-label">Nhiệm vụ hằng ngày</span>
      <span class="tab-count">{{ dailyQuests.length }}</span>
    </button>

    <button
      class="tab-button"
      [class.active]="selectedTab === 'weekly'"
      (click)="switchTab('weekly')"
    >
      <span class="tab-icon">📊</span>
      <span class="tab-label">Nhiệm vụ tuần</span>
      <span class="tab-count">{{ weeklyQuests.length }}</span>
    </button>

    <button
      class="refresh-button"
      (click)="refreshQuests()"
      [disabled]="isLoading"
      title="Làm mới nhiệm vụ"
    >
      <span class="refresh-icon" [class.spinning]="isLoading">🔄</span>
    </button>
  </div>

  <!-- Daily Quests -->
  <div class="quest-content" *ngIf="selectedTab === 'daily'">
    <div class="quest-list">
      <div 
        *ngFor="let quest of dailyQuests; trackBy: trackByQuestId" 
        class="quest-card"
        [class.completed]="quest.status === QuestStatus.COMPLETED"
        [class.expired]="quest.status === QuestStatus.EXPIRED"
      >
        <div class="quest-icon">
          {{ getQuestIcon(quest.icon) }}
        </div>
        
        <div class="quest-info">
          <div class="quest-header-info">
            <h3 class="quest-title">{{ quest.title }}</h3>
            <span class="difficulty-badge" [ngClass]="getDifficultyBadgeColor(quest.difficulty)">
              {{ quest.difficulty }}
            </span>
          </div>
          
          <p class="quest-description">{{ quest.description }}</p>
          
          <div class="quest-progress">
            <div class="progress-info">
              <span class="progress-text">{{ quest.current }}/{{ quest.target }}</span>
              <span class="time-remaining">{{ getTimeRemaining(quest.expiresAt) }}</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="getProgressPercentage(quest)"></div>
            </div>
          </div>
        </div>
        
        <div class="quest-reward">
          <div class="reward-info">
            <span class="reward-icon">{{ getRewardIcon(quest.reward.type) }}</span>
            <span class="reward-text">{{ quest.reward.description }}</span>
          </div>
          
          <button
            class="claim-button"
            [class.completed]="quest.status === QuestStatus.COMPLETED"
            [disabled]="quest.status !== QuestStatus.COMPLETED || isLoading"
            (click)="claimReward(quest)"
          >
            <span *ngIf="isLoading" class="loading-spinner">⏳</span>
            <span *ngIf="!isLoading && quest.status === QuestStatus.COMPLETED">Nhận thưởng</span>
            <span *ngIf="!isLoading && quest.status === QuestStatus.ACTIVE">Chưa hoàn thành</span>
            <span *ngIf="!isLoading && quest.status === QuestStatus.EXPIRED">Đã hết hạn</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Weekly Quests -->
  <div class="quest-content" *ngIf="selectedTab === 'weekly'">
    <div class="quest-list">
      <div 
        *ngFor="let quest of weeklyQuests; trackBy: trackByQuestId" 
        class="quest-card weekly-quest"
        [class.completed]="quest.status === QuestStatus.COMPLETED"
        [class.expired]="quest.status === QuestStatus.EXPIRED"
      >
        <div class="quest-icon">
          {{ getQuestIcon(quest.icon) }}
        </div>
        
        <div class="quest-info">
          <div class="quest-header-info">
            <h3 class="quest-title">{{ quest.title }}</h3>
            <span class="difficulty-badge" [ngClass]="getDifficultyBadgeColor(quest.difficulty)">
              {{ quest.difficulty }}
            </span>
          </div>
          
          <p class="quest-description">{{ quest.description }}</p>
          
          <div class="quest-progress">
            <div class="progress-info">
              <span class="progress-text">{{ quest.current }}/{{ quest.target }}</span>
              <span class="time-remaining">{{ getTimeRemaining(quest.expiresAt) }}</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="getProgressPercentage(quest)"></div>
            </div>
          </div>
        </div>
        
        <div class="quest-reward">
          <div class="reward-info">
            <span class="reward-icon">{{ getRewardIcon(quest.reward.type) }}</span>
            <span class="reward-text">{{ quest.reward.description }}</span>
          </div>
          
          <button
            class="claim-button"
            [class.completed]="quest.status === QuestStatus.COMPLETED"
            [disabled]="quest.status !== QuestStatus.COMPLETED || isLoading"
            (click)="claimReward(quest)"
          >
            <span *ngIf="isLoading" class="loading-spinner">⏳</span>
            <span *ngIf="!isLoading && quest.status === QuestStatus.COMPLETED">Nhận thưởng</span>
            <span *ngIf="!isLoading && quest.status === QuestStatus.ACTIVE">Chưa hoàn thành</span>
            <span *ngIf="!isLoading && quest.status === QuestStatus.EXPIRED">Đã hết hạn</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="(selectedTab === 'daily' && dailyQuests.length === 0) || (selectedTab === 'weekly' && weeklyQuests.length === 0)">
    <div class="empty-icon">📋</div>
    <h3 class="empty-title">Không có nhiệm vụ nào</h3>
    <p class="empty-description">
      <span *ngIf="selectedTab === 'daily'">Hôm nay bạn đã hoàn thành tất cả nhiệm vụ!</span>
      <span *ngIf="selectedTab === 'weekly'">Tuần này bạn đã hoàn thành tất cả nhiệm vụ!</span>
    </p>
  </div>
</div>
