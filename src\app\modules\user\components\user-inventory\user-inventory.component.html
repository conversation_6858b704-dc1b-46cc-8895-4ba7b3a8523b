<div class="inventory-container">
  <!-- Header -->
  <div class="inventory-header">
    <div class="inventory-header__title">
      <h2 class="inventory-title"><PERSON><PERSON></h2>
      <p class="inventory-subtitle"><PERSON>u<PERSON>n lý vật phẩm của bạn</p>
    </div>
    
    <div class="inventory-header__actions">
      <!-- View Mode Toggle -->
      <div class="view-mode-toggle">
        <button 
          class="view-mode-btn"
          [class.view-mode-btn--active]="viewMode() === 'grid'"
          (click)="onViewModeChange('grid')"
          title="Xem dạng lưới"
        >
          <i class="icon-grid"></i>
        </button>
        <button 
          class="view-mode-btn"
          [class.view-mode-btn--active]="viewMode() === 'list'"
          (click)="onViewModeChange('list')"
          title="Xem dạng danh sách"
        >
          <i class="icon-list"></i>
        </button>
      </div>

      <!-- Card Size Toggle (for grid view) -->
      <div class="card-size-toggle" *ngIf="viewMode() === 'grid'">
        <button 
          class="size-btn"
          [class.size-btn--active]="cardSize() === 'small'"
          (click)="onCardSizeChange('small')"
          title="Nhỏ"
        >S</button>
        <button 
          class="size-btn"
          [class.size-btn--active]="cardSize() === 'medium'"
          (click)="onCardSizeChange('medium')"
          title="Vừa"
        >M</button>
        <button 
          class="size-btn"
          [class.size-btn--active]="cardSize() === 'large'"
          (click)="onCardSizeChange('large')"
          title="Lớn"
        >L</button>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="inventory-filters">
    <!-- Search -->
    <div class="filter-group">
      <label class="filter-label">Tìm kiếm</label>
      <input 
        type="text"
        class="filter-input"
        placeholder="Tìm theo tên hoặc mô tả..."
        [value]="filter().searchTerm || ''"
        (input)="onFilterChange({ searchTerm: $any($event.target).value })"
      >
    </div>

    <!-- Category Filter -->
    <div class="filter-group">
      <label class="filter-label">Loại vật phẩm</label>
      <select 
        class="filter-select"
        [value]="filter().category || ''"
        (change)="onFilterChange({ category: $any($event.target).value || undefined })"
      >
        <option value="">Tất cả</option>
        <option *ngFor="let category of categoryOptions" [value]="category">
          {{ ITEM_CATEGORY_INFO[category].displayName }}
        </option>
      </select>
    </div>

    <!-- Rarity Filter -->
    <div class="filter-group">
      <label class="filter-label">Độ hiếm</label>
      <select 
        class="filter-select"
        [value]="filter().rarity || ''"
        (change)="onFilterChange({ rarity: $any($event.target).value || undefined })"
      >
        <option value="">Tất cả</option>
        <option *ngFor="let rarity of rarityOptions" [value]="rarity">
          {{ ITEM_RARITY_INFO[rarity].displayName }}
        </option>
      </select>
    </div>

    <!-- Sort -->
    <div class="filter-group">
      <label class="filter-label">Sắp xếp</label>
      <select 
        class="filter-select"
        [value]="filter().sortBy || ''"
        (change)="onFilterChange({ sortBy: $any($event.target).value || undefined })"
      >
        <option value="">Mặc định</option>
        <option *ngFor="let option of sortOptions" [value]="option.value">
          {{ option.label }}
        </option>
      </select>
    </div>

    <!-- Sort Order -->
    <div class="filter-group" *ngIf="filter().sortBy">
      <label class="filter-label">Thứ tự</label>
      <select 
        class="filter-select"
        [value]="filter().sortOrder || 'asc'"
        (change)="onFilterChange({ sortOrder: $any($event.target).value })"
      >
        <option value="asc">Tăng dần</option>
        <option value="desc">Giảm dần</option>
      </select>
    </div>
  </div>

  <!-- Selected Items Actions -->
  <div class="selected-actions" *ngIf="hasSelectedItems">
    <div class="selected-info">
      <span>Đã chọn {{ selectedItemsCount }} vật phẩm</span>
    </div>
    <div class="selected-buttons">
      <button class="action-btn action-btn--secondary" (click)="clearSelection()">
        Bỏ chọn tất cả
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading()">
    <div class="loading-spinner"></div>
    <p>Đang tải kho đồ...</p>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!isLoading() && filteredItems.length === 0">
    <div class="empty-state__icon">
      <i class="icon-empty-box"></i>
    </div>
    <h3 class="empty-state__title">Không có vật phẩm</h3>
    <p class="empty-state__description">
      <span *ngIf="filter().searchTerm || filter().category || filter().rarity">
        Không tìm thấy vật phẩm phù hợp với bộ lọc.
      </span>
      <span *ngIf="!filter().searchTerm && !filter().category && !filter().rarity">
        Kho đồ của bạn đang trống. Hãy hoàn thành nhiệm vụ để nhận vật phẩm!
      </span>
    </p>
  </div>

  <!-- Items Grid/List -->
  <div class="inventory-content" *ngIf="!isLoading() && filteredItems.length > 0">
    <!-- Grid View -->
    <div 
      class="items-grid"
      [class.items-grid--small]="cardSize() === 'small'"
      [class.items-grid--medium]="cardSize() === 'medium'"
      [class.items-grid--large]="cardSize() === 'large'"
      *ngIf="viewMode() === 'grid'"
    >
      <app-item-card
        *ngFor="let item of filteredItems; trackBy: trackByItemId"
        [item]="item"
        [size]="cardSize()"
        [selectable]="true"
        [selected]="selectedItems().has(item.template.id)"
        (actionClicked)="onItemAction($event)"
        (itemClicked)="onItemClick($event)"
        (selectionChanged)="onSelectionChanged($event)"
      ></app-item-card>
    </div>

    <!-- List View -->
    <div class="items-list" *ngIf="viewMode() === 'list'">
      <div class="list-header">
        <div class="list-col list-col--item">Vật phẩm</div>
        <div class="list-col list-col--category">Loại</div>
        <div class="list-col list-col--rarity">Độ hiếm</div>
        <div class="list-col list-col--quantity">Số lượng</div>
        <div class="list-col list-col--actions">Hành động</div>
      </div>
      
      <div class="list-body">
        <div 
          *ngFor="let item of filteredItems; trackBy: trackByItemId"
          class="list-row"
          [class.list-row--selected]="selectedItems().has(item.template.id)"
          [class.list-row--equipped]="item.isEquipped"
        >
          <div class="list-col list-col--item">
            <input 
              type="checkbox"
              [checked]="selectedItems().has(item.template.id)"
              (change)="onSelectionChanged({ item, selected: $any($event.target).checked })"
              class="list-checkbox"
            >
            <img 
              [src]="item.template.icon || '/assets/icons/default-item.svg'"
              [alt]="item.template.name"
              class="list-item-icon"
            >
            <div class="list-item-info">
              <span class="list-item-name">{{ item.template.name }}</span>
              <span class="list-item-description">{{ item.template.description }}</span>
            </div>
          </div>
          
          <div class="list-col list-col--category">
            <span class="category-badge">
              {{ ITEM_CATEGORY_INFO[item.template.category].displayName }}
            </span>
          </div>
          
          <div class="list-col list-col--rarity">
            <span 
              class="rarity-badge"
              [style.color]="ITEM_RARITY_INFO[item.template.rarity].color"
            >
              {{ ITEM_RARITY_INFO[item.template.rarity].displayName }}
            </span>
          </div>
          
          <div class="list-col list-col--quantity">
            {{ item.quantity || 1 }}
          </div>
          
          <div class="list-col list-col--actions">
            <button 
              *ngIf="item.canUse"
              class="list-action-btn list-action-btn--primary"
              (click)="onItemAction({ action: ItemActionType.USE, item })"
            >
              Sử dụng
            </button>
            <button
              *ngIf="item.canEquip && !item.isEquipped"
              class="list-action-btn list-action-btn--secondary"
              (click)="onItemAction({ action: ItemActionType.EQUIP, item })"
            >
              Trang bị
            </button>
            <button
              *ngIf="item.isEquipped"
              class="list-action-btn list-action-btn--secondary"
              (click)="onItemAction({ action: ItemActionType.UNEQUIP, item })"
            >
              Tháo
            </button>
            <button
              class="list-action-btn list-action-btn--ghost"
              (click)="onItemAction({ action: ItemActionType.VIEW_DETAILS, item })"
            >
              Chi tiết
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div class="inventory-pagination" *ngIf="!isLoading() && totalPages() > 1">
    <button 
      class="pagination-btn"
      [disabled]="currentPage() <= 1"
      (click)="onPageChange(currentPage() - 1)"
    >
      Trước
    </button>
    
    <span class="pagination-info">
      Trang {{ currentPage() }} / {{ totalPages() }}
    </span>
    
    <button 
      class="pagination-btn"
      [disabled]="currentPage() >= totalPages()"
      (click)="onPageChange(currentPage() + 1)"
    >
      Sau
    </button>
  </div>
</div>
