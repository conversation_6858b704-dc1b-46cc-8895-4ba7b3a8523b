.top-list-container {
    @apply border border-gray-200 dark:border-gray-900 dark:bg-neutral-800 rounded bg-gray-100 min-h-36;
  }

  .top-list-header {
    @apply flex justify-between rounded font-bold mt-2 w-full text-center text-[0.75rem] p-2;
  }

  .top-list-tab {
    @apply w-full flex items-center justify-center text-gray-900 dark:text-white m-1 cursor-pointer;
  }

  .top-list-tab-active {
    @apply text-primary-100 border-b-2 border-primary-100;
  }

  .top-list-content {
    @apply w-full overflow-x-auto bg-gray-100 dark:bg-neutral-800 rounded-b p-2;
  }

  .top-list {
    @apply grid grid-cols-4 md:grid-cols-8 xl:grid-cols-1 xl:gap-2  dark:text-white;
  }

  .top-list-item {
    @apply xl:flex hover:bg-gradient-to-r from-gray-300 dark:from-neutral-800 dark:bg-neutral-900 to-gray-100 min-w-24 w-full  border-gray-200 dark:hover:border-gray-600 bg-white rounded-sm;
  }

  .top-list-item-content {
    @apply flex flex-col xl:flex-row justify-between h-full items-center mx-2;
  }

  .top-list-item-image {
    @apply relative w-full xl:w-14 shrink-0 mt-2 xl:mb-2 h-0 xl:h-auto xl:pb-0 pb-[140%] flex;
  }

  .top-list-item-rank {
    @apply absolute z-[1] left-[calc(50%-12px)] -top-2 rounded-full size-5 bg-secondary-100 text-white flex items-center justify-center mx-auto font-bold;
  }

  .top-list-item-cover {
    @apply absolute z-0 xl:relative h-full xl:h-[4.5rem] w-full rounded object-cover shadow-sm;
  }

  .top-list-item-info {
    @apply flex flex-col xl:block xl:ml-1 p-1 items-center;
  }

  .top-list-item-title {
    @apply mb-1 font-semibold md:text-sm text-xs;
  }

  .top-list-item-chapter {
    @apply md:text-sm text-xs dark:text-gray-200 text-gray-700;
  }

  .top-list-item-view {
    @apply  font-semibold flex items-center space-x-1 text-primary-100;
  }

  .top-list-item-view-icon {
    @apply h-4 w-4;
  }

  .top-list-item-view-count {
    @apply  text-xs md:text-sm uppercase;
  }