// Modern Top List Component - Comic Website Design
.top-list-container {
  @apply bg-white dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 shadow-sm overflow-hidden;
}

// Enhanced Header
.top-list-header {
  @apply w-full flex items-center justify-center py-3 bg-gray-50 dark:bg-neutral-800 border-b border-gray-200 dark:border-neutral-700;
} 

.header-title-section {
  @apply flex items-center gap-2 sm:gap-3;
}

.header-icon {
  @apply w-4 h-4 sm:w-5 sm:h-5 text-primary-100;
  fill: currentColor;
  stroke: none;
}

.header-title {
  @apply text-base md:text-lg font-bold text-gray-900 dark:text-white;
}

// Tab Navigation
.tab-navigation {
  @apply flex items-center gap-0.5 md:gap-1 bg-gray-100 dark:bg-neutral-700 rounded-lg p-0.5 md:p-1;
}

.tab-button {
  @apply flex items-center gap-2 px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white rounded-md transition-all duration-200 border-none cursor-pointer;

  &.tab-active {
    @apply bg-white dark:bg-neutral-600 text-primary-100 shadow-sm;
  }
}

.tab-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.tab-text {
  @apply font-medium;
}

// Content Area
.top-list-content {
  @apply bg-white dark:bg-dark-bg;
}

// Empty State
.empty-state {
  @apply p-8 text-center;
}

.empty-state-content {
  @apply space-y-4;
}

.empty-state-icon {
  @apply w-16 h-16 mx-auto text-gray-300 dark:text-gray-600;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.empty-state-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.empty-state-description {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

// Top Comics List
.top-comics-list {
  @apply grid grid-cols-4 gap-2 p-2 lg:grid-cols-8 xl:grid-cols-1;
}

.top-comic-item {
  @apply transition-all duration-200 hover:transform hover:scale-[1.02];
}

.comic-item-content {
  @apply relative flex flex-col xl:flex-row items-center gap-1 p-1 sm:gap-2 sm:p-2 md:gap-3 md:p-2.5 bg-gray-50 dark:bg-neutral-800/50 hover:bg-gray-100 dark:hover:bg-neutral-700/50 rounded-lg border border-gray-200 dark:border-neutral-700 transition-all duration-200;
}

// Rank Badge
.rank-badge {
  @apply absolute -top-2 left-1/2 xl:top-0 xl:left-8 transform -translate-x-1/2 z-10 flex items-center justify-center w-5 h-5 rounded-full font-bold text-white text-xs md:text-xs xl:text-sm shadow-md;

  &.rank-gold {
    @apply bg-gradient-to-br from-yellow-400 to-yellow-600;
  }

  &.rank-silver {
    @apply bg-gradient-to-br from-gray-300 to-gray-500;
  }

  &.rank-bronze {
    @apply bg-gradient-to-br from-orange-400 to-orange-600;
  }

  &.rank-default {
    @apply bg-gradient-to-br from-gray-400 to-gray-600;
  }
}

.rank-number {
  @apply font-bold;
}

// Comic Image
.comic-image-container {
  @apply relative flex-shrink-0;
}

.comic-image-link {
  @apply relative block overflow-hidden rounded-lg;
}

.comic-image {
  @apply w-16 h-20 md:w-12 md:h-16 object-cover transition-transform duration-200 hover:scale-105;
}

.image-overlay {
  @apply absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors duration-200;
}

// Comic Info
.comic-info {
  @apply flex-1 min-w-0 space-y-2 xl:space-y-1;
}

.comic-main-info {
  @apply space-y-1;
}

.comic-title {
  @apply text-xs sm:text-xs md:text-sm xl:text-base font-bold text-gray-900 dark:text-white line-clamp-2 xl:line-clamp-1 text-center xl:text-left;
}

.comic-title-link {
  @apply hover:text-primary-100 transition-colors duration-200;
}

.comic-chapter {
  @apply hidden xl:flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400;
}

.chapter-icon {
  @apply w-3 h-3 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.comic-chapter-link {
  @apply hover:text-primary-100 hover:underline transition-colors duration-200 line-clamp-1;
}

// Comic Stats
.comic-stats {
  @apply flex items-center justify-center xl:justify-start;
}

.view-count {
  @apply flex items-center gap-1 text-xs text-primary-100 font-semibold;
}

.view-icon {
  @apply w-3 h-3;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.view-count-text {
  @apply uppercase;
}

