import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { UrlService } from '@services/url.service';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
  InventoryPage,
  ItemCategory,
  ItemOperationResponse,
  ItemTemplate,
  UserEquippedItem,
  UserInventoryItem
} from '../interfaces/item.interface';

@Injectable({
  providedIn: 'root'
})
export class ItemService {
  private readonly API_BASE = '/item';

  // State management
  private inventorySubject = new BehaviorSubject<UserInventoryItem[]>([]);
  private equippedItemsSubject = new BehaviorSubject<UserEquippedItem[]>([]);
  private itemTemplatesSubject = new BehaviorSubject<ItemTemplate[]>([]);
  private currencySubject = new BehaviorSubject<Record<string, number>>({});

  // Public observables
  public inventory$ = this.inventorySubject.asObservable();
  public equippedItems$ = this.equippedItemsSubject.asObservable();
  public itemTemplates$ = this.itemTemplatesSubject.asObservable();
  public currency$ = this.currencySubject.asObservable();

  constructor(
    private http: HttpClient,
    private urlService: UrlService
  ) {
    this.initializeData();
  }

  // Initialize data from API
  private initializeData(): void {
    this.loadItemTemplates();
    this.loadUserInventory();
    this.loadUserEquippedItems();
    this.loadUserCurrency();
  }

  private loadItemTemplates(): void {
    this.getItemTemplates().subscribe({
      next: (templates) => {
        this.itemTemplatesSubject.next(templates);
      },
      error: (error) => {
        console.error('Error loading item templates:', error);
      }
    });
  }

  private loadUserInventory(): void {
    this.getUserInventory().subscribe({
      next: (inventory) => {
        this.inventorySubject.next(inventory.items);
      },
      error: (error) => {
        console.error('Error loading user inventory:', error);
      }
    });
  }

  private loadUserEquippedItems(): void {
    this.getUserEquippedItems().subscribe({
      next: (equippedItems) => {
        this.equippedItemsSubject.next(equippedItems);
      },
      error: (error) => {
        console.error('Error loading equipped items:', error);
      }
    });
  }

  private loadUserCurrency(): void {
    this.getUserCurrency().subscribe({
      next: (currency) => {
        this.currencySubject.next(currency);
      },
      error: (error) => {
        console.error('Error loading user currency:', error);
      }
    });
  }

  // API Methods
  getItemTemplates(category?: ItemCategory): Observable<ItemTemplate[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/templates`);
    // const params = category ? { category: category.toString() } : {};
    return this.http.get<ItemTemplate[]>(url).pipe(
      catchError(error => {
        console.error('Error fetching item templates:', error);
        return of([]);
      })
    );
  }

  getUserInventory(page: number = 1, pageSize: number = 20, category?: ItemCategory): Observable<InventoryPage> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/inventory`);
    const params: any = { page: page.toString(), pageSize: pageSize.toString() };
    if (category) {
      params.category = category.toString();
    }
    return this.http.get<InventoryPage>(url, { params }).pipe(
      catchError(error => {
        console.error('Error fetching user inventory:', error);
        return of({ items: [], totalItems: 0, currentPage: 1, totalPages: 1 });
      })
    );
  }

  getUserInventoryByCategory(category: ItemCategory): Observable<UserInventoryItem[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/inventory/category/${category}`);
    return this.http.get<UserInventoryItem[]>(url).pipe(
      catchError(error => {
        console.error('Error fetching user inventory by category:', error);
        return of([]);
      })
    );
  }

  getUserEquippedItems(): Observable<UserEquippedItem[]> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/equipped`);
    return this.http.get<UserEquippedItem[]>(url).pipe(
      catchError(error => {
        console.error('Error fetching equipped items:', error);
        return of([]);
      })
    );
  }

  getUserCurrency(): Observable<Record<string, number>> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/currency`);
    return this.http.get<Record<string, number>>(url).pipe(
      catchError(error => {
        console.error('Error fetching user currency:', error);
        return of({});
      })
    );
  }

  useItem(itemTemplateId: number, quantity: number = 1): Observable<ItemOperationResponse> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/use`);
    const body = { ItemTemplateID: itemTemplateId, Quantity: quantity };
    return this.http.post<ItemOperationResponse>(url, body).pipe(
      tap((response) => {
        if (response.success) {
          // Refresh inventory after successful use
          this.loadUserInventory();
          this.loadUserCurrency();
        }
      }),
      catchError(error => {
        console.error('Error using item:', error);
        return of({
          success: false,
          message: 'Có lỗi xảy ra khi sử dụng vật phẩm',
          data: null
        });
      })
    );
  }

  equipItem(itemTemplateId: number, slotType: string): Observable<ItemOperationResponse> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/equip`);
    const body = { ItemTemplateID: itemTemplateId, SlotType: slotType };
    return this.http.post<ItemOperationResponse>(url, body).pipe(
      tap((response) => {
        if (response.success) {
          // Refresh equipped items after successful equip
          this.loadUserEquippedItems();
        }
      }),
      catchError(error => {
        console.error('Error equipping item:', error);
        return of({
          success: false,
          message: 'Có lỗi xảy ra khi trang bị vật phẩm',
          data: null
        });
      })
    );
  }

  unequipItem(slotType: string): Observable<ItemOperationResponse> {
    const url = this.urlService.buildUrl(`${this.API_BASE}/unequip`);
    const body = { SlotType: slotType };
    return this.http.post<ItemOperationResponse>(url, body).pipe(
      tap((response) => {
        if (response.success) {
          // Refresh equipped items after successful unequip
          this.loadUserEquippedItems();
        }
      }),
      catchError(error => {
        console.error('Error unequipping item:', error);
        return of({
          success: false,
          message: 'Có lỗi xảy ra khi gỡ trang bị',
          data: null
        });
      })
    );
  }

  // Utility methods
  refreshInventory(): void {
    this.loadUserInventory();
  }

  refreshEquippedItems(): void {
    this.loadUserEquippedItems();
  }

  refreshCurrency(): void {
    this.loadUserCurrency();
  }

  refreshAll(): void {
    this.initializeData();
  }

  // Get current state values
  getCurrentInventory(): UserInventoryItem[] {
    return this.inventorySubject.value;
  }

  getCurrentEquippedItems(): UserEquippedItem[] {
    return this.equippedItemsSubject.value;
  }

  getCurrentCurrency(): Record<string, number> {
    return this.currencySubject.value;
  }

  getCurrentItemTemplates(): ItemTemplate[] {
    return this.itemTemplatesSubject.value;
  }

  // Helper methods
  getItemTemplateById(id: number): ItemTemplate | undefined {
    return this.getCurrentItemTemplates().find(template => template.id === id);
  }

  getInventoryItemByTemplateId(templateId: number): UserInventoryItem | undefined {
    return this.getCurrentInventory().find(item => item.itemTemplateId === templateId);
  }

  getEquippedItemBySlot(slotType: string): UserEquippedItem | undefined {
    return this.getCurrentEquippedItems().find(item => item.slotType === slotType);
  }

  isItemEquipped(templateId: number): boolean {
    return this.getCurrentEquippedItems().some(item => item.itemTemplateId === templateId);
  }

  canUseItem(templateId: number, quantity: number = 1): boolean {
    const inventoryItem = this.getInventoryItemByTemplateId(templateId);
    return inventoryItem ? inventoryItem.quantity >= quantity : false;
  }
}
