import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, map, tap, catchError, of, delay } from 'rxjs';
import { UrlService } from '@services/url.service';
import { IServiceResponse } from '@schema';
import {
  ItemTemplate,
  UserInventoryItem,
  UserEquippedItem,
  InventoryPage,
  ItemOperationResponse,
  UseItemRequest,
  EquipItemRequest,
  UnequipItemRequest,
  GiveItemRequest,
  ItemCategory,
  ItemRarity,
  InventoryFilter
} from '../interfaces/item.interface';

@Injectable({
  providedIn: 'root'
})
export class ItemService {
  private readonly API_BASE = '/item';
  private readonly USE_MOCK_DATA = true; // Set to false when backend is ready

  // State management
  private inventorySubject = new BehaviorSubject<UserInventoryItem[]>([]);
  private equippedItemsSubject = new BehaviorSubject<UserEquippedItem[]>([]);
  private itemTemplatesSubject = new BehaviorSubject<ItemTemplate[]>([]);

  // Public observables
  public inventory$ = this.inventorySubject.asObservable();
  public equippedItems$ = this.equippedItemsSubject.asObservable();
  public itemTemplates$ = this.itemTemplatesSubject.asObservable();

  // Mock data
  private mockItemTemplates: ItemTemplate[] = [];
  private mockInventoryItems: UserInventoryItem[] = [];
  private mockEquippedItems: UserEquippedItem[] = [];

  constructor(
    private http: HttpClient,
    private urlService: UrlService
  ) {
    this.initializeMockData();
  }

  // Initialize mock data
  private initializeMockData(): void {
    // Mock Item Templates
    this.mockItemTemplates = [
      // Currency
      {
        id: 1,
        name: 'Xu',
        description: 'Tiền tệ cơ bản trong game',
        icon: '/assets/icons/coin.svg',
        category: ItemCategory.CURRENCY,
        rarity: ItemRarity.COMMON,
        isActive: true
      },
      {
        id: 2,
        name: 'Kim cương',
        description: 'Tiền tệ cao cấp',
        icon: '/assets/icons/diamond.svg',
        category: ItemCategory.CURRENCY,
        rarity: ItemRarity.RARE,
        isActive: true
      },
      // Experience
      {
        id: 3,
        name: 'Điểm kinh nghiệm',
        description: 'Tăng level cho nhân vật',
        icon: '/assets/icons/exp.svg',
        category: ItemCategory.EXPERIENCE,
        rarity: ItemRarity.COMMON,
        isActive: true
      },
      // Badges
      {
        id: 4,
        name: 'Huy hiệu Độc giả',
        description: 'Huy hiệu cho độc giả tích cực',
        icon: '/assets/icons/reader-badge.svg',
        category: ItemCategory.BADGE,
        rarity: ItemRarity.UNCOMMON,
        isActive: true
      },
      {
        id: 5,
        name: 'Huy hiệu Bình luận viên',
        description: 'Huy hiệu cho người hay bình luận',
        icon: '/assets/icons/comment-badge.svg',
        category: ItemCategory.BADGE,
        rarity: ItemRarity.RARE,
        isActive: true
      },
      {
        id: 6,
        name: 'Huy hiệu Khám phá',
        description: 'Huy hiệu cho người khám phá nhiều truyện',
        icon: '/assets/icons/explorer-badge.svg',
        category: ItemCategory.BADGE,
        rarity: ItemRarity.EPIC,
        isActive: true
      },
      // Avatar Frames
      {
        id: 7,
        name: 'Khung Avatar Đồng',
        description: 'Khung avatar màu đồng',
        icon: '/assets/icons/bronze-frame.svg',
        category: ItemCategory.AVATAR_FRAME,
        rarity: ItemRarity.COMMON,
        isActive: true
      },
      {
        id: 8,
        name: 'Khung Avatar Bạc',
        description: 'Khung avatar màu bạc',
        icon: '/assets/icons/silver-frame.svg',
        category: ItemCategory.AVATAR_FRAME,
        rarity: ItemRarity.UNCOMMON,
        isActive: true
      },
      {
        id: 9,
        name: 'Khung Avatar Vàng',
        description: 'Khung avatar màu vàng',
        icon: '/assets/icons/gold-frame.svg',
        category: ItemCategory.AVATAR_FRAME,
        rarity: ItemRarity.RARE,
        isActive: true
      },
      {
        id: 10,
        name: 'Khung Avatar Huyền thoại',
        description: 'Khung avatar huyền thoại với hiệu ứng đặc biệt',
        icon: '/assets/icons/legendary-frame.svg',
        category: ItemCategory.AVATAR_FRAME,
        rarity: ItemRarity.LEGENDARY,
        isActive: true
      },
      // Titles
      {
        id: 11,
        name: 'Độc giả mới',
        description: 'Danh hiệu cho người mới',
        icon: '/assets/icons/newbie-title.svg',
        category: ItemCategory.TITLE,
        rarity: ItemRarity.COMMON,
        isActive: true
      },
      {
        id: 12,
        name: 'Độc giả chăm chỉ',
        description: 'Đọc truyện thường xuyên',
        icon: '/assets/icons/diligent-reader.svg',
        category: ItemCategory.TITLE,
        rarity: ItemRarity.UNCOMMON,
        isActive: true
      },
      {
        id: 13,
        name: 'Bậc thầy bình luận',
        description: 'Viết bình luận hay',
        icon: '/assets/icons/comment-master.svg',
        category: ItemCategory.TITLE,
        rarity: ItemRarity.RARE,
        isActive: true
      },
      {
        id: 14,
        name: 'Nhà khám phá',
        description: 'Khám phá nhiều truyện mới',
        icon: '/assets/icons/explorer-title.svg',
        category: ItemCategory.TITLE,
        rarity: ItemRarity.EPIC,
        isActive: true
      },
      {
        id: 15,
        name: 'Huyền thoại',
        description: 'Danh hiệu cao nhất',
        icon: '/assets/icons/legend-title.svg',
        category: ItemCategory.TITLE,
        rarity: ItemRarity.LEGENDARY,
        isActive: true
      },
      // Consumables
      {
        id: 16,
        name: 'Thuốc tăng EXP',
        description: 'Tăng 100% EXP trong 1 giờ',
        icon: '/assets/icons/exp-potion.svg',
        category: ItemCategory.CONSUMABLE,
        rarity: ItemRarity.UNCOMMON,
        isActive: true
      },
      {
        id: 17,
        name: 'Thuốc tăng EXP cấp cao',
        description: 'Tăng 200% EXP trong 2 giờ',
        icon: '/assets/icons/exp-potion-high.svg',
        category: ItemCategory.CONSUMABLE,
        rarity: ItemRarity.RARE,
        isActive: true
      },
      // Premium
      {
        id: 18,
        name: 'Premium 1 ngày',
        description: 'Tài khoản premium 1 ngày',
        icon: '/assets/icons/premium-1d.svg',
        category: ItemCategory.PREMIUM,
        rarity: ItemRarity.UNCOMMON,
        isActive: true
      },
      {
        id: 19,
        name: 'Premium 7 ngày',
        description: 'Tài khoản premium 7 ngày',
        icon: '/assets/icons/premium-7d.svg',
        category: ItemCategory.PREMIUM,
        rarity: ItemRarity.RARE,
        isActive: true
      },
      {
        id: 20,
        name: 'Premium 30 ngày',
        description: 'Tài khoản premium 30 ngày',
        icon: '/assets/icons/premium-30d.svg',
        category: ItemCategory.PREMIUM,
        rarity: ItemRarity.EPIC,
        isActive: true
      }
    ];

    // Mock User Inventory
    this.mockInventoryItems = [
      {
        id: 1,
        userId: 1,
        itemTemplateId: 1,
        quantity: 1250,
        obtainedAt: new Date('2024-01-15'),
        itemTemplate: this.mockItemTemplates[0]
      },
      {
        id: 2,
        userId: 1,
        itemTemplateId: 2,
        quantity: 45,
        obtainedAt: new Date('2024-01-20'),
        itemTemplate: this.mockItemTemplates[1]
      },
      {
        id: 3,
        userId: 1,
        itemTemplateId: 3,
        quantity: 2500,
        obtainedAt: new Date('2024-01-10'),
        itemTemplate: this.mockItemTemplates[2]
      },
      {
        id: 4,
        userId: 1,
        itemTemplateId: 4,
        quantity: 1,
        obtainedAt: new Date('2024-01-25'),
        itemTemplate: this.mockItemTemplates[3]
      },
      {
        id: 5,
        userId: 1,
        itemTemplateId: 5,
        quantity: 1,
        obtainedAt: new Date('2024-02-01'),
        itemTemplate: this.mockItemTemplates[4]
      },
      {
        id: 6,
        userId: 1,
        itemTemplateId: 7,
        quantity: 1,
        obtainedAt: new Date('2024-01-05'),
        itemTemplate: this.mockItemTemplates[6]
      },
      {
        id: 7,
        userId: 1,
        itemTemplateId: 8,
        quantity: 1,
        obtainedAt: new Date('2024-01-30'),
        itemTemplate: this.mockItemTemplates[7]
      },
      {
        id: 8,
        userId: 1,
        itemTemplateId: 9,
        quantity: 1,
        obtainedAt: new Date('2024-02-05'),
        itemTemplate: this.mockItemTemplates[8]
      },
      {
        id: 9,
        userId: 1,
        itemTemplateId: 11,
        quantity: 1,
        obtainedAt: new Date('2024-01-01'),
        itemTemplate: this.mockItemTemplates[10]
      },
      {
        id: 10,
        userId: 1,
        itemTemplateId: 12,
        quantity: 1,
        obtainedAt: new Date('2024-01-15'),
        itemTemplate: this.mockItemTemplates[11]
      },
      {
        id: 11,
        userId: 1,
        itemTemplateId: 16,
        quantity: 5,
        obtainedAt: new Date('2024-02-01'),
        expiresAt: new Date('2024-03-01'),
        itemTemplate: this.mockItemTemplates[15]
      },
      {
        id: 12,
        userId: 1,
        itemTemplateId: 17,
        quantity: 2,
        obtainedAt: new Date('2024-02-05'),
        expiresAt: new Date('2024-03-05'),
        itemTemplate: this.mockItemTemplates[16]
      },
      {
        id: 13,
        userId: 1,
        itemTemplateId: 18,
        quantity: 3,
        obtainedAt: new Date('2024-02-10'),
        expiresAt: new Date('2024-02-25'),
        itemTemplate: this.mockItemTemplates[17]
      }
    ];

    // Mock Equipped Items
    this.mockEquippedItems = [
      {
        userId: 1,
        itemTemplateId: 8,
        slotType: 'avatar_frame' as any,
        equippedAt: new Date('2024-02-01'),
        itemTemplate: this.mockItemTemplates[7]
      },
      {
        userId: 1,
        itemTemplateId: 12,
        slotType: 'title' as any,
        equippedAt: new Date('2024-02-01'),
        itemTemplate: this.mockItemTemplates[11]
      },
      {
        userId: 1,
        itemTemplateId: 4,
        slotType: 'badge' as any,
        equippedAt: new Date('2024-02-01'),
        itemTemplate: this.mockItemTemplates[3]
      }
    ];

    // Initialize subjects with mock data
    this.itemTemplatesSubject.next(this.mockItemTemplates);
    this.inventorySubject.next(this.mockInventoryItems);
    this.equippedItemsSubject.next(this.mockEquippedItems);
  }

  // Item Templates
  getItemTemplates(category?: ItemCategory): Observable<ItemTemplate[]> {
    if (this.USE_MOCK_DATA) {
      let filteredTemplates = this.mockItemTemplates;
      if (category) {
        filteredTemplates = this.mockItemTemplates.filter(t => t.category === category);
      }
      return of(filteredTemplates).pipe(
        delay(500), // Simulate network delay
        tap(templates => this.itemTemplatesSubject.next(templates))
      );
    }

    let params = new HttpParams();
    if (category) {
      params = params.set('category', category);
    }

    return this.http.get<IServiceResponse<ItemTemplate[]>>(
      `${this.urlService.API_URL}${this.API_BASE}/templates`,
      { params }
    ).pipe(
      map(response => response.data || []),
      tap(templates => this.itemTemplatesSubject.next(templates)),
      catchError(error => {
        console.error('Error fetching item templates:', error);
        return of([]);
      })
    );
  }

  getItemTemplate(templateId: number): Observable<ItemTemplate | null> {
    if (this.USE_MOCK_DATA) {
      const template = this.mockItemTemplates.find(t => t.id === templateId);
      return of(template || null).pipe(delay(300));
    }

    return this.http.get<IServiceResponse<ItemTemplate>>(
      `${this.urlService.API_URL}${this.API_BASE}/templates/${templateId}`
    ).pipe(
      map(response => response.data || null),
      catchError(error => {
        console.error('Error fetching item template:', error);
        return of(null);
      })
    );
  }

  getItemTemplatesByRarity(rarity: ItemRarity): Observable<ItemTemplate[]> {
    if (this.USE_MOCK_DATA) {
      const filteredTemplates = this.mockItemTemplates.filter(t => t.rarity === rarity);
      return of(filteredTemplates).pipe(delay(300));
    }

    return this.http.get<IServiceResponse<ItemTemplate[]>>(
      `${this.urlService.API_URL}${this.API_BASE}/templates/rarity/${rarity}`
    ).pipe(
      map(response => response.data || []),
      catchError(error => {
        console.error('Error fetching item templates by rarity:', error);
        return of([]);
      })
    );
  }

  // User Inventory
  getUserInventory(
    page: number = 1,
    pageSize: number = 20,
    filter?: InventoryFilter
  ): Observable<InventoryPage> {
    if (this.USE_MOCK_DATA) {
      let filteredItems = [...this.mockInventoryItems];

      // Apply filters
      if (filter?.category) {
        filteredItems = filteredItems.filter(item => item.itemTemplate.category === filter.category);
      }
      if (filter?.rarity) {
        filteredItems = filteredItems.filter(item => item.itemTemplate.rarity === filter.rarity);
      }
      if (filter?.searchTerm) {
        const searchTerm = filter.searchTerm.toLowerCase();
        filteredItems = filteredItems.filter(item =>
          item.itemTemplate.name.toLowerCase().includes(searchTerm) ||
          item.itemTemplate.description?.toLowerCase().includes(searchTerm)
        );
      }

      // Apply sorting
      if (filter?.sortBy) {
        filteredItems.sort((a, b) => {
          let comparison = 0;
          switch (filter.sortBy) {
            case 'name':
              comparison = a.itemTemplate.name.localeCompare(b.itemTemplate.name);
              break;
            case 'rarity':
              const rarityOrder = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
              comparison = rarityOrder.indexOf(a.itemTemplate.rarity) - rarityOrder.indexOf(b.itemTemplate.rarity);
              break;
            case 'quantity':
              comparison = a.quantity - b.quantity;
              break;
            case 'obtainedAt':
              comparison = new Date(a.obtainedAt).getTime() - new Date(b.obtainedAt).getTime();
              break;
            case 'category':
              comparison = a.itemTemplate.category.localeCompare(b.itemTemplate.category);
              break;
          }
          return filter.sortOrder === 'desc' ? -comparison : comparison;
        });
      }

      // Apply pagination
      const totalItems = filteredItems.length;
      const totalPages = Math.ceil(totalItems / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedItems = filteredItems.slice(startIndex, endIndex);

      const result: InventoryPage = {
        items: paginatedItems,
        totalItems,
        currentPage: page,
        totalPages
      };

      return of(result).pipe(
        delay(800), // Simulate network delay
        tap(inventory => this.inventorySubject.next(inventory.items))
      );
    }

    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());

    if (filter?.category) {
      params = params.set('category', filter.category);
    }

    return this.http.get<IServiceResponse<InventoryPage>>(
      `${this.urlService.API_URL}${this.API_BASE}/inventory`,
      { params }
    ).pipe(
      map(response => response.data || { items: [], totalItems: 0, currentPage: 1, totalPages: 1 }),
      tap(inventory => this.inventorySubject.next(inventory.items)),
      catchError(error => {
        console.error('Error fetching user inventory:', error);
        return of({ items: [], totalItems: 0, currentPage: 1, totalPages: 1 });
      })
    );
  }

  getUserInventoryItem(itemTemplateId: number): Observable<UserInventoryItem | null> {
    if (this.USE_MOCK_DATA) {
      const item = this.mockInventoryItems.find(i => i.itemTemplateId === itemTemplateId);
      return of(item || null).pipe(delay(300));
    }

    return this.http.get<IServiceResponse<UserInventoryItem>>(
      `${this.urlService.API_URL}${this.API_BASE}/inventory/${itemTemplateId}`
    ).pipe(
      map(response => response.data || null),
      catchError(error => {
        console.error('Error fetching inventory item:', error);
        return of(null);
      })
    );
  }

  getUserInventoryByCategory(category: ItemCategory): Observable<UserInventoryItem[]> {
    if (this.USE_MOCK_DATA) {
      const filteredItems = this.mockInventoryItems.filter(i => i.itemTemplate.category === category);
      return of(filteredItems).pipe(delay(300));
    }

    return this.http.get<IServiceResponse<UserInventoryItem[]>>(
      `${this.urlService.API_URL}${this.API_BASE}/inventory/category/${category}`
    ).pipe(
      map(response => response.data || []),
      catchError(error => {
        console.error('Error fetching inventory by category:', error);
        return of([]);
      })
    );
  }

  // Item Operations
  useItem(request: UseItemRequest): Observable<ItemOperationResponse> {
    if (this.USE_MOCK_DATA) {
      const itemIndex = this.mockInventoryItems.findIndex(i => i.itemTemplateId === request.itemTemplateId);

      if (itemIndex === -1) {
        return of({ success: false, message: 'Item not found in inventory' }).pipe(delay(500));
      }

      const item = this.mockInventoryItems[itemIndex];

      if (item.quantity < request.quantity) {
        return of({
          success: false,
          message: `Not enough items. You have ${item.quantity}, need ${request.quantity}`
        }).pipe(delay(500));
      }

      // Update quantity
      item.quantity -= request.quantity;

      // Remove item if quantity reaches 0
      if (item.quantity <= 0) {
        this.mockInventoryItems.splice(itemIndex, 1);
      }

      // Update subjects
      this.inventorySubject.next([...this.mockInventoryItems]);

      return of({
        success: true,
        message: `Successfully used ${request.quantity} ${item.itemTemplate.name}`,
        updatedItem: item.quantity > 0 ? item : undefined
      }).pipe(delay(500));
    }

    return this.http.post<IServiceResponse<ItemOperationResponse>>(
      `${this.urlService.API_URL}${this.API_BASE}/use`,
      request
    ).pipe(
      map(response => response.data || { success: false, message: 'Unknown error' }),
      tap(result => {
        if (result.success) {
          this.refreshInventory();
        }
      }),
      catchError(error => {
        console.error('Error using item:', error);
        return of({ success: false, message: 'Error using item' });
      })
    );
  }

  equipItem(request: EquipItemRequest): Observable<ItemOperationResponse> {
    if (this.USE_MOCK_DATA) {
      const inventoryItem = this.mockInventoryItems.find(i => i.itemTemplateId === request.itemTemplateId);

      if (!inventoryItem) {
        return of({ success: false, message: 'Item not found in inventory' }).pipe(delay(500));
      }

      const itemTemplate = inventoryItem.itemTemplate;

      // Check if there's already an item equipped in this slot
      const existingEquippedIndex = this.mockEquippedItems.findIndex(e => e.slotType === request.slotType);

      if (existingEquippedIndex !== -1) {
        // Unequip existing item first
        this.mockEquippedItems.splice(existingEquippedIndex, 1);
      }

      // Equip new item
      const newEquippedItem: UserEquippedItem = {
        userId: 1,
        itemTemplateId: request.itemTemplateId,
        slotType: request.slotType as any,
        equippedAt: new Date(),
        itemTemplate: itemTemplate
      };

      this.mockEquippedItems.push(newEquippedItem);

      // Update subjects
      this.equippedItemsSubject.next([...this.mockEquippedItems]);
      this.inventorySubject.next([...this.mockInventoryItems]);

      return of({
        success: true,
        message: `Successfully equipped ${itemTemplate.name}`
      }).pipe(delay(500));
    }

    return this.http.post<IServiceResponse<ItemOperationResponse>>(
      `${this.urlService.API_URL}${this.API_BASE}/equip`,
      request
    ).pipe(
      map(response => response.data || { success: false, message: 'Unknown error' }),
      tap(result => {
        if (result.success) {
          this.refreshEquippedItems();
          this.refreshInventory();
        }
      }),
      catchError(error => {
        console.error('Error equipping item:', error);
        return of({ success: false, message: 'Error equipping item' });
      })
    );
  }

  unequipItem(request: UnequipItemRequest): Observable<ItemOperationResponse> {
    if (this.USE_MOCK_DATA) {
      const equippedItemIndex = this.mockEquippedItems.findIndex(e => e.slotType === request.slotType);

      if (equippedItemIndex === -1) {
        return of({ success: false, message: 'No item equipped in this slot' }).pipe(delay(500));
      }

      const equippedItem = this.mockEquippedItems[equippedItemIndex];

      // Remove from equipped items
      this.mockEquippedItems.splice(equippedItemIndex, 1);

      // Update subjects
      this.equippedItemsSubject.next([...this.mockEquippedItems]);
      this.inventorySubject.next([...this.mockInventoryItems]);

      return of({
        success: true,
        message: `Successfully unequipped ${equippedItem.itemTemplate.name}`
      }).pipe(delay(500));
    }

    return this.http.post<IServiceResponse<ItemOperationResponse>>(
      `${this.urlService.API_URL}${this.API_BASE}/unequip`,
      request
    ).pipe(
      map(response => response.data || { success: false, message: 'Unknown error' }),
      tap(result => {
        if (result.success) {
          this.refreshEquippedItems();
          this.refreshInventory();
        }
      }),
      catchError(error => {
        console.error('Error unequipping item:', error);
        return of({ success: false, message: 'Error unequipping item' });
      })
    );
  }

  // Equipped Items
  getUserEquippedItems(): Observable<UserEquippedItem[]> {
    if (this.USE_MOCK_DATA) {
      return of([...this.mockEquippedItems]).pipe(
        delay(400),
        tap(equippedItems => this.equippedItemsSubject.next(equippedItems))
      );
    }

    return this.http.get<IServiceResponse<UserEquippedItem[]>>(
      `${this.urlService.API_URL}${this.API_BASE}/equipped`
    ).pipe(
      map(response => response.data || []),
      tap(equippedItems => this.equippedItemsSubject.next(equippedItems)),
      catchError(error => {
        console.error('Error fetching equipped items:', error);
        return of([]);
      })
    );
  }

  getUserEquippedItemBySlot(slotType: string): Observable<UserEquippedItem | null> {
    if (this.USE_MOCK_DATA) {
      const equippedItem = this.mockEquippedItems.find(e => e.slotType === slotType);
      return of(equippedItem || null).pipe(delay(300));
    }

    return this.http.get<IServiceResponse<UserEquippedItem>>(
      `${this.urlService.API_URL}${this.API_BASE}/equipped/${slotType}`
    ).pipe(
      map(response => response.data || null),
      catchError(error => {
        console.error('Error fetching equipped item by slot:', error);
        return of(null);
      })
    );
  }

  // Admin Operations (if user has admin role)
  giveItemToUser(request: GiveItemRequest): Observable<ItemOperationResponse> {
    if (this.USE_MOCK_DATA) {
      const itemTemplate = this.mockItemTemplates.find(t => t.id === request.itemTemplateId);

      if (!itemTemplate) {
        return of({ success: false, message: 'Item template not found' }).pipe(delay(500));
      }

      // Check if user already has this item
      const existingItemIndex = this.mockInventoryItems.findIndex(
        i => i.userId === request.userId && i.itemTemplateId === request.itemTemplateId
      );

      if (existingItemIndex !== -1) {
        // Add to existing quantity
        this.mockInventoryItems[existingItemIndex].quantity += request.quantity;
      } else {
        // Create new inventory item
        const newItem: UserInventoryItem = {
          id: this.mockInventoryItems.length + 1,
          userId: request.userId,
          itemTemplateId: request.itemTemplateId,
          quantity: request.quantity,
          obtainedAt: new Date(),
          itemTemplate: itemTemplate
        };
        this.mockInventoryItems.push(newItem);
      }

      // Update subjects
      this.inventorySubject.next([...this.mockInventoryItems]);

      return of({
        success: true,
        message: `Successfully gave ${request.quantity} ${itemTemplate.name} to user`
      }).pipe(delay(500));
    }

    return this.http.post<IServiceResponse<ItemOperationResponse>>(
      `${this.urlService.API_URL}${this.API_BASE}/admin/give`,
      request
    ).pipe(
      map(response => response.data || { success: false, message: 'Unknown error' }),
      catchError(error => {
        console.error('Error giving item to user:', error);
        return of({ success: false, message: 'Error giving item to user' });
      })
    );
  }

  // Utility Methods
  private refreshInventory(): void {
    if (!this.USE_MOCK_DATA) {
      this.getUserInventory().subscribe();
    }
  }

  private refreshEquippedItems(): void {
    if (!this.USE_MOCK_DATA) {
      this.getUserEquippedItems().subscribe();
    }
  }

  // Get current state
  getCurrentInventory(): UserInventoryItem[] {
    return this.inventorySubject.value;
  }

  getCurrentEquippedItems(): UserEquippedItem[] {
    return this.equippedItemsSubject.value;
  }

  getCurrentItemTemplates(): ItemTemplate[] {
    return this.itemTemplatesSubject.value;
  }

  // Initialize data
  initializeItemData(): void {
    if (this.USE_MOCK_DATA) {
      // Data is already initialized in constructor
      return;
    }

    this.getItemTemplates().subscribe();
    this.getUserInventory().subscribe();
    this.getUserEquippedItems().subscribe();
  }

  // Method to add mock items for testing (can be called from console)
  addMockItemToInventory(itemTemplateId: number, quantity: number = 1): void {
    if (!this.USE_MOCK_DATA) return;

    const itemTemplate = this.mockItemTemplates.find(t => t.id === itemTemplateId);
    if (!itemTemplate) {
      console.error('Item template not found:', itemTemplateId);
      return;
    }

    const existingItemIndex = this.mockInventoryItems.findIndex(i => i.itemTemplateId === itemTemplateId);

    if (existingItemIndex !== -1) {
      this.mockInventoryItems[existingItemIndex].quantity += quantity;
    } else {
      const newItem: UserInventoryItem = {
        id: this.mockInventoryItems.length + 1,
        userId: 1,
        itemTemplateId: itemTemplateId,
        quantity: quantity,
        obtainedAt: new Date(),
        itemTemplate: itemTemplate
      };
      this.mockInventoryItems.push(newItem);
    }

    this.inventorySubject.next([...this.mockInventoryItems]);
    console.log(`Added ${quantity} ${itemTemplate.name} to inventory`);
  }

  // Method to clear all equipped items for testing
  clearAllEquippedItems(): void {
    if (!this.USE_MOCK_DATA) return;

    this.mockEquippedItems.length = 0;
    this.equippedItemsSubject.next([...this.mockEquippedItems]);
    console.log('Cleared all equipped items');
  }
}
