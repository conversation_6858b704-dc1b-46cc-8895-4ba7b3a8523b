//backup
PGPASSWORD=1hai3bon5sau! pg_dump -U metruyenmoi -h *************** -p 5432 -F c -b -v -f "lastest1.backup" comicapp
PGPASSWORD=1hai3bon5sau! pg_dump -U metruyenmoi -h *************** -p 5432 -F c -b -v -f "lastest2.backup" warehouse

//restore

PGPASSWORD=comicapp@123! psql -U postgres -h localhost -p 5432 -d postgres -c "DROP DATABASE IF EXISTS warehouse;"
PGPASSWORD=comicapp@123! psql -U postgres -h localhost -p 5432 -d postgres -c "CREATE DATABASE warehouse;"
PGPASSWORD=comicapp@123! pg_restore -U postgres -h localhost -p 5432 -d warehouse -v lastest2.backup




PGPASSWORD=comicapp@123! psql -U postgres -h localhost -p 5432 -d postgres -c "DROP DATABASE IF EXISTS comicapp;"
PGPASSWORD=comicapp@123! psql -U postgres -h localhost -p 5432 -d postgres -c "CREATE DATABASE comicapp;"
PGPASSWORD=comicapp@123! pg_restore -U postgres -h localhost -p 5432 -d comicapp -v lastest1.backup

