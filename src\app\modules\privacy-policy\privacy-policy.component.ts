import { Component } from '@angular/core';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

@Component({
    selector: 'app-privacy-policy',
    templateUrl: './privacy-policy.component.html',
    styleUrl: './privacy-policy.component.scss',

    standalone: false
})
export class PrivacyPolicyComponent {
    host = '';
    constructor(private urlService: UrlService, private seoService: SeoService,
    ) {
        this.host = this.urlService.BASE_URL;
        this.seoService.setTitle('Chính sách bảo mật');
        this.seoService.addTags([
            { name: 'description', content: 'Chính sách bảo mật của MeTruyenMoi' },
            { name: 'keywords', content: 'Chính sách bảo mật, bảo mật, quyền riêng tư, thông tin cá nhân' },
            { property: 'og:description', content: '<PERSON><PERSON>h sách bảo mật của MeTruyenMoi' },
            { property: 'og:title', content: 'Chính sách bảo mật' },
            { property: 'og:url', content: `${this.host}/chinh-sach-bao-mat` },
            { property: 'og:type', content: 'website' },
            { property: 'og:site_name', content: 'MeTruyenMoi' },
            { itemprop: 'name', content: 'Chính sách bảo mật' },
            { itemprop: 'description', content: 'Chính sách bảo mật của MeTruyenMoi' },
        ]);
        this.seoService.updateLink('canonical', `${this.host}/chinh-sach-bao-mat`);
    }
}
