import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, Output, ChangeDetectorRef, Inject, PLATFORM_ID } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic } from '@schema';
import { OptimizedBaseComponent } from '../../base/optimized-base.component';

@Component({
    selector: 'app-comic-card',
    templateUrl: './comic-card.component.html',
    styleUrl: './comic-card.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule, RouterLink, NumeralPipe, DateAgoPipe],
})
export class ComicCardComponent extends OptimizedBaseComponent implements OnD<PERSON>roy {
  @Input() comic?: Comic;
  @Input() eventName?: string;
  @Output() comicHover = new EventEmitter<Comic | undefined>();
  @Output() clickEvent = new EventEmitter<number>();

  // Computed properties for better performance
  get comicRouterLink(): string[] {
    return this.comic ? ['/truyen-tranh', `${this.comic.url}-${this.comic.id}`] : [];
  }

  get chapterRouterLink(): string[] {
    if (!this.comic?.chapters?.[0]) return [];
    return ['/truyen-tranh', this.comic.url, this.comic.chapters[0].id.toString()];
  }

  get hasChapters(): boolean {
    return !!(this.comic?.chapters?.length);
  }

  get authorName(): string {
    return this.comic?.author ?? 'Đang cập nhật';
  }

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
  }

  onHoverComic = (hover: boolean): void => {
    this.comicHover.emit(hover && this.comic ? this.comic : undefined);
  };

  onClick = (): void => {
    if (this.comic?.id) {
      this.clickEvent.emit(this.comic.id);
    }
  };

  override ngOnDestroy(): void {
    this.comicHover.emit(undefined);
    super.ngOnDestroy();
  }
}
