<div class="comic-container">
  <div class="comic-header">
    <h2 class="comic-title"><PERSON><PERSON><PERSON>ần <PERSON></h2>
    <div class="comic-view-more">
      <a [routerLink]="['/lich-su']" class="comic-view-more-link">Xem thêm</a>
      <svg
        class="comic-view-more-icon"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <polyline points="13 17 18 12 13 7" />
        <polyline points="6 17 11 12 6 7" />
      </svg>
    </div>
  </div>

  <div *ngFor="let comic of listComics" class="comic-item">
    <div class="comic-item-content">
      <img
        loading="lazy"
        class="comic-item-image"
        [src]="comic.coverImage"
        [alt]="comic.title"
        loading="lazy"
        onerror="this.src='/option2.png'"
      />
      <div class="comic-item-info">
        <div class="comic-item-info-content">
          <span class="comic-item-title">
            <a
              [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
              class="comic-item-title-link"
              >{{ comic.title }}</a
            >
          </span>

          <span class="comic-item-chapter">
            <a
              [routerLink]="['/truyen-tranh', comic.url, comic.chapters![0].id]"
              *ngIf="comic.chapters && comic.chapters.length > 0"
              class="comic-item-chapter-link"
              >{{ comic.chapters[0].title }}</a
            >
          </span>
          <div class="flex justify-between mb-1">
            <div
              class="text-sm text-center flex items-center text-orange-400 gap-1"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="none"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                class="feather feather-star icon rel"
                viewBox="0 0 24 24"
                style="color: currentcolor"
              >
                <path
                  d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01z"
                ></path>
              </svg>
              <p>{{ comic.rating }}</p>
            </div>
            <div class="text-xs my-auto text-end text-gray-500">
              {{ comic.updateAt | dateAgo }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
