import { 
  Component, 
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ItemService } from '../../services/item.service';
import { ItemCardComponent } from '../item-card/item-card.component';
import { 
  UserEquippedItem,
  ItemDisplayInfo,
  ItemActionType,
  EquipmentSlotType,
  ITEM_CATEGORY_INFO,
  ITEM_RARITY_INFO
} from '../../interfaces/item.interface';

@Component({
  selector: 'app-equipped-items',
  standalone: true,
  imports: [CommonModule, RouterLink, ItemCardComponent],
  templateUrl: './equipped-items.component.html',
  styleUrl: './equipped-items.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EquippedItemsComponent extends OptimizedBaseComponent implements OnInit {
  // Signals for reactive state
  private readonly equippedItemsSignal = signal<UserEquippedItem[]>([]);
  private readonly isLoadingSignal = signal<boolean>(false);

  // Computed properties
  readonly equippedItems = computed(() => this.equippedItemsSignal());
  readonly isLoading = computed(() => this.isLoadingSignal());

  // Constants for templates
  readonly EquipmentSlotType = EquipmentSlotType;
  readonly ItemActionType = ItemActionType;
  readonly ITEM_CATEGORY_INFO = ITEM_CATEGORY_INFO;
  readonly ITEM_RARITY_INFO = ITEM_RARITY_INFO;

  // Equipment slots configuration
  readonly equipmentSlots = [
    {
      slotType: EquipmentSlotType.AVATAR_FRAME,
      label: 'Khung Avatar',
      icon: 'frame',
      description: 'Khung trang trí cho avatar'
    },
    {
      slotType: EquipmentSlotType.TITLE,
      label: 'Danh hiệu',
      icon: 'crown',
      description: 'Danh hiệu hiển thị'
    },
    {
      slotType: EquipmentSlotType.BADGE,
      label: 'Huy hiệu',
      icon: 'shield',
      description: 'Huy hiệu thành tích'
    },
    {
      slotType: EquipmentSlotType.BACKGROUND,
      label: 'Nền',
      icon: 'image',
      description: 'Hình nền profile'
    }
  ];

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private itemService: ItemService
  ) {
    super(cdr, platformId);
  }

  ngOnInit(): void {
    this.loadEquippedItems();
    this.setupSubscriptions();
  }

  // Computed getters
  get equippedItemsBySlot(): Map<EquipmentSlotType, UserEquippedItem | null> {
    const itemsMap = new Map<EquipmentSlotType, UserEquippedItem | null>();
    
    // Initialize all slots as empty
    Object.values(EquipmentSlotType).forEach(slotType => {
      itemsMap.set(slotType, null);
    });

    // Fill with equipped items
    this.equippedItems().forEach(item => {
      itemsMap.set(item.slotType, item);
    });

    return itemsMap;
  }

  // Event handlers
  onItemAction(event: { action: ItemActionType; item: ItemDisplayInfo }): void {
    const { action, item } = event;

    switch (action) {
      case ItemActionType.UNEQUIP:
        this.unequipItem(item);
        break;
      case ItemActionType.VIEW_DETAILS:
        this.viewItemDetails(item);
        break;
    }
  }

  onSlotClick(slotType: EquipmentSlotType): void {
    // Open inventory filtered by compatible items for this slot
    console.log('Open inventory for slot:', slotType);
  }

  // Actions
  private async unequipItem(item: ItemDisplayInfo): Promise<void> {
    const slotType = this.getSlotTypeFromItem(item);
    if (!slotType) return;

    this.isLoadingSignal.set(true);

    try {
      const result = await this.itemService.unequipItem({
        slotType
      }).toPromise();

      if (result?.success) {
        this.loadEquippedItems();
        // Show success message
      } else {
        // Show error message
      }
    } catch (error) {
      console.error('Error unequipping item:', error);
      // Show error message
    } finally {
      this.isLoadingSignal.set(false);
    }
  }

  private viewItemDetails(item: ItemDisplayInfo): void {
    // Open item details modal/popup
    console.log('View item details:', item);
  }

  // Data loading
  private loadEquippedItems(): void {
    this.isLoadingSignal.set(true);

    this.addSubscription(
      this.itemService.getUserEquippedItems()
        .subscribe({
          next: (equippedItems) => {
            this.equippedItemsSignal.set(equippedItems);
            this.isLoadingSignal.set(false);
            this.safeMarkForCheck();
          },
          error: (error) => {
            console.error('Error loading equipped items:', error);
            this.isLoadingSignal.set(false);
            this.safeMarkForCheck();
          }
        })
    );
  }

  private setupSubscriptions(): void {
    // Subscribe to equipped items updates
    this.addSubscription(
      this.itemService.equippedItems$.subscribe(items => {
        this.equippedItemsSignal.set(items);
        this.safeMarkForCheck();
      })
    );
  }

  // Utility methods
  getEquippedItemForSlot(slotType: EquipmentSlotType): UserEquippedItem | null {
    return this.equippedItemsBySlot.get(slotType) || null;
  }

  getDisplayInfoForEquippedItem(equippedItem: UserEquippedItem): ItemDisplayInfo {
    return {
      template: equippedItem.itemTemplate,
      quantity: 1,
      isEquipped: true,
      canUse: false,
      canEquip: false
    };
  }

  private getSlotTypeFromItem(item: ItemDisplayInfo): EquipmentSlotType | null {
    // This should match the logic in the inventory component
    switch (item.template.category) {
      case 'avatar_frame':
        return EquipmentSlotType.AVATAR_FRAME;
      case 'title':
        return EquipmentSlotType.TITLE;
      case 'badge':
        return EquipmentSlotType.BADGE;
      default:
        return null;
    }
  }

  // TrackBy functions
  trackBySlotType = (_index: number, slot: any): EquipmentSlotType => {
    return slot.slotType;
  };
}
