.pagination-container {
    @apply flex flex-col items-center justify-between border-t dark:border-neutral-700 px-4 mt-4 py-3 sm:px-6;
  }
  .pagination-nav {
    @apply isolate inline-flex -space-x-px rounded-md gap-1;
  }
  .pagination-prev {
    @apply cursor-pointer relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 hover:bg-gray-200 focus:z-20 focus:outline-offset-0;
  }
  .pagination-icon {
    @apply h-5 w-5;
  }
  .pagination-page {
    @apply relative rounded-md dark:border-neutral-500 items-center text-center min-w-10 py-2 text-sm font-semibold
    text-gray-900 cursor-pointer hover:bg-slate-200 dark:hover:bg-neutral-700 dark:text-white;
  }
  .pagination-page.active {
    @apply bg-primary-100 text-white;
  }
  .pagination-next {
    @apply cursor-pointer relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 hover:bg-gray-200 focus:z-20 focus:outline-offset-0;
  }
  .pagination-search {
    @apply relative flex gap-2 items-center justify-center mt-2;
  }
  .pagination-search-label {
    @apply text-end text-gray-500 font-medium text-sm;
  }
  .pagination-search-input {
    @apply text-center w-20 outline-none focus:outline-2 focus:outline-primary-100 rounded-full p-1.5 pl-0 z-10 text-sm text-gray-900 bg-gray-50 border dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white;
  }
  .pagination-search-button {
    @apply py-1 px-3.5 h-full text-sm font-medium text-white rounded-full bg-primary-100 focus:outline-none focus:ring-4 dark:bg-primary-200 dark:hover:bg-primary-200 dark:focus:ring-primary-200;
  }
  .pagination-search-icon {
    @apply h-6 w-6;
  }