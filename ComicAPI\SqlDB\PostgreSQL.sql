--DROP DATABASE IF EXISTS ComicApp;
CREATE DATABASE ComicApp;

\c ComicApp;

CREATE TABLE _USER (
    ID SERIAL PRIMARY KEY,
    -- Username VARCHAR(50) UNIQUE,
    Email VARCHAR(100) UNIQUE,
    HashPassword VARCHAR(255),
    FirstName VARCHAR(50),
    LastName VARCHAR(50),
    DOB TIMESTAMP,
    Avatar VARCHAR(255),
    Gender INT CHECK (Gender IN (0, 1)) DEFAULT 0,
    Status INT CHECK (Status IN (0, 1)) DEFAULT 1,
    LastLogin TIMESTAMP,
    LastLoginIp VARCHAR(50),
    CreateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    Role INT NOT NULL CHECK (Role IN (0, 1, 2)),
    Experience INT DEFAULT 0,
    Maxim TEXT,
    TypeLevel INT DEFAULT 0
    --MOCK DATA
    -- INSERT INTO _USER (Userna<PERSON>, <PERSON>ail, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>O<PERSON>, <PERSON>tar, Gender, Status, Role)
    -- VALUES
    -- ('johndoe', '<EMAIL>', 'John', 'Doe', '1990-01-01 00:00:00', 'avatar.jpg', 0, 1, 0),
    -- ('janedoe', '<EMAIL>', 'Jane', 'Doe', '1995-02-15 00:00:00', 'avatar2.png', 1, 1, 1),
    -- ('coderDude', '<EMAIL>', 'Alex', 'Wang', '1985-12-24 00:00:00', 'avatar3.gif', 0, 1, 2),
    -- ('bookworm', '<EMAIL>', 'Alice', 'Green', '2000-06-09 00:00:00', NULL, 1, 0, 0),
    -- ('musicLover', '<EMAIL>', 'Ben', 'Garcia', '1978-03-18 00:00:00', 'avatar5.jpg', 0, 1, 1);
);

CREATE TABLE COMIC (
    ID SERIAL PRIMARY KEY,
    Title VARCHAR(255),
    OtherName VARCHAR(255),
    URL VARCHAR(255) NOT NULL UNIQUE,
    Author VARCHAR(100),
    Description TEXT,
    CoverImage VARCHAR(255),
    Status INT NOT NULL CHECK (status IN (0, 1)),
    Rating NUMERIC NOT NULL CHECK (Rating <= 10),
    CreateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    viewcount integer DEFAULT 0,
    numchapter integer NOT NULL DEFAULT 0
    lastchapter INT
    FOREIGN KEY (lastchapter) REFERENCES CHAPTER(ID) ON  DELETE SET NULL
);
-- ALTER TABLE comic ADD  FOREIGN KEY (lastchapter) REFERENCES CHAPTER(id) ON  DELETE SET NULL

CREATE TABLE USER_FOLLOW_COMIC (
  UserID INT NOT NULL,
  ComicID INT NOT NULL,  
  CreateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

  PRIMARY KEY (UserID, ComicID),
  FOREIGN KEY (UserID) REFERENCES _USER(ID),  
  FOREIGN KEY (ComicID) REFERENCES COMIC(ID) 
);

CREATE TABLE USER_VOTE_COMIC (
  UserID INT NOT NULL,
  ComicID INT NOT NULL,
  VotePoint INT NOT NULL CHECK (VotePoint IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)),
  PRIMARY KEY (UserID, ComicID),  
  FOREIGN KEY (UserID) REFERENCES _USER(ID),  
  FOREIGN KEY (ComicID) REFERENCES COMIC(ID)  
);

CREATE TABLE GENRE (
    ID SERIAL PRIMARY KEY,
    Title VARCHAR(255) UNIQUE,
    Slug VARCHAR(255) UNIQUE,
    Description TEXT
);

CREATE TABLE COMIC_GENRE (
    ComicID INT,
    GenreID INT,
    PRIMARY KEY (ComicID, GenreID),
    FOREIGN KEY (ComicID) REFERENCES COMIC(ID) ON DELETE CASCADE;,
    FOREIGN KEY (GenreID) REFERENCES GENRE(ID)
);

CREATE TABLE CHAPTER (
    ID SERIAL PRIMARY KEY,
    ComicID INT,
    Title VARCHAR(255),
    URL REAL,
    ViewCount INT NOT NULL DEFAULT 0,
    UpdateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    Pages TEXT, 
    FOREIGN KEY (ComicID) REFERENCES COMIC(ID) ON DELETE CASCADE;
);

CREATE TABLE PAGE (
  ID SERIAL PRIMARY KEY,
  ChapterID INT NOT NULL,
  PageNumber INT NOT NULL,
  Content TEXT,  -- Description of the page content (optional)
  URL VARCHAR(255),
  ImageURL VARCHAR(255),  -- URL for the page image
  FOREIGN KEY (ChapterID) REFERENCES CHAPTER(ID)
);
CREATE TABLE DAILY_COMIC_VIEWS (
    ComicID INT NOT NULL,
    ViewDate DATE NOT NULL,
    ViewCount INT NOT NULL DEFAULT 0,
    PRIMARY KEY (ComicID, ViewDate),
    FOREIGN KEY (ComicID) REFERENCES COMIC(ID)
);
-- SELECT ComicID, ViewDate, ViewCount
-- FROM DAILY_COMIC_VIEWS
-- WHERE ViewDate = CURRENT_DATE;
-- SELECT ComicID, DATE_TRUNC('week', ViewDate) as ViewWeek, SUM(ViewCount) as ViewCount
-- FROM DAILY_COMIC_VIEWS
-- WHERE ViewDate >= CURRENT_DATE - INTERVAL '1 week'
-- GROUP BY ComicID, DATE_TRUNC('week', ViewDate);
-- SELECT ComicID, DATE_TRUNC('month', ViewDate) as ViewMonth, SUM(ViewCount) as ViewCount
-- FROM DAILY_COMIC_VIEWS
-- WHERE ViewDate >= CURRENT_DATE - INTERVAL '1 month'
-- GROUP BY ComicID, DATE_TRUNC('month', ViewDate);

CREATE TABLE COMMENT (
    ID SERIAL PRIMARY KEY,
    ChapterID INT NOT NULL,
    ComicID INT NOT NULL,
    UserID INT NOT NULL ,
    Content TEXT,
    CommentedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ParentCommentID INT DEFAULT NULL,
    ReplyUserID INT DEFAULT NULL,
    FOREIGN KEY (ChapterID) REFERENCES CHAPTER(ID) ON DELETE CASCADE,
    FOREIGN KEY (UserID) REFERENCES _USER(ID),
    FOREIGN KEY (ParentCommentID) REFERENCES COMMENT(ID),
    FOREIGN KEY (ComicID) REFERENCES COMIC(ID)
    );


CREATE TABLE IF NOT EXISTS hostcollector
(
    id int NOT NULL,
    host VARCHAR(255),
    comic_format VARCHAR(255),
    chapter_format VARCHAR(255),
    CONSTRAINT hostcollector_pkey PRIMARY KEY (id)
)

CREATE TABLE IF NOT EXISTS chaptermapping
(
    comic_slug VARCHAR(255)  NOT NULL,
    chapter_slug VARCHAR(255)  NOT NULL,
    chapterid VARCHAR(255) ,
    comicid VARCHAR(255) ,
    hostid integer,
    CONSTRAINT chaptermapping_pkey PRIMARY KEY (comic_slug, chapter_slug),
    CONSTRAINT fk_hostid FOREIGN KEY (hostid) REFERENCES hostcollector (id)
)
-- Create the USER NOTIFICATION table
CREATE TABLE IF NOT EXISTS USER_NOTIFICATION (
    UserID INT NOT NULL,
    NtfID INT NOT NULL,
    IsRead BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (UserID, NtfID),
    FOREIGN KEY (UserID) REFERENCES _USER(ID),
    FOREIGN KEY (NtfID) REFERENCES NOTIFICATION(ID) ON DELETE CASCADE;
);

CREATE TABLE IF NOT EXISTS NOTIFICATION (
    ID SERIAL PRIMARY KEY,
    Message TEXT,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Type INT default 0,
    Params JSONB
);

CREATE TABLE IF NOT EXISTS NEW_COMIC_RAW (
    URL VARCHAR(255) PRIMARY KEY,
    DATA TEXT,
    STATUS INT,
    CREATEDAT TIMESTAMP
);

-- Quest System Tables
CREATE TABLE IF NOT EXISTS QUEST_TEMPLATE (
    ID SERIAL PRIMARY KEY,
    Title VARCHAR(255) NOT NULL,
    Description TEXT,
    QuestType VARCHAR(50) NOT NULL, -- 'read_chapters', 'write_comments', etc.
    Target INT NOT NULL,
    Difficulty VARCHAR(20) NOT NULL CHECK (Difficulty IN ('easy', 'medium', 'hard', 'legendary')),
    Icon VARCHAR(50),
    IsDaily BOOLEAN NOT NULL DEFAULT TRUE,
    IsWeekly BOOLEAN NOT NULL DEFAULT FALSE,
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    CreateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS QUEST_REWARD (
    ID SERIAL PRIMARY KEY,
    QuestTemplateID INT NOT NULL,
    RewardType VARCHAR(50) NOT NULL, -- 'coins', 'experience', 'badge', etc.
    Amount INT NOT NULL,
    Item VARCHAR(100), -- For badge, avatar_frame, title, etc.
    Description VARCHAR(255),
    FOREIGN KEY (QuestTemplateID) REFERENCES QUEST_TEMPLATE(ID) ON DELETE CASCADE
);

  
CREATE TABLE IF NOT EXISTS USER_QUEST (
    ID SERIAL PRIMARY KEY,
    UserID INT NOT NULL,
    QuestTemplateID INT NOT NULL,
    Current INT NOT NULL DEFAULT 0,
    Status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (Status IN ('active', 'completed', 'expired', 'locked')),
    ExpiresAt TIMESTAMP NOT NULL,
    CompletedAt TIMESTAMP,
    CreateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    WeekNumber INT, -- For weekly quests
    ResetDay VARCHAR(10), -- For weekly quests
    FOREIGN KEY (UserID) REFERENCES _USER(ID) ON DELETE CASCADE,
    FOREIGN KEY (QuestTemplateID) REFERENCES QUEST_TEMPLATE(ID) ON DELETE CASCADE,
    UNIQUE(UserID, QuestTemplateID, CreateAt::DATE) -- Prevent duplicate daily quests
);

CREATE TABLE IF NOT EXISTS USER_QUEST_PROGRESS (
    ID SERIAL PRIMARY KEY,
    UserQuestID INT NOT NULL,
    Progress INT NOT NULL DEFAULT 0,
    LastUpdated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (UserQuestID) REFERENCES USER_QUEST(ID) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS USER_REWARD_INVENTORY (
    ID SERIAL PRIMARY KEY,
    UserID INT NOT NULL,
    RewardType VARCHAR(50) NOT NULL,
    Amount INT NOT NULL DEFAULT 0,
    Item VARCHAR(100),
    EarnedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    QuestID INT,
    FOREIGN KEY (UserID) REFERENCES _USER(ID) ON DELETE CASCADE,
    FOREIGN KEY (QuestID) REFERENCES USER_QUEST(ID) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS USER_QUEST_STATS (
    UserID INT PRIMARY KEY,
    TotalCompleted INT NOT NULL DEFAULT 0,
    StreakDays INT NOT NULL DEFAULT 0,
    WeeklyCompleted INT NOT NULL DEFAULT 0,
    MonthlyCompleted INT NOT NULL DEFAULT 0,
    TotalRewardsEarned INT NOT NULL DEFAULT 0,
    Level INT NOT NULL DEFAULT 1,
    Experience INT NOT NULL DEFAULT 0,
    NextLevelExp INT NOT NULL DEFAULT 100,
    LastLoginDate DATE,
    UpdateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (UserID) REFERENCES _USER(ID) ON DELETE CASCADE
);

-- Item System Tables (Simplified)
CREATE TABLE IF NOT EXISTS ITEM_TEMPLATE (
    ID SERIAL PRIMARY KEY,
    Name VARCHAR(255) NOT NULL,
    Description TEXT,
    Icon VARCHAR(100),
    Category VARCHAR(50) NOT NULL, -- 'currency', 'experience', 'badge', 'avatar_frame', 'title', 'premium'
    Rarity VARCHAR(20) NOT NULL DEFAULT 'common' CHECK (Rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    IsActive BOOLEAN NOT NULL DEFAULT TRUE,
    CreateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdateAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS USER_INVENTORY (
    ID SERIAL PRIMARY KEY,
    UserID INT NOT NULL,
    ItemTemplateID INT NOT NULL,
    Quantity INT NOT NULL DEFAULT 1,
    ObtainedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ExpiresAt TIMESTAMP,
    Metadata JSONB, -- For storing additional item data like premium days remaining, etc.
    FOREIGN KEY (UserID) REFERENCES _USER(ID) ON DELETE CASCADE,
    FOREIGN KEY (ItemTemplateID) REFERENCES ITEM_TEMPLATE(ID) ON DELETE CASCADE,
    UNIQUE(UserID, ItemTemplateID) -- Ensure one record per user per item
);

CREATE TABLE IF NOT EXISTS USER_EQUIPPED_ITEMS (
    UserID INT NOT NULL,
    ItemTemplateID INT NOT NULL,
    SlotType VARCHAR(50) NOT NULL, -- 'avatar_frame', 'title', 'badge'
    EquippedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (UserID, SlotType),
    FOREIGN KEY (UserID) REFERENCES _USER(ID) ON DELETE CASCADE,
    FOREIGN KEY (ItemTemplateID) REFERENCES ITEM_TEMPLATE(ID) ON DELETE CASCADE
);

CREATE INDEX idx_user_username ON _USER (Username);
CREATE INDEX idx_user_email ON _USER (Email);
CREATE INDEX idx_user_role ON _USER (Role);

CREATE INDEX idx_COMIC_title ON COMIC (Title);
CREATE INDEX idx_COMIC_status ON COMIC (Status);
CREATE INDEX idx_COMIC_rating ON COMIC (Rating);

CREATE INDEX idx_genre_title ON GENRE (Title);
CREATE INDEX idx_genre_slug ON GENRE (Slug);

CREATE INDEX idx_comic_id ON CHAPTER (ComicID);
CREATE INDEX idx_chapter_number ON CHAPTER (url);

CREATE INDEX idx_comic_genre_comic_id ON COMIC_GENRE (ComicID);
CREATE INDEX idx_comic_genre_genre_id ON COMIC_GENRE (GenreID);

CREATE INDEX idx_chapter_id ON PAGE (ChapterID);

CREATE INDEX idx_notifications_comic_id ON USER_NOTIFICATION (UserID);
CREATE INDEX idx_notifications_genre_id ON USER_NOTIFICATION (ComicID);

CREATE INDEX idx_vote_comic_id ON USER_VOTE_COMIC (UserID);
CREATE INDEX idx_vote_genre_id ON USER_VOTE_COMIC (ComicID);

-- Quest System Indexes
CREATE INDEX idx_quest_template_type ON QUEST_TEMPLATE (QuestType);
CREATE INDEX idx_quest_template_daily ON QUEST_TEMPLATE (IsDaily);
CREATE INDEX idx_quest_template_weekly ON QUEST_TEMPLATE (IsWeekly);
CREATE INDEX idx_quest_template_active ON QUEST_TEMPLATE (IsActive);

CREATE INDEX idx_quest_reward_template ON QUEST_REWARD (QuestTemplateID);
CREATE INDEX idx_quest_reward_type ON QUEST_REWARD (RewardType);

CREATE INDEX idx_user_quest_user ON USER_QUEST (UserID);
CREATE INDEX idx_user_quest_template ON USER_QUEST (QuestTemplateID);
CREATE INDEX idx_user_quest_status ON USER_QUEST (Status);
CREATE INDEX idx_user_quest_expires ON USER_QUEST (ExpiresAt);
CREATE INDEX idx_user_quest_date ON USER_QUEST (UserID, CreateAt);

CREATE INDEX idx_user_quest_progress_quest ON USER_QUEST_PROGRESS (UserQuestID);
CREATE INDEX idx_user_quest_progress_updated ON USER_QUEST_PROGRESS (LastUpdated);

CREATE INDEX idx_user_reward_user ON USER_REWARD_INVENTORY (UserID);
CREATE INDEX idx_user_reward_type ON USER_REWARD_INVENTORY (RewardType);
CREATE INDEX idx_user_reward_earned ON USER_REWARD_INVENTORY (EarnedAt);

CREATE INDEX idx_user_quest_stats_level ON USER_QUEST_STATS (Level);
CREATE INDEX idx_user_quest_stats_exp ON USER_QUEST_STATS (Experience);

-- Item System Indexes (Simplified)
CREATE INDEX idx_item_template_category ON ITEM_TEMPLATE (Category);
CREATE INDEX idx_item_template_name ON ITEM_TEMPLATE (Name);
CREATE INDEX idx_item_template_rarity ON ITEM_TEMPLATE (Rarity);
CREATE INDEX idx_item_template_active ON ITEM_TEMPLATE (IsActive);

CREATE INDEX idx_user_inventory_user ON USER_INVENTORY (UserID);
CREATE INDEX idx_user_inventory_item ON USER_INVENTORY (ItemTemplateID);
CREATE INDEX idx_user_inventory_expires ON USER_INVENTORY (ExpiresAt);
CREATE INDEX idx_user_inventory_quantity ON USER_INVENTORY (Quantity);

CREATE INDEX idx_user_equipped_user ON USER_EQUIPPED_ITEMS (UserID);
CREATE INDEX idx_user_equipped_slot ON USER_EQUIPPED_ITEMS (SlotType);


-- INSERT INTO hostcollector
-- (id,host,comic_format,chapter_format)
-- VALUES
-- (1,'nhattruyenss.com','https://{host}/truyen-tranh/{comic-slug}-{comicid}','https://{host}/truyen-tranh/{comic-slug}/{chapter-slug}/{chapterid}');

-- Insert sample quest templates
INSERT INTO QUEST_TEMPLATE (Title, Description, QuestType, Target, Difficulty, Icon, IsDaily, IsWeekly, IsActive) VALUES
('Đọc 3 chapter', 'Đọc ít nhất 3 chapter truyện tranh trong ngày', 'read_chapters', 3, 'easy', 'book-open', TRUE, FALSE, TRUE),
('Bình luận 2 truyện', 'Viết bình luận cho 2 truyện tranh khác nhau', 'write_comments', 2, 'easy', 'message-circle', TRUE, FALSE, TRUE),
('Thêm 1 truyện yêu thích', 'Thêm một truyện tranh vào danh sách yêu thích', 'add_favorites', 1, 'easy', 'heart', TRUE, FALSE, TRUE),
('Đánh giá 3 truyện', 'Đánh giá sao cho 3 truyện tranh', 'rate_comics', 3, 'medium', 'star', TRUE, FALSE, TRUE),
('Khám phá truyện mới', 'Đọc 1 chapter của truyện chưa từng đọc', 'discover_new', 1, 'medium', 'compass', TRUE, FALSE, TRUE),
('Đọc 20 chapter trong tuần', 'Hoàn thành 20 chapter trong 7 ngày', 'weekly_reading', 20, 'hard', 'trophy', FALSE, TRUE, TRUE),
('Hoàn thành 1 bộ truyện', 'Đọc hết tất cả chapter của 1 bộ truyện', 'complete_series', 1, 'legendary', 'award', FALSE, TRUE, TRUE);

-- Insert sample item templates (Simplified)
INSERT INTO ITEM_TEMPLATE (Name, Description, Icon, Category, Rarity) VALUES
-- Currency
('Xu', 'Tiền tệ cơ bản trong game', 'coin', 'currency', 'common'),
('Kim cương', 'Tiền tệ cao cấp', 'diamond', 'currency', 'rare'),

-- Experience
('Điểm kinh nghiệm', 'Tăng level cho nhân vật', 'exp', 'experience', 'common'),

-- Badges
('Huy hiệu Độc giả mới', 'Dành cho người mới bắt đầu', 'newbie_badge', 'badge', 'common'),
('Huy hiệu Độc giả tuần', 'Hoàn thành quest tuần', 'weekly_reader_badge', 'badge', 'uncommon'),
('Huy hiệu Bình luận viên', 'Viết nhiều bình luận', 'commenter_badge', 'badge', 'uncommon'),
('Huy hiệu Khám phá', 'Đọc nhiều truyện mới', 'explorer_badge', 'badge', 'rare'),
('Huy hiệu Huyền thoại', 'Thành tích đặc biệt', 'legendary_badge', 'badge', 'legendary'),

-- Avatar Frames
('Khung đồng', 'Khung avatar màu đồng', 'bronze_frame', 'avatar_frame', 'common'),
('Khung bạc', 'Khung avatar màu bạc', 'silver_frame', 'avatar_frame', 'uncommon'),
('Khung vàng', 'Khung avatar màu vàng', 'gold_frame', 'avatar_frame', 'rare'),
('Khung kim cương', 'Khung avatar kim cương', 'diamond_frame', 'avatar_frame', 'epic'),
('Khung huyền thoại', 'Khung avatar huyền thoại', 'legendary_frame', 'avatar_frame', 'legendary'),

-- Titles
('Độc giả mới', 'Danh hiệu cho người mới', 'newbie_title', 'title', 'common'),
('Độc giả chăm chỉ', 'Đọc truyện thường xuyên', 'diligent_reader', 'title', 'uncommon'),
('Bậc thầy bình luận', 'Viết bình luận hay', 'comment_master', 'title', 'rare'),
('Nhà khám phá', 'Khám phá nhiều truyện mới', 'explorer_title', 'title', 'epic'),
('Huyền thoại', 'Danh hiệu cao nhất', 'legend_title', 'title', 'legendary'),

-- Premium
('Premium 1 ngày', 'Tài khoản premium 1 ngày', 'premium_1d', 'premium', 'uncommon'),
('Premium 7 ngày', 'Tài khoản premium 7 ngày', 'premium_7d', 'premium', 'rare'),
('Premium 30 ngày', 'Tài khoản premium 30 ngày', 'premium_30d', 'premium', 'epic');

-- Update quest rewards to use items with quantities
DELETE FROM QUEST_REWARD;
INSERT INTO QUEST_REWARD (QuestTemplateID, RewardType, Amount, Item, Description) VALUES
(1, 'item', 50, '1', '50 xu'), -- 50 coins (quantity 50)
(2, 'item', 100, '3', '100 EXP'), -- 100 experience (quantity 100)
(3, 'item', 30, '1', '30 xu'), -- 30 coins (quantity 30)
(4, 'item', 150, '3', '150 EXP'), -- 150 experience (quantity 150)
(5, 'item', 75, '1', '75 xu'), -- 75 coins (quantity 75)
(6, 'item', 1, '5', 'Huy hiệu "Độc giả tuần"'), -- Weekly reader badge (quantity 1)
(7, 'item', 3, '19', '3 ngày Premium'); -- 3 premium days (quantity 3)




