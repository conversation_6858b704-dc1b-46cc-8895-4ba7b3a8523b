
// Redesigned Notification Component - Matching Comic Website Design
.notify-container {
  @apply flex justify-center items-center relative h-8 w-8 cursor-pointer bg-gray-100 dark:bg-neutral-800 rounded-full hover:bg-gray-200 dark:hover:bg-neutral-700 transition-colors duration-200;
}

// Bell Button - Comic Website Style
.notify-bell {
  @apply h-full w-full items-center flex select-none relative justify-center transition-all duration-200 border-none bg-transparent;

  &:hover {
    .notify-bell-icon {
      @apply fill-primary-200;
    }
  }

  &.notify-bell-active {

    .notify-bell-icon {
      @apply fill-primary-100;
    }
  }

}

.notify-bell-wrapper {
  @apply relative flex items-center justify-center;
}

.notify-bell-icon {
  @apply fill-gray-600 dark:fill-gray-300 transition-colors duration-200;
}

.notify-badge {
  @apply absolute z-[50] size-3 -top-2 -right-2 bg-primary-100 text-center font-bold text-white text-[10px] rounded-full flex items-center justify-center transition-all duration-200;

  &.notify-badge-visible {
    @apply opacity-100 scale-100;
  }

  &.notify-badge-hidden {
    @apply opacity-0 scale-75;
  }
}

// Main Panel - Comic Website Design
.notify-panel {
  @apply absolute right-0 max-sm:right-[-54px] top-full mt-3 w-80 bg-white dark:bg-dark-bg border border-gray-200 dark:border-neutral-700 rounded-xl shadow-xl z-50 overflow-hidden;
  max-height: 600px;
}

// Header - Comic Website Style
.notify-header {
  @apply relative bg-gray-50 dark:bg-neutral-800;
}

.notify-header-content {
  @apply p-4 border-b border-gray-200 dark:border-neutral-700 flex items-center justify-between;
}

.notify-title-section {
  @apply flex items-center gap-3;
}

.notify-title-icon {
  @apply w-5 h-5 text-primary-100;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.notify-title {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.notify-count {
  @apply px-2 py-1 bg-primary-100 text-white text-xs font-bold rounded-full;
}

.notify-actions {
  @apply flex items-center gap-2;
}

.notify-action-btn {
  @apply p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border-none cursor-pointer;

  &.active {
    @apply text-primary-100 bg-primary-100/10;
  }
}

.notify-action-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Dropdown Menu - Comic Website Style
.notify-dropdown-menu {
  @apply absolute top-full right-0 mt-2 w-64 bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-lg z-10;
}

.notify-dropdown-arrow {
  @apply absolute -top-2 right-4 w-4 h-4 bg-white dark:bg-neutral-800 border-l border-t border-gray-200 dark:border-neutral-700 transform rotate-45;
}

.notify-dropdown-content {
  @apply p-2;
}

.notify-dropdown-item {
  @apply flex items-center gap-3 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &.danger {
    @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20;
  }

  &:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

.notify-dropdown-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;

  &.success {
    @apply text-green-600 dark:text-green-400;
  }
}

// Filter Tabs - Comic Website Style
.notify-filter-tabs {
  @apply relative bg-white dark:bg-dark-bg;
}

.notify-tab-container {
  @apply flex;
}

.notify-tab {
  @apply flex-1 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white border-none bg-transparent cursor-pointer transition-colors duration-200;

  &.active {
    @apply text-primary-100 bg-gray-50 dark:bg-neutral-800;
  }
}

.notify-tab-text {
  @apply font-medium;
}

.notify-tab-count {
  @apply px-2 py-0.5 bg-gray-100 dark:bg-neutral-700 text-gray-600 dark:text-gray-400 text-xs rounded-full font-medium;

  &.unread {
    @apply bg-primary-100 text-white;
  }
}

.notify-tab-indicator {
  @apply absolute bottom-0 left-0 w-1/2 h-0.5 bg-primary-100 transition-transform duration-300;

  &.unread-active {
    transform: translateX(100%);
  }
}

// Empty State - Comic Website Style
.notify-empty-state {
  @apply p-8 text-center flex justify-center items-center flex-col bg-white dark:bg-dark-bg;
}

.notify-empty-content {
  @apply space-y-4;
}

.notify-empty-illustration {
  @apply flex justify-center;
}

.notify-empty-icon {
  @apply flex size-24 text-gray-300 dark:text-gray-600;
}

.notify-empty-text {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.notify-empty-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.notify-empty-description {
  @apply text-gray-500 dark:text-gray-400 max-w-sm mx-auto;
}

// Notification List - Comic Website Style
.notify-list {
  @apply bg-white dark:bg-dark-bg overflow-y-auto max-h-80;
}

.notify-list-container {
  @apply flex flex-col;
}

.notify-item {
  @apply relative select-none transition-all duration-200 border-b border-gray-100 dark:border-neutral-800 last:border-b-0;

  &.notify-item-unread {
    @apply bg-blue-50/50 dark:bg-blue-900/10;
  }

  &.notify-item-hovered {
    @apply bg-gray-50 dark:bg-neutral-800/50;
  }
}

.notify-unread-indicator {
  @apply absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-12 bg-primary-100 rounded-r;
}

.notify-item-content {
  @apply flex items-center p-3 gap-3 hover:bg-gray-50 dark:hover:bg-neutral-800/50 transition-all duration-200;
}

.notify-item-image {
  @apply cursor-pointer rounded-lg size-12 flex-shrink-0 overflow-hidden;
}

.notify-item-image-src {
  @apply rounded-lg size-12 w-full h-full object-cover border border-gray-200 dark:border-neutral-600 transition-transform duration-200 hover:scale-105;
}

.notify-item-info {
  @apply flex flex-col cursor-pointer transition-colors duration-200 flex-1 min-w-0;

  &.notify-item-info-read {
    @apply opacity-70;
  }
}

.notify-item-info-content {
  @apply text-sm line-clamp-2 font-medium text-gray-900 dark:text-white mb-1;
}

.notify-item-info-timestamp {
  @apply text-xs text-gray-500 dark:text-gray-400 font-medium;

  &.notify-item-info-timestamp-unread {
    @apply text-primary-100 font-semibold;
  }
}

// Option Menu - Comic Website Style
.notify-item-option {
  @apply absolute z-50 top-1/2 -translate-y-1/2 right-3 flex items-center justify-center bg-white dark:bg-neutral-700 w-8 h-8 rounded-full border border-gray-200 dark:border-neutral-600 hover:cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-600 transition-all duration-200 shadow-sm;

  &:hover {
    @apply scale-105 shadow-md;
  }
}

.notify-item-option-icon {
  @apply w-4 h-4 text-gray-500 dark:text-gray-400;
}

.notify-item-option-dropdown {
  @apply absolute z-[100] right-[40px] top-8 bg-white dark:bg-neutral-800 shadow-xl w-[200px] p-2 rounded-lg border border-gray-200 dark:border-neutral-700;
}

.notify-item-option-dropdown-icon {
  @apply z-[200] absolute top-[-9px] right-[8px] w-4 h-4 fill-white dark:fill-neutral-800;
}

.notify-item-option-dropdown-item {
  @apply flex items-center p-2 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-colors duration-200 cursor-pointer;
}

.notify-item-option-dropdown-item-icon {
  @apply w-4 h-4 flex-shrink-0;
}

.notify-item-option-dropdown-item-content {
  @apply ml-3 text-sm font-medium text-gray-700 dark:text-gray-200;
}

// Loading State - Comic Website Style
.notify-loading {
  @apply flex justify-center items-center p-6 bg-white dark:bg-dark-bg;

  .loading-spinner {
    @apply animate-spin h-6 w-6 text-primary-100;
  }
}

// Error State - Comic Website Style
.notify-error {
  @apply flex flex-col items-center justify-center p-6 text-red-600 dark:text-red-400 bg-white dark:bg-dark-bg;

  .error-icon {
    @apply w-8 h-8 mb-2;
  }

  .error-text {
    @apply text-sm font-medium text-center;
  }
}

// Responsive Design
@media (max-width: 640px) {
  .notify-panel {
    @apply w-72 right-[-32px];
  }

  .notify-header-content {
    @apply p-3;
  }

  .notify-title {
    @apply text-base;
  }

  .notify-item-content {
    @apply p-2;
  }

  .notify-item-image,
  .notify-item-image-src {
    @apply size-10;
  }
}

// Accessibility
.notify-bell:focus,
.notify-action-btn:focus,
.notify-tab:focus,
.notify-dropdown-item:focus {
  @apply outline-none ;
}

// Performance Optimizations
.notify-panel,
.notify-item,
.notify-dropdown-menu {
  transform: translateZ(0);
  backface-visibility: hidden;
}