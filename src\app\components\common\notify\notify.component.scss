
// Notify Container
.notify-container {
  @apply flex justify-center items-center relative h-7 w-7 cursor-pointer bg-neutral-200 dark:bg-neutral-700 rounded-full;
}

// Notify Bell
.notify-bell {
  @apply h-full w-full items-center flex select-none relative justify-center transition-colors duration-150;

  &:hover {
    .notify-bell-icon {
      @apply fill-primary-200;
    }
  }

}

.notify-bell-icon {
  @apply fill-primary-100 transition-colors duration-150;
}

.notify-badge {
  @apply absolute z-[50] size-3 -top-1 -right-1 bg-red-600 text-center font-semibold text-white text-[8px] rounded-full transition-opacity duration-150;

  &.notify-badge-visible {
    @apply opacity-100;
  }

  &.notify-badge-hidden {
    @apply opacity-0;
  }
}

// Notify Panel
.notify-panel {
  @apply flex flex-col absolute dark:bg-neutral-800 pb-2 m-0 border dark:border-gray-700 w-80 max-sm:max-w-[300px] max-[310px]:max-w-[250px] max-h-96 z-50 right-0 top-10 max-sm:right-[-54px] bg-white rounded-lg overflow-hidden shadow-[0px_0px_5px_5px_rgba(0,0,0,0.1)] cursor-default;

  &.notify-panel-visible {
    @apply block;
  }

  &.notify-panel-hidden {
    @apply hidden;
  }
}

// Notify Header
.notify-header {
  @apply relative mt-3 mx-4 flex items-center justify-between border-b dark:border-gray-500 divide-gray-500;
}

.notify-title {
  @apply text-lg dark:text-white font-bold;
}

.notify-option-general {
  @apply hover:cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 px-2 rounded-lg select-none transition-colors duration-150;
}

.notify-option-icon {
  @apply h-5 w-5 text-gray-500;
}

.notify-option-dropdown {
  @apply absolute z-[100] bg-white dark:text-white dark:bg-neutral-700 shadow-lg top-[30px] left-[5%] w-[90%] px-2 py-2 rounded-lg;
}

.notify-option-dropdown-icon {
  @apply z-[200] absolute top-[-10px] fill-white right-[0px] dark:fill-neutral-700;
}

.notify-option-read-all {
  @apply flex flex-row p-2 items-center hover:bg-gray-200 dark:hover:bg-neutral-600 rounded-lg transition-colors duration-150;
}

.notify-option-read-all-icon {
  @apply h-5 w-5 text-sky-500;
}

.notify-option-delete-all {
  @apply flex flex-row items-center p-2 hover:bg-gray-200 dark:hover:bg-neutral-700 rounded-lg transition-colors duration-150;
}

.notify-option-delete-all-icon {
  @apply h-5 w-5 text-red-500;
}

// Notify Options
.notify-option {
  @apply my-1 mx-2 flex items-center dark:text-white;
}

.notify-option-all,
.notify-option-unread {
  @apply text-sm font-semibold mr-3 px-2 py-1 text-gray-400 dark:hover:opacity-70 transition-all duration-150 cursor-pointer;

  &:hover {
    @apply text-gray-600 dark:text-gray-200;
  }

}

.notify-option-active {
  @apply border-b-2 border-primary-100 text-primary-100;
}

// Empty State
.notify-empty-state {
  @apply items-center flex flex-col p-4;
}

.notify-empty-icon {
  @apply w-1/2;
}

.notify-empty-text {
  @apply my-2 font-semibold text-center text-slate-400 dark:text-gray-300;
}

// Notify List
.notify-list {
  @apply mt-2 px-2 overflow-y-scroll max-h-80 no-scrollbar;
}

.notify-list-ul {
  @apply flex flex-col gap-1;
}

.notify-item {
  @apply relative select-none flex flex-col;
}

.notify-item-content {
  @apply flex items-center dark:text-white hover:bg-gray-200 bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 px-2 py-1 rounded-md gap-2 transition-colors duration-150;
}

.notify-item-image {
  @apply cursor-pointer rounded-full size-14 flex-shrink-0;
}

.notify-item-image-src {
  @apply rounded-full size-14 flex-shrink-0 w-full object-cover;
}

.notify-item-info {
  @apply flex flex-col cursor-pointer transition-colors duration-150;

  &.notify-item-info-read {
    @apply text-gray-600 dark:text-gray-400;
  }
}

.notify-item-info-content {
  @apply text-xs line-clamp-3;
}

.notify-item-info-timestamp {
  @apply font-bold text-xs;

  &.notify-item-info-timestamp-unread {
    @apply text-cyan-500;
  }
}

.notify-item-option {
  @apply absolute z-50 top-[25%] right-2 flex items-center justify-between bg-white dark:bg-neutral-600 w-7 h-7 rounded-full border border-gray-200 dark:border-neutral-600 hover:cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-400 transition-all duration-150;

  &:hover {
    @apply scale-110;
  }

}

.notify-item-option-icon {
  @apply m-auto;
}

.notify-item-option-dropdown {
  @apply absolute z-[100] right-[40px] top-10 bg-white dark:bg-neutral-700 drop-shadow-2xl w-[200px] px-2 py-2 rounded-lg;
}

.notify-item-option-dropdown-icon {
  @apply z-[200] absolute top-[-9px] fill-white right-[0px] dark:fill-neutral-700;
}

.notify-item-option-dropdown-item {
  @apply flex flex-row items-center p-2 hover:bg-gray-200 dark:hover:bg-neutral-600 rounded-lg transition-colors duration-150;
}

.notify-item-option-dropdown-item-icon {
  @apply w-4 h-4;
}

.notify-item-option-dropdown-item-content {
  @apply ml-2 dark:text-white;
}




.notify-loading {
  @apply flex justify-center items-center p-4;

  .loading-spinner {
    @apply animate-spin h-6 w-6 text-primary-100;
  }
}

// Error State
.notify-error {
  @apply flex flex-col items-center justify-center p-4 text-red-500 dark:text-red-400;

  .error-icon {
    @apply w-8 h-8 mb-2;
  }

  .error-text {
    @apply text-sm font-medium text-center;
  }
}

