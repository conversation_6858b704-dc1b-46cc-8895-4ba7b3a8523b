using ComicApp.DTOs.Item;
using ComicApp.Reposibility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ComicApp.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ItemController : ControllerBase
    {
        private readonly IItemRepository _itemRepository;
        private readonly ILogger<ItemController> _logger;

        public ItemController(IItemRepository itemRepository, ILogger<ItemController> logger)
        {
            _itemRepository = itemRepository;
            _logger = logger;
        }

        /// <summary>
        /// Get item templates by category
        /// </summary>
        [HttpGet("templates")]
        public async Task<ActionResult<List<ItemTemplateDTO>>> GetItemTemplates([FromQuery] string? category = null)
        {
            try
            {
                var templates = await _itemRepository.GetItemTemplatesAsync(category);
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item templates");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get user inventory with pagination
        /// </summary>
        [HttpGet("inventory")]
        public async Task<ActionResult<InventoryPageDTO>> GetUserInventory(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? category = null)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized("User not found");

                var inventory = await _itemRepository.GetUserInventoryAsync(userId.Value, page, pageSize, category);
                return Ok(inventory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user inventory");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get user inventory by category
        /// </summary>
        [HttpGet("inventory/category/{category}")]
        public async Task<ActionResult<List<UserInventoryDTO>>> GetUserInventoryByCategory(string category)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized("User not found");

                var items = await _itemRepository.GetUserInventoryByCategoryAsync(userId.Value, category);
                return Ok(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user inventory by category");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get user equipped items
        /// </summary>
        [HttpGet("equipped")]
        public async Task<ActionResult<List<UserEquippedItemDTO>>> GetUserEquippedItems()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized("User not found");

                var equippedItems = await _itemRepository.GetUserEquippedItemsAsync(userId.Value);
                return Ok(equippedItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user equipped items");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Use an item from inventory
        /// </summary>
        [HttpPost("use")]
        public async Task<ActionResult<ItemOperationResponse>> UseItem([FromBody] UseItemRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _itemRepository.UseItemAsync(userId.Value, request.ItemTemplateID, request.Quantity);
                
                if (!result.Success)
                    return BadRequest(result);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error using item");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Equip an item
        /// </summary>
        [HttpPost("equip")]
        public async Task<ActionResult<ItemOperationResponse>> EquipItem([FromBody] EquipItemRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _itemRepository.EquipItemAsync(userId.Value, request.ItemTemplateID, request.SlotType);
                
                if (!result.Success)
                    return BadRequest(result);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error equipping item");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Unequip an item
        /// </summary>
        [HttpPost("unequip")]
        public async Task<ActionResult<ItemOperationResponse>> UnequipItem([FromBody] UnequipItemRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized("User not found");

                var result = await _itemRepository.UnequipItemAsync(userId.Value, request.SlotType);
                
                if (!result.Success)
                    return BadRequest(result);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unequipping item");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get user currency (coins, diamonds, etc.)
        /// </summary>
        [HttpGet("currency")]
        public async Task<ActionResult<Dictionary<string, int>>> GetUserCurrency()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                    return Unauthorized("User not found");

                var currency = await _itemRepository.GetUserCurrencyAsync(userId.Value);
                return Ok(currency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user currency");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Admin: Give item to user
        /// </summary>
        [HttpPost("admin/give")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ItemOperationResponse>> GiveItemToUser([FromBody] GiveItemRequest request)
        {
            try
            {
                var result = await _itemRepository.GiveItemToUserAsync(
                    request.UserID,
                    request.ItemTemplateID,
                    request.Quantity,
                    request.Source,
                    request.SourceID);
                
                if (!result.Success)
                    return BadRequest(result);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error giving item to user");
                return StatusCode(500, "Internal server error");
            }
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (int.TryParse(userIdClaim, out int userId))
                return userId;
            return null;
        }
    }
}
