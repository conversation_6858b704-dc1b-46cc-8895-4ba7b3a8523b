using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;
using ComicApp.Models;

namespace ComicAPI.Models
{
    public class Notification
    {
        [Column("id")]
        [Key]
        public int ID { get; set; }

        [Column("message")]
        public string? Message { get; set; }

        [Column("createdat")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("type")]
        public int Type { get; set; } = 0;

        [Column("params")]
        public string? Params { get; set; }

        [Column("isglobal")]
        public bool IsGlobal { get; set; }

        [Column("expires_at")]
        public DateTime? ExpireAt { get; set; }
    }
}