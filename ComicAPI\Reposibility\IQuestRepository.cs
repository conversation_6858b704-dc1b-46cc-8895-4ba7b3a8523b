using ComicApp.DTOs.Quest;
using ComicApp.Models.Quest;

namespace ComicApp.Reposibility
{
    public interface IQuestRepository
    {
        // Daily Quests
        Task<List<DailyQuestDTO>> GetDailyQuestsAsync(int userId);
        Task<List<WeeklyQuestDTO>> GetWeeklyQuestsAsync(int userId);
        Task<UserQuestStatsDTO?> GetUserQuestStatsAsync(int userId);
        
        // Quest Management
        Task<bool> CreateDailyQuestsForUserAsync(int userId);
        Task<bool> CreateWeeklyQuestsForUserAsync(int userId);
        Task<bool> UpdateQuestProgressAsync(int userId, string questId, int progress);
        Task<ClaimRewardResponse> ClaimQuestRewardAsync(int userId, string questId);
        
        // Quest Templates
        Task<List<QuestTemplate>> GetActiveQuestTemplatesAsync(bool isDaily = true);
        Task<QuestTemplate?> GetQuestTemplateAsync(int templateId);
        
        // User Quest Stats
        Task<bool> UpdateUserQuestStatsAsync(int userId, UserQuestStats stats);
        Task<bool> InitializeUserQuestStatsAsync(int userId);
        
        // Progress Tracking
        Task<bool> TrackReadChapterAsync(int userId, int chapterId);
        Task<bool> TrackCommentAsync(int userId, int comicId);
        Task<bool> TrackFavoriteAsync(int userId, int comicId);
        Task<bool> TrackRatingAsync(int userId, int comicId);
        Task<bool> TrackLoginAsync(int userId);
        
        // Utility
        Task<bool> ExpireOldQuestsAsync();
        Task<bool> ResetDailyQuestsAsync();
        Task<bool> ResetWeeklyQuestsAsync();
    }
}
