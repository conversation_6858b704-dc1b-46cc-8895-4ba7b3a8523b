<div class="bg-[#333] text-white flex">
  <div class="md:container w-full mx-auto">
    <app-anouncement></app-anouncement>
  </div>
</div>
<div class="bg-[#333] min-h-screen" #screenContainer>
  <app-breadcrumb
    class="md:container z-10 mx-auto flex text-gray-100 hover:text-white"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      {
        label: comic.title,
        url: '/truyen-tranh/' + comic.url + '-' + comic.id
      },
      { label: mainChapter.title, url: '' }
    ]"
  >
  </app-breadcrumb>
  <div
    #HeaderContainer
    class="mx-auto md:container flex flex-col items-center font-bold text-base static"
  >
    <div
      class="bg-white text-black dark:bg-dark-bg dark:text-white p-2 lg:p-5 w-full rounded-t-lg z-20"
    >
      <div
        class="noti-success flex items-center rounded-lg mt-2 lg:mt-0 mx-1 lg:mx-0"
      >
        <span class="relative flex h-5 w-5">
          <!-- <span
            class="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"
          ></span> -->
          <svg
            class="animate-ping absolute inline-flex h-full w-full rounded-full text-sky-400 opacity-75"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <circle cx="12" cy="12" r="9" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
          <svg
            class="h-5 w-5 text-cyan-700"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <circle cx="12" cy="12" r="9" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
          <!-- <span
            class="relative inline-flex rounded-full h-3 w-3 bg-sky-500"
          ></span> -->
        </span>
        <div class="ml-1 font-medium text-xs dark:text-white">
          <p class="flex items-center">
            <span class="whitespace-nowrap"> Nhấn vào biểu tượng ( </span>
            <b class="flex items-center">
              <svg
                class="h-3 w-3 dark:text-white inline-block"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <circle cx="12" cy="12" r="1" />
                <circle cx="12" cy="5" r="1" />
                <circle cx="12" cy="19" r="1" />
              </svg>
            </b>
            <span class="whitespace-nowrap"
              >) để khám phá các tính năng nâng cao</span
            >
          </p>
        </div>
      </div>
      <div class="mt-6 lg:mt-10 text-center">
        <button
          class="px-2 py-2 rounded-lg flex flex-col text-yellow-400 text-sm mx-auto items-center"
          (click)="reportError()"
        >
          <svg
            class="h-6 w-6 hover:animate-pulse"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
            />
            <line x1="12" y1="9" x2="12" y2="13" />
            <line x1="12" y1="17" x2="12.01" y2="17" />
          </svg>
          <span class="font-bold text-xs">Báo lỗi</span>
        </button>

        <h1 class="text-center font-bold text-xl">
          <a
            [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
            title="{{ comic.title }}"
            class="cursor-pointer text-primary-100 dark:text-primary text-xl font-bold uppercase hover:underline"
            >{{ comic.title }}</a
          >
        </h1>

        <p class="text-sm font-medium">{{ mainChapter.title }}</p>
        <time
          class="text-[0.75rem] font-medium xl:font-light text-gray-500 dark:text-gray-300"
          [dateTime]="mainChapter.updateAt | date : 'yyyy-MM-dd'"
        >
          Đăng lúc: {{ mainChapter.updateAt | date : " dd/MM/yyyy" }}
        </time>
      </div>
      <div
        class="flex flex-wrap items-center justify-center mt-4 font-medium text-xs gap-2"
      >
        <button
          *ngFor="
            let serverId of showAllServers
              ? listChapterServerIds
              : (listChapterServerIds ?? []).slice(0, 3);
            let i = index
          "
          (click)="changeServer(serverId, i)"
          [ngClass]="{
            'text-cyan-500': i === selectedServerId,
            'text-gray-400': i !== selectedServerId
          }"
          class="px-4 py-2 rounded-lg transition bg-gray-100 dark:bg-neutral-800 p-1 flex gap-2 items-center"
        >
          <svg
            class="size-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path
              d="M7 18a4.6 4.4 0 0 1 0 -9h0a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"
            />
          </svg>
          Server {{ i + 1 }}
        </button>
        <button
          (click)="showMoreServer()"
          *ngIf="listChapterServerIds && listChapterServerIds.length > 3"
          class="py-2 rounded-lg transition bg-gray-100 dark:bg-neutral-800 p-1 flex gap-2 items-center"
        >
          <svg
            class="size-6"
            [ngClass]="{
              'rotate-0 ': showAllServers,
              'rotate-180': !showAllServers
            }"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path d="M18 15l-6-6l-6 6h12" />
          </svg>
        </button>
      </div>
      <div class="flex flex-wrap gap-2 w-full">
        <div class="flex flex-wrap gap-2 w-full"></div>
      </div>

      <div id="position-btn"></div>
    </div>
    <div
      #controlBar
      appFadeIn
      [duration]="1000"
      class="relative flex flex-row justify-center h-12 w-full items-center z-10 bg-white"
    >
      <div
        *ngIf="isBrowser"
        class="flex justify-center w-full bg-white dark:bg-dark-bg z-20 items-center h-full py-2 lg:px-0 gap-1 xl:gap-2"
      >
        <a href="" title="Trang chủ"
          ><svg
            class="h-7 w-7 text-secondary-100 hover:text-primary-100 dark:stroke-white dark:hover:stroke-primary-100"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <polyline points="5 12 3 12 12 3 21 12 19 12" />
            <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
            <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />
          </svg>
        </a>
        <!-- <div class="p-1"> -->

        <button
          title="Full Screen"
          class="p-1 rounded"
          (click)="enterFullscreen()"
        >
          <svg
            class="h-6 w-6 text-black hover:text-primary-100 dark:stroke-white dark:hover:stroke-primary-100"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
            />
          </svg>
        </button>

        <!-- </div> -->
        <div id="select-chapter-dropdown" class="flex">
          <button
            class="chapter-navigation"
            (click)="navigateChapter(true)"
            aria-label="Previous chapter"
            [disabled]="isLoading"
            [ngClass]="{
              'text-white !bg-primary-100':
                listChapters.length > 0 &&
                mainChapter.slug !==
                  listChapters![listChapters!.length - 1].slug
            }"
          >
            <svg
              class="h-8 w-8 text-white"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" />
              <polyline points="15 6 9 12 15 18" />
            </svg>
          </button>

          <app-chapter-selector
            class="mx-1 lg:mx-2"
            [chapters]="listChapters"
            [mainChapter]="mainChapter"
            [topToBottom]="TopToBottom"
            (chapterChange)="OnChangeChapter($event)"
          >
          </app-chapter-selector>

          <button
            class="chapter-navigation"
            (click)="navigateChapter(false)"
            aria-label="Next chapter"
            [disabled]="isLoading"
            [ngClass]="{
              'text-white !bg-primary-100':
                listChapters.length > 0 &&
                mainChapter.slug !== listChapters![0].slug
            }"
          >
            <svg
              class="h-8 w-8 text-white"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <polyline points="9 18 15 12 9 6" />
            </svg>
          </button>
        </div>

        <button title="Zoom In" class="p-1 rounded flex group">
          @if (!isLimitZoom) {
          <svg
            (click)="ZoomImage(true)"
            class="h-6 w-6 text-black hover:text-primary-100 relative dark:stroke-white dark:hover:stroke-primary-100"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="11" cy="11" r="8" />
            <line x1="21" y1="21" x2="16.65" y2="16.65" />
            <line x1="11" y1="8" x2="11" y2="14" />
            <line x1="8" y1="11" x2="14" y2="11" />
          </svg>
          } @else {
          <svg
            (click)="ZoomImage(false)"
            class="h-6 w-6 text-black hover:text-primary-100 relative dark:stroke-white dark:hover:stroke-primary-100"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="11" cy="11" r="8" />
            <line x1="21" y1="21" x2="16.65" y2="16.65" />
            <line x1="8" y1="11" x2="14" y2="11" />
          </svg>
          }

          <!-- <span>100%</span> -->
          <div
            id="zoom-progress"
            class="absolute hidden group-focus-within:flex bg-white dark:bg-neutral-800 rounded shadow-sm mt-10 p-2 space-x-2 items-center -translate-x-3/4 lg:translate-x-0 min-w-48"
          >
            <div class="flex items-center gap-4">
              <p class="text-secondary-100 dark:text-white text-sm font-light">
                {{ getZoomPercentage() }}%
              </p>
              <div class="flex gap-2">
                <svg
                  class="h-4 w-4 text-secondary-100 hover:text-primary-100 font-bold dark:stroke-white dark:hover:stroke-primary-100"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  (click)="ZoomImage(false)"
                >
                  <line x1="5" y1="12" x2="19" y2="12" />
                </svg>
                <svg
                  class="h-4 w-4 text-secondary-100 hover:text-primary-100 dark:stroke-white dark:hover:stroke-primary-100"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  (click)="ZoomImage(true)"
                >
                  <path stroke="none" d="M0 0h24v24H0z" />
                  <line x1="12" y1="5" x2="12" y2="19" />
                  <line x1="5" y1="12" x2="19" y2="12" />
                </svg>
              </div>
            </div>

            <button
              (click)="resetView()"
              title="Reset View"
              class="mr-2 p-1 rounded-lg flex border-primary-100 border text-sm hover:opacity-80"
            >
              <svg
                class="h-4 w-4 text-primary-100"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>

              <span class="ml-2 text-primary-100">Đặt lại</span>
            </button>
          </div>
        </button>

        <button
          (click)="ToggleMenu(!isToggle)"
          title="More Settings"
          #ToggleMenuButton
        >
          <svg
            class="size-7 dark:text-white hover:text-primary-100"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <circle cx="12" cy="12" r="3" />
          </svg>
        </button>
      </div>

      <div
        *ngIf="isBrowser"
        #MenuNavigation
        class="absolute -z-10 bottom-0 transition-transform px-2.5 py-2 w-full bg-white dark:bg-dark-bg dark:show-neutral-700 shadow-slate-300 dark:text-white"
      >
        <div
          class="grid grid-cols-2 lg:grid-cols-4 gap-2 items-center justify-center lg:w-[55%] mx-auto"
        >
          <button
            class="rounded-lg p-3 min-h-[2.5rem] w-full"
            (click)="changeDirectionReading(true)"
            [ngClass]="{
              'hover:bg-gray-100 dark:hover:bg-neutral-600': !isVertical,
              'bg-slate-100 dark:bg-neutral-700 text-primary-100': isVertical
            }"
          >
            <span class="font-medium select-none pointer-events-none">
              <p class="text-[0.75rem] font-light text-start">Đọc dọc</p>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
                class="icon mr-4"
                style="color: currentcolor"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"
                ></path>
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 2v7h7"
                ></path>
              </svg>
            </span>
          </button>
          <button
            class="rounded-lg p-3 min-h-[2.5rem] w-full"
            (click)="changeDirectionReading(false)"
            [ngClass]="{
              'hover:bg-gray-100 dark:hover:bg-neutral-600': isVertical,
              'bg-slate-100 dark:bg-neutral-700 text-primary-100': !isVertical
            }"
          >
            <span class="font-medium select-none pointer-events-none">
              <p class="text-[0.75rem] font-light text-start">Đọc ngang</p>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
                class="icon mr-4"
                style="color: currentcolor"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M22 20h-6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h6M2 4h6a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2"
                ></path>
              </svg>
            </span>
          </button>
          <label
            class="cursor-pointer hover:bg-slate-100 dark:hover:bg-neutral-700 p-3 rounded-lg"
            [ngClass]="{
              'bg-slate-100 dark:bg-neutral-700  text-primary-100':
                isAutoNextChapter
            }"
          >
            <p
              class="text-gray-900 dark:text-gray-300 text-[0.75rem] font-light text-start leading-snug mb-1"
            >
              Tự động chuyển chương
            </p>
            <input
              type="checkbox"
              value=""
              class="sr-only peer"
              (change)="onCheckboxChange($event)"
            />
            <div
              class="relative w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-50 rounded-full peer dark:bg-neutral-600 peer-checked:after:translate-x-6 rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-100"
            ></div>
          </label>
          <button
            (click)="enableNightLight(!isNightMode)"
            class="rounded-lg p-3 hover:bg-slate-100 dark:hover:bg-neutral-700 min-h-[2.5rem] w-full"
            [ngClass]="{
              'bg-slate-100 dark:bg-neutral-700 text-primary-100': isNightMode
            }"
          >
            <span class="font-medium select-none pointer-events-none">
              <p class="text-[0.75rem] font-light text-start">Chế độ ban đêm</p>
              <svg
                class="h-6 w-6"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" />
                <circle cx="12" cy="12" r="3" />
                <line x1="12" y1="5" x2="12" y2="3" />
                <line x1="17" y1="7" x2="18.4" y2="5.6" />
                <line x1="19" y1="12" x2="21" y2="12" />
                <line x1="17" y1="17" x2="18.4" y2="18.4" />
                <line x1="12" y1="19" x2="12" y2="21" />
                <line x1="7" y1="17" x2="5.6" y2="18.4" />
                <line x1="6" y1="12" x2="4" y2="12" />
                <line x1="7" y1="7" x2="5.6" y2="5.6" />
              </svg>
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>

  @if(isBrowser && !isLoading) {
  <div class="my-2 gap-2 flex flex-col">
    <app-banner2 [codes]="['2051269', '2051272']"> </app-banner2>
    <app-banner3> </app-banner3>
  </div>

  }

  <div class="relative w-full h-full" #readingContainer>
    <div
      #imageContainer
      id="image-container"
      class="flex-grow min-h-screen mx-0 lg:mx-50"
    >
      <div
        class="scroll-buttons absolute flex justify-between w-full z-20 h-1/4 top-1/3"
        [ngClass]="{
          hidden: isVertical || isFullScreen
        }"
      >
        <button
          (click)="scrollHorizontal(-1)"
          class="bg-secondary-100 opacity-30 hover:opacity-55 bg-gradient-to-r from-black text-white px-4 py-2"
        >
          <svg
            class="h-8 w-8 text-white text-center"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <polyline points="15 6 9 12 15 18" />
          </svg>
          Prev
        </button>
        <button
          (click)="scrollHorizontal(1)"
          class="bg-secondary-100 opacity-30 hover:opacity-55 bg-gradient-to-l from-black text-white px-4 py-2"
        >
          <svg
            class="h-8 w-8 text-white text-center"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="9 18 15 12 9 6" />
          </svg>
          Next
        </button>
      </div>
      <div *ngIf="isLoading" class="mx-auto justify-center h-full">
        <div class="my-4" *ngFor="let page of [1, 2, 23, 4, 5]">
          <svg
            class="size-20 animate-spin text-neutral-300"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <circle cx="12" cy="12" r="3" />
            <line x1="12" y1="21" x2="12" y2="21.01" />
            <line x1="3" y1="9" x2="3" y2="9.01" />
            <line x1="21" y1="9" x2="21" y2="9.01" />
            <path d="M8 20.1a9 9 0 0 1 -5 -7.1" />
            <path d="M16 20.1a9 9 0 0 0 5 -7.1" />
            <path d="M6.2 5a9 9 0 0 1 11.4 0" />
          </svg>
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <ng-container *ngIf="isBrowser && !isLoading">
        <div
          *ngFor="let page of listImg; let i = index; trackBy: trackByPageId"
          class="block relative object-contain ease-in-out mx-auto"
          [style.width]="this.defaultWidth * (1 + zoomLevel) + 'px'"
        >
          <img
            [loading]="i <= 5 ? 'eager' : 'lazy'"
            class="object-cover w-full h-full min-h-48"
            [alt]="comic.url + 'chapter-page-' + (i + 1)"
            #chapterPageImage
            [ngClass]="{
              'min-w-[20rem] lg:min-w-[28rem] h-full': !isVertical,
              'night-mode': isNightMode
            }"
            [src]="page.url"
            (error)="onError($event)"
            (load)="onLoad($event)"
          />
        </div>
      </ng-container>
    </div>

    <div #EndChapter class="flex justify-center w-full gap-5 mt-5">
      <button
        class="chapter-navigation"
        (click)="navigateChapter(true)"
        aria-label="Previous chapter"
        [disabled]="isLoading"
        [ngClass]="{
          ' !bg-primary-100':
            listChapters.length > 0 &&
            mainChapter.slug !== listChapters![listChapters!.length - 1].slug
        }"
      >
        <svg
          class="h-8 w-8 text-white"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <polyline points="15 6 9 12 15 18" />
        </svg>
        <p class="mr-4">Quay lại</p>
      </button>
      <button
        class="chapter-navigation"
        (click)="navigateChapter(false)"
        aria-label="Next chapter"
        [disabled]="isLoading"
        [ngClass]="{
          ' !bg-primary-100':
            listChapters.length > 0 &&
            mainChapter.slug !== listChapters![0].slug
        }"
      >
        <p class="ml-4">Đọc tiếp</p>
        <svg
          class="h-8 w-8 text-white"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="9 18 15 12 9 6" />
        </svg>
      </button>
    </div>
    @if(isBrowser && !isLoading) {
    <div class="my-5">
      <app-banner2 [codes]="['2052076', '2052074']"> </app-banner2>
    </div>
    } @if (isBrowser) {
    <app-comment
      #commentComponent
      [comicId]="comic.id"
      [chapterID]="mainChapter.id"
    ></app-comment>
    }
  </div>

  <button
    (click)="scrollToTop($event)"
    class="fixed bottom-10 right-2 lg:right-10 z-40 mr-4 mb-4 transition-opacity duration-1000 ease-in-out"
    title="Back To Top"
    type="button"
    [ngClass]="{ 'opacity-0': !showScrollToTop }"
  >
    <svg
      class="h-8 w-8 lg:h-10 lg:w-10 text-white bg-secondary-100 rounded cursor-pointer hover:-translate-y-1 duration-100 ease-in-out p-1 -rotate-45"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      stroke-width="2"
      stroke="currentColor"
      fill="none"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <path stroke="none" d="M0 0h24v24H0z" />
      <path
        d="M4 13a8 8 0 0 1 7 7a6 6 0 0 0 3 -5a9 9 0 0 0 6 -8a3 3 0 0 0 -3 -3a9 9 0 0 0 -8 6a6 6 0 0 0 -5 3"
      />
      <path d="M7 14a6 6 0 0 0 -3 6a6 6 0 0 0 6 -3" />
      <circle cx="15" cy="9" r="1" />
    </svg>
  </button>
</div>
