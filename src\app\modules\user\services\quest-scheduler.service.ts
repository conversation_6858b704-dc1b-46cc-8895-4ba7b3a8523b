import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { interval, Subscription, timer } from 'rxjs';
import { switchMap, filter } from 'rxjs/operators';
import { QuestService } from './daily-quest.service';

@Injectable({
  providedIn: 'root'
})
export class QuestSchedulerService implements OnDestroy {
  private refreshSubscription?: Subscription;
  private lastDailyReset: Date | null = null;
  private lastWeeklyReset: Date | null = null;

  constructor(private questService: QuestService) {
    this.initializeScheduler();
  }

  ngOnDestroy(): void {
    this.stopScheduler();
  }

  private initializeScheduler(): void {
    // Check every minute for quest resets
    this.refreshSubscription = interval(60000).pipe(
      filter(() => this.shouldCheckForReset()),
      switchMap(() => this.questService.checkAndRefreshQuests())
    ).subscribe({
      next: (refreshStatus) => {
        if (refreshStatus.dailyRefreshed) {
          console.log('Daily quests auto-refreshed');
          this.lastDailyReset = new Date();
        }
        
        if (refreshStatus.weeklyRefreshed) {
          console.log('Weekly quests auto-refreshed');
          this.lastWeeklyReset = new Date();
        }
      },
      error: (error) => {
        console.error('Quest scheduler error:', error);
      }
    });

    // Initial check
    this.performInitialCheck();
  }

  private shouldCheckForReset(): boolean {
    const now = new Date();
    
    // Check if it's time for daily reset (midnight)
    const isDailyResetTime = this.isDailyResetTime(now);
    const isWeeklyResetTime = this.isWeeklyResetTime(now);
    
    return isDailyResetTime || isWeeklyResetTime;
  }

  private isDailyResetTime(now: Date): boolean {
    if (!this.lastDailyReset) return true;
    
    const lastResetDate = new Date(this.lastDailyReset);
    const currentDate = new Date(now);
    
    // Reset at midnight
    const lastMidnight = new Date(currentDate);
    lastMidnight.setHours(0, 0, 0, 0);
    
    const lastResetMidnight = new Date(lastResetDate);
    lastResetMidnight.setHours(0, 0, 0, 0);
    
    return lastMidnight.getTime() > lastResetMidnight.getTime();
  }

  private isWeeklyResetTime(now: Date): boolean {
    if (!this.lastWeeklyReset) return true;
    
    const lastResetDate = new Date(this.lastWeeklyReset);
    const currentDate = new Date(now);
    
    // Reset on Monday at midnight
    const currentWeekStart = this.getWeekStart(currentDate);
    const lastResetWeekStart = this.getWeekStart(lastResetDate);
    
    return currentWeekStart.getTime() > lastResetWeekStart.getTime();
  }

  private getWeekStart(date: Date): Date {
    const weekStart = new Date(date);
    const day = weekStart.getDay();
    const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1); // Monday
    weekStart.setDate(diff);
    weekStart.setHours(0, 0, 0, 0);
    return weekStart;
  }

  private performInitialCheck(): void {
    // Delay initial check by 5 seconds to allow app to initialize
    timer(5000).pipe(
      switchMap(() => this.questService.checkAndRefreshQuests())
    ).subscribe({
      next: (refreshStatus) => {
        console.log('Initial quest check completed:', refreshStatus);
        this.lastDailyReset = new Date();
        this.lastWeeklyReset = new Date();
      },
      error: (error) => {
        console.error('Initial quest check failed:', error);
      }
    });
  }

  public forceRefreshDaily(): void {
    this.questService.refreshDailyQuests().subscribe({
      next: (success) => {
        if (success) {
          console.log('Daily quests manually refreshed');
          this.lastDailyReset = new Date();
        }
      },
      error: (error) => {
        console.error('Failed to manually refresh daily quests:', error);
      }
    });
  }

  public forceRefreshWeekly(): void {
    this.questService.refreshWeeklyQuests().subscribe({
      next: (success) => {
        if (success) {
          console.log('Weekly quests manually refreshed');
          this.lastWeeklyReset = new Date();
        }
      },
      error: (error) => {
        console.error('Failed to manually refresh weekly quests:', error);
      }
    });
  }

  public getTimeUntilDailyReset(): number {
    const now = new Date();
    const nextMidnight = new Date(now);
    nextMidnight.setDate(nextMidnight.getDate() + 1);
    nextMidnight.setHours(0, 0, 0, 0);
    
    return nextMidnight.getTime() - now.getTime();
  }

  public getTimeUntilWeeklyReset(): number {
    const now = new Date();
    const nextMonday = this.getNextMonday(now);
    
    return nextMonday.getTime() - now.getTime();
  }

  private getNextMonday(date: Date): Date {
    const nextMonday = new Date(date);
    const day = nextMonday.getDay();
    const daysUntilMonday = day === 0 ? 1 : 8 - day; // If Sunday, next Monday is 1 day away
    
    nextMonday.setDate(nextMonday.getDate() + daysUntilMonday);
    nextMonday.setHours(0, 0, 0, 0);
    
    return nextMonday;
  }

  public formatTimeRemaining(milliseconds: number): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 24) {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      return `${days} ngày ${remainingHours} giờ`;
    } else if (hours > 0) {
      return `${hours} giờ ${minutes} phút`;
    } else if (minutes > 0) {
      return `${minutes} phút ${seconds} giây`;
    } else {
      return `${seconds} giây`;
    }
  }

  public stopScheduler(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
      this.refreshSubscription = undefined;
    }
  }

  public startScheduler(): void {
    if (!this.refreshSubscription) {
      this.initializeScheduler();
    }
  }

  public getSchedulerStatus(): {
    isRunning: boolean;
    lastDailyReset: Date | null;
    lastWeeklyReset: Date | null;
    timeUntilDailyReset: number;
    timeUntilWeeklyReset: number;
  } {
    return {
      isRunning: !!this.refreshSubscription,
      lastDailyReset: this.lastDailyReset,
      lastWeeklyReset: this.lastWeeklyReset,
      timeUntilDailyReset: this.getTimeUntilDailyReset(),
      timeUntilWeeklyReset: this.getTimeUntilWeeklyReset()
    };
  }
}
