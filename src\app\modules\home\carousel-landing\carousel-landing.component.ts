import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule, isPlatformServer } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  NgZone,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  QueryList,
  ViewChild,
  ViewChildren
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { Comic, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { interval, Subscription, timer } from 'rxjs';
import { SwiperComponent } from 'src/app/modules/home/<USER>/page/swiper/swiper.component';
import { CarouselLayoutComponent } from './page/carousel-layout/carousel-layout.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import globalConfig from 'GlobalConfig';
import { ComicDescriptionlPipe } from '@pines/description.pipe';
import { NumeralPipe } from '@pines/numeral.pipe';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
@Component({
  selector: 'app-carousel-landing',
  templateUrl: './carousel-landing.component.html',
  styleUrl: './carousel-landing.component.scss',
  animations: [
    trigger('formAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(100%)' })),
      state('*', style({ opacity: 1, transform: 'translateY(0)' })),
      transition('void => *', [animate('200ms ease-out')]),
      transition('* => void', [animate('200ms ease-in')]),
    ]),
  ],
  standalone: true,
  imports: [
    SwiperComponent,
    CommonModule,
    RouterLink,
    CarouselLayoutComponent,
    SpinnerComponent,
    ComicDescriptionlPipe,
    NumeralPipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CarouselLandingComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  // Component state
  carouselItems: Comic[][] = [];
  lastTime = 0;
  isTransitioning = false;
  hoverComic?: Comic;
  _state = 'out';
  TypeUI = 0;
  grid = 3;
  comicList: Comic[] = [];
  currentIdx = 0;
  attrs: any[] = [];
  subscription?: Subscription;

  // Performance optimizations
  private readonly AUTO_SLIDE_INTERVAL = 5000;
  private readonly INITIAL_DELAY = 3000;
  private readonly TRANSITION_THROTTLE = 500;
  private readonly DESKTOP_GRID_SIZE = 3;
  private readonly MOBILE_GRID_SIZE_SM = 3;
  private readonly MOBILE_GRID_SIZE_MD = 4;

  private debouncedNext!: Function;
  private debouncedPrev!: Function;

  @ViewChild(SwiperComponent) swiperComponent!: SwiperComponent;
  @ViewChildren("carouselItemPc") carouselElePcs!: QueryList<ElementRef>;
  @ViewChildren("carouselItemMobile") carouselEleMobiles!: QueryList<ElementRef>;
  carouselEles!: QueryList<ElementRef>;

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    @Inject(PLATFORM_ID) override platformId: object,
    private comicService: ComicService
  ) {
    super(cdr, platformId);
    this.setupDebouncedMethods();
  }

  // Computed properties for better performance
  get hasCarouselItems(): boolean {
    return this.carouselItems.length > 0;
  }

  get isDesktop(): boolean {
    return this.TypeUI === 0;
  }

  get isMobile(): boolean {
    return this.TypeUI === 1;
  }

  get currentCarouselElements(): QueryList<ElementRef> {
    return this.isDesktop ? this.carouselElePcs : this.carouselEleMobiles;
  }

  get shouldShowHoverDetails(): boolean {
    return !!this.hoverComic && this.isDesktop;
  }

  get isServerSideRendering(): boolean {
    return isPlatformServer(this.platformId);
  }

  // TrackBy functions for ngFor optimization
  trackByCarouselIndex = (index: number): number => index;
  trackByComicId = (index: number, comic: Comic): number => comic.id;
  ngOnInit(): void {
    this.runInBrowser(() => {
      this.computeStyleUI();
      this.setupInitialDelay();
    });
    this.loadRecommendedComics();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  private setupDebouncedMethods(): void {
    this.debouncedNext = this.debounce(this.performNext.bind(this), 100);
    this.debouncedPrev = this.debounce(this.performPrev.bind(this), 100);
  }

  private setupInitialDelay(): void {
    this.addSubscription(
      timer(this.INITIAL_DELAY).subscribe(() => {
        this.resetAutoSlide();
      })
    );
  }

  private loadRecommendedComics(): void {
    this.addSubscription(
      this.comicService.getRecommendComics().subscribe((res: IServiceResponse<Comic[]>) => {
        if (!res.data) return;

        if (this.isServerSideRendering) {
          this.carouselItems = this.chunk<Comic>(res.data, 5).slice(0, 4);
          this.comicList = res.data.slice(0, 4);
        } else {
          this.carouselItems = this.chunk<Comic>(res.data, 5);
          this.comicList = res.data.slice(0, 10);
        }

        this.safeMarkForCheck();
      })
    );
  }
  chunk<T>(array: T[], size: number): T[][] {
    const result: T[][] = [];

    for (let i = 0; i < array.length; i += size) {
      // Slice the array from the current index to the next 'size' elements
      const chunkedArray = array.slice(i, i + size);
      result.push(chunkedArray); // Add the chunk to the result array
    }

    return result; // Return the array of chunks
  }

  OnComicLeave(): void {
    this._state = 'out';
    this.startAutoSlide();
  }

  private computeStyleUI(): void {
    const windowWidth = window.innerWidth;
    const lgBreakpoint = globalConfig.GetScreenSize('lg');
    const smBreakpoint = globalConfig.GetScreenSize('sm');

    if (windowWidth <= lgBreakpoint) {
      this.TypeUI = 1;
      this.grid = windowWidth <= smBreakpoint ? this.MOBILE_GRID_SIZE_SM : this.MOBILE_GRID_SIZE_MD;
    } else {
      this.TypeUI = 0;
      this.grid = this.DESKTOP_GRID_SIZE;
    }
  }
  @HostListener('document:visibilitychange', ['$event'])
  onDocumentVisibilityChange(): void {
    this.runInBrowser(() => {
      if (document.hidden) {
        this.stopAutoSlide();
      } else {
        this.startAutoSlide();
      }
    });
  }

  OnComicHover(comic: Comic): void {
    if (this.hoverComic === comic) return;
    this.stopAutoSlide();
    this.hoverComic = comic;
    this.safeMarkForCheck();
  }

  private startAutoSlide(): void {
    this.stopAutoSlide();
    this.ngZone.runOutsideAngular(() => {
      this.subscription = interval(this.AUTO_SLIDE_INTERVAL).subscribe(() => this.prev())
    })
    
  }

  private stopAutoSlide(): void {
    this.subscription?.unsubscribe();
  }

  next(step = 1): void {
    this.debouncedNext(step);
  }

  prev(step = 1): void {
    this.debouncedPrev(step);
  }

  private performNext(): void {
    if (!this.canTransition()) return;
    this.updateLastTime();
    this.performRecalculate(true);
    this.swiperComponent?.next();
  }

  private performPrev(): void {
    if (!this.canTransition()) return;
    this.updateLastTime();
    this.performRecalculate(false);
    this.swiperComponent?.prev();
  }

  private canTransition(): boolean {
    const now = Date.now();
    return now - this.lastTime >= this.TRANSITION_THROTTLE;
  }

  private updateLastTime(): void {
    this.lastTime = Date.now();
  }

  private performRecalculate(isNext = true): void {
    this.carouselEles = this.currentCarouselElements;
    if (this.carouselEles.length === 0) return;

    this.runInBrowser(() => {
      this.updateElementVisibility(isNext);
      this.updateCurrentIndex(isNext);
      this.updateElementPositions();
    });
  }

  private updateElementVisibility(isNext: boolean): void {
    this.carouselEles.forEach((e: ElementRef, index) => {
      const value = (index + this.currentIdx) % this.carouselEles.length;
      e.nativeElement.classList.add('carousel-item-hidden');

      if (isNext && value <= this.grid) {
        e.nativeElement.classList.remove('carousel-item-hidden');
      } else if (!isNext && value >= 1 && value <= this.grid + 1) {
        e.nativeElement.classList.remove('carousel-item-hidden');
      }
    });
  }

  private updateCurrentIndex(isNext: boolean): void {
    const direction = isNext ? 1 : -1;
    this.currentIdx = (this.currentIdx + direction + this.carouselEles.length) % this.carouselEles.length;
  }

  private updateElementPositions(): void {
    this.carouselEles.forEach((e: ElementRef, index) => {
      const value = (index + this.currentIdx) % this.carouselEles.length - 1;
      e.nativeElement.style.left = `${(value * 100) / this.grid}%`;
      e.nativeElement.style.width = `${100 / this.grid}%`;
    });
  }

  private resetAutoSlide(): void {
    this.startAutoSlide();
  }
  SlideChange(direct: number): void {
    this.performRecalculate(direct > 0);
  }

}
