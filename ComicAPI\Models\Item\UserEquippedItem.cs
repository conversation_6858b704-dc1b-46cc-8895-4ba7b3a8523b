using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models.Item
{
    public class UserEquippedItem
    {
        [Required, Column("userid")]
        public int UserID { get; set; }

        [Required, Column("itemtemplateid")]
        public int ItemTemplateID { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(50), Column("slottype")]
        public string SlotType { get; set; } = string.Empty;

        [Column("equippedat")]
        public DateTime EquippedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual ItemTemplate ItemTemplate { get; set; } = null!;
    }

    public enum EquipmentSlotType
    {
        avatar_frame,
        title,
        badge
    }
}
