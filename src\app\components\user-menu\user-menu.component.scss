// User Menu Component Styles
.user-menu-wrapper {
  @apply relative;
}

.user-menu-trigger {
  @apply flex items-center gap-3 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 cursor-pointer border-none bg-transparent;


  // Guest user trigger styles
  &.guest {
    @apply bg-gray-200 dark:bg-gray-700;

    &:hover {
      @apply bg-gray-300 dark:bg-gray-600;
    }


  }
}

.user-avatar-container {
  @apply relative;
}

.user-avatar {
  @apply w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 transition-all duration-200;
}

.user-status-indicator {
  @apply absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full;
}

.user-info-preview {
  @apply flex items-center gap-2;
}

.user-name-preview {
  @apply font-medium text-gray-900 dark:text-white text-sm;
}

.dropdown-chevron {
  @apply w-4 h-4 text-gray-500 dark:text-neutral-400 transition-transform duration-200;

  &.rotated {
    transform: rotate(180deg);
  }
}

// Dropdown Container
.user-dropdown-container {
  @apply absolute right-0 top-full mt-3 w-72 md:w-80 z-50 transition-all duration-300;

  &.hidden {
    @apply opacity-0 invisible transform scale-95 translate-y-2;
  }

  &._visible {
    @apply opacity-100 visible transform scale-100 translate-y-0;
  }
}

.user-dropdown {
  @apply bg-white dark:bg-neutral-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-neutral-700 overflow-hidden;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);

  .dark & {
    background: rgba(38, 38, 38, 0.95);
  }
}

// User Profile Header
.user-profile-header {
  @apply p-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-neutral-800 dark:to-neutral-900 border-b border-gray-200 dark:border-neutral-700;
}

.user-avatar-large {
  @apply relative mb-4;

  img {
    @apply w-16 h-16 rounded-full object-cover border-4 border-white dark:border-neutral-700 shadow-lg;
  }
}

.user-status-badge {
  @apply absolute -bottom-1 -right-1 flex items-center gap-1 bg-green-500 text-white text-xs px-2 py-1 rounded-full;
}

.status-dot {
  @apply w-2 h-2 bg-white rounded-full animate-pulse;
}

.status-text {
  @apply font-medium;
}

.user-profile-info {
  @apply space-y-2;
}

.user-full-name {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.user-email {
  @apply text-sm text-gray-600 dark:text-neutral-400;
}

.user-stats {
  @apply mt-3;
}

.stat-item {
  @apply flex items-center gap-2 text-sm text-gray-700 dark:text-neutral-300;
}

.stat-icon {
  @apply w-4 h-4;
}

// Quick Actions
.quick-actions {
  @apply flex items-center justify-center gap-2 p-4 border-b border-gray-200 dark:border-neutral-700;
}

.quick-action-btn {
  @apply relative p-3 bg-gray-100 dark:bg-neutral-700 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded transition-all duration-200 border-none cursor-pointer;

  svg {
    @apply w-5 h-5 text-gray-600 dark:text-neutral-400;
  }

  &:hover svg {
    @apply text-blue-600 dark:text-blue-400;
  }


}

.notification-badge {
  @apply absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold;
}

// Navigation Menu
.user-navigation {
  @apply py-2;
}

.nav-menu-list {
  @apply space-y-1;
}

.nav-menu-item {
  @apply mx-2;
}

.nav-link {
  @apply flex items-center gap-3 px-4 py-3 text-gray-700 dark:text-neutral-300 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded transition-all duration-200;

  &.nav-button {
    @apply border-none bg-transparent cursor-pointer w-full text-left;
  }

  &:hover {
    @apply text-gray-900 dark:text-white;

    .nav-arrow {
      @apply text-blue-500 transform translate-x-1;
    }
  }
}

.nav-icon {
  @apply w-5 h-5 text-gray-500 dark:text-neutral-400;
}

.nav-text {
  @apply flex-1 font-medium;
}

.nav-arrow {
  @apply w-4 h-4 text-gray-400 dark:text-neutral-500 transition-all duration-200;
}

// Menu Divider
.menu-divider {
  @apply h-px bg-gray-200 dark:bg-neutral-700 mx-4 my-2;
}

// Logout Section
.logout-section {
  @apply p-2;
}

.logout-button {
  @apply flex items-center gap-3 w-full px-4 py-3 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-all duration-200 border-none bg-transparent cursor-pointer;

}

.logout-icon {
  @apply w-5 h-5;
}

// Backdrop
// .dropdown-backdrop {
//   @apply fixed inset-0 z-40 bg-black/20 backdrop-blur-sm;
// }


// Guest User Styles
.guest-avatar-container {
  @apply relative flex items-center justify-center;
}

.guest-avatar-icon {
  @apply size-6 text-gray-600 dark:text-neutral-400;
}

.guest-info-preview {
  @apply flex items-center gap-2;
}

.guest-name-preview {
  @apply font-medium text-gray-700 dark:text-neutral-300 text-sm;
}

// Guest Dropdown Styles
.guest-dropdown {
  @apply w-80;
}

.guest-profile-header {
  @apply p-6 bg-gradient-to-br from-gray-50 to-blue-50 dark:from-neutral-800 dark:to-blue-900/20 border-b border-gray-200 dark:border-neutral-700;
}

.guest-avatar-large {
  @apply flex items-center justify-center mb-4;
}

.guest-avatar-icon-large {
  @apply w-20 h-20 text-gray-400 dark:text-neutral-500 p-4 bg-gray-100 dark:bg-neutral-700 rounded-full;
}

.guest-profile-info {
  @apply text-center space-y-3;
}

.guest-welcome-title {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.guest-welcome-subtitle {
  @apply text-gray-600 dark:text-neutral-400;
}

.guest-features {
  @apply space-y-2 mt-4;
}

.feature-item {
  @apply flex items-center gap-2 text-sm text-gray-700 dark:text-neutral-300;
}

.feature-icon {
  @apply w-4 h-4 text-blue-500 dark:text-blue-400;
}

// Authentication Section
.auth-section {
  @apply p-4 space-y-3;
}

.auth-button {
  @apply flex items-center justify-center gap-3 w-full px-4 py-3 rounded font-medium transition-all duration-200 border-none cursor-pointer;

  &.login-button {
    @apply bg-primary-100 text-white hover:bg-primary-200 shadow-lg hover:shadow-xl;
  }

  &.register-button {
    @apply bg-gray-100 dark:bg-neutral-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-neutral-600;
  }

  &:focus {
    @apply outline-none ring-2 ring-blue-500/50;
  }
}

.auth-icon {
  @apply w-5 h-5;
}

// Guest Quick Links
.guest-quick-links {
  @apply p-4 border-t border-gray-200 dark:border-neutral-700;
}

.quick-links-title {
  @apply text-sm font-semibold text-gray-900 dark:text-white mb-3;
}

.quick-links-grid {
  @apply grid grid-cols-3 gap-2;
}

.quick-link-item {
  @apply flex flex-col items-center gap-2 p-3 bg-gray-50 dark:bg-neutral-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200;

  &:hover {
    @apply text-blue-600 dark:text-blue-400;

    .quick-link-icon {
      @apply text-blue-600 dark:text-blue-400;
    }
  }
}

.quick-link-icon {
  @apply w-6 h-6 text-gray-500 dark:text-neutral-400 transition-colors duration-200;
}

.quick-link-item span {
  @apply text-xs font-medium text-gray-700 dark:text-neutral-300;
}

// // Backdrop
// .dropdown-backdrop {
//   @apply fixed inset-0 z-40 bg-black/20 backdrop-blur-sm;
// }

// Fix for visible class
.user-dropdown-container.visible1 {
  @apply opacity-100 visible transform scale-100 translate-y-0;
}

