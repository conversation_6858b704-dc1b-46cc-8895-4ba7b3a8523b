import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  PLATFORM_ID,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { Chapter } from '@schema';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';
import { OptimizedBaseComponent } from '../base/optimized-base.component';
import { LoopScrollComponent } from '../loop-scroll/loop-scroll.component';

@Component({
  selector: 'app-chapter-selector',
  templateUrl: './chapter-selector.component.html',
  styleUrl: './chapter-selector.component.scss',
  standalone: true,
  imports: [CommonModule, LoopScrollComponent, RouterLink, ClickOutsideDirective,DateAgoPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChapterSelectorComponent extends OptimizedBaseComponent implements OnInit, OnChanges, OnDestroy {
  // Input properties
  @Input() topToBottom = false;
  @Input() chapters: Chapter[] = [];
  @Input() mainChapter: Chapter | null = null;

  // Output events
  @Output() chapterChange = new EventEmitter<number>();

  // Component state
  isDropdownOpen = false;
  searchTerm = '';
  filteredChapters: Chapter[] = [];

  // Performance optimizations
  private searchSubject = new Subject<string>();
  private readonly SEARCH_DEBOUNCE_TIME = 300;

  @ViewChild('dropdownList') dropdownList!: ElementRef;

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
    this.setupSearchHandler();
  }

  // Computed properties for better performance
  get hasChapters(): boolean {
    return this.chapters.length > 0;
  }

  get hasFilteredChapters(): boolean {
    return this.filteredChapters.length > 0;
  }

  get displayTitle(): string {
    if (!this.mainChapter?.title) return 'Select Chapter';
    return `#Chapter ${this.mainChapter.slug}`;
  }


  get shouldShowSearch(): boolean {
    return this.hasFilteredChapters;
  }

  // TrackBy functions for ngFor optimization
  trackByChapterId = (index: number, chapter: Chapter): number => {
    return chapter.id;
  };

  private setupSearchHandler(): void {
    this.searchSubject
      .pipe(
        debounceTime(this.SEARCH_DEBOUNCE_TIME),
        distinctUntilChanged(),
        this.takeUntilDestroy()
      )
      .subscribe((searchTerm) => {
        this.performSearch(searchTerm as string);
      });
  }

  ngOnInit(): void {
    this.initializeFilteredChapters();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['chapters']) {
      this.initializeFilteredChapters();
      this.closeDropdown();
    }
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    if (this.isDropdownOpen) {
      this.resetSearch();
    }
  }

  selectChapter(chapter: Chapter): void {
    this.chapterChange.emit(chapter.id);
    this.closeDropdown();
  }

  onSearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    const searchTerm = target.value?.toLowerCase() || '';
    this.searchTerm = searchTerm;
    this.searchSubject.next(searchTerm);
  }

  private initializeFilteredChapters(): void {
    this.filteredChapters = [...this.chapters];
  }

  private performSearch(searchTerm: string): void {
    this.filteredChapters = searchTerm
      ? this.chapters.filter((chapter: Chapter) =>
          chapter.title?.toLowerCase().includes(searchTerm)
        )
      : [...this.chapters];

    this.safeMarkForCheck();
  }

  private closeDropdown(): void {
    this.isDropdownOpen = false;
    this.resetSearch();
  }

  resetSearch(): void {
    this.searchTerm = '';
    this.initializeFilteredChapters();
    this.safeMarkForCheck();
  }

}
