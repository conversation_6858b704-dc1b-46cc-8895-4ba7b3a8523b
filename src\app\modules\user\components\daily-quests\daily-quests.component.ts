import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Observable, Subject, takeUntil } from 'rxjs';
import {
  DailyQuest,
  QuestDifficulty,
  QuestStatus,
  RewardType,
  UserQuestStats,
  WeeklyQuest
} from '../../interfaces/daily-quest.interface';
import { DailyQuestService } from '../../services/daily-quest.service';

@Component({
  selector: 'app-daily-quests',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './daily-quests.component.html',
  styleUrls: ['./daily-quests.component.scss']
})
export class DailyQuestsComponent implements OnInit, OnDestroy {
  dailyQuests$: Observable<DailyQuest[]>;
  weeklyQuests$: Observable<WeeklyQuest[]>;
  userStats$: Observable<UserQuestStats | null>;
  
  dailyQuests: DailyQuest[] = [];
  weeklyQuests: WeeklyQuest[] = [];
  userStats: UserQuestStats | null = null;
  
  selectedTab: 'daily' | 'weekly' = 'daily';
  
  // Enums for template
  QuestStatus = QuestStatus;
  QuestDifficulty = QuestDifficulty;
  RewardType = RewardType;
  
  private destroy$ = new Subject<void>();

  constructor(private dailyQuestService: DailyQuestService) {
    this.dailyQuests$ = this.dailyQuestService.getDailyQuests();
    this.weeklyQuests$ = this.dailyQuestService.getWeeklyQuests();
    this.userStats$ = this.dailyQuestService.getUserStats();
  }

  ngOnInit(): void {
    this.dailyQuests$
      .pipe(takeUntil(this.destroy$))
      .subscribe(quests => this.dailyQuests = quests);
      
    this.weeklyQuests$
      .pipe(takeUntil(this.destroy$))
      .subscribe(quests => this.weeklyQuests = quests);
      
    this.userStats$
      .pipe(takeUntil(this.destroy$))
      .subscribe(stats => this.userStats = stats);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  switchTab(tab: 'daily' | 'weekly'): void {
    this.selectedTab = tab;
  }

  getProgressPercentage(quest: DailyQuest | WeeklyQuest): number {
    return Math.min((quest.current / quest.target) * 100, 100);
  }

  getDifficultyColor(difficulty: QuestDifficulty): string {
    switch (difficulty) {
      case QuestDifficulty.EASY:
        return 'text-green-500';
      case QuestDifficulty.MEDIUM:
        return 'text-yellow-500';
      case QuestDifficulty.HARD:
        return 'text-orange-500';
      case QuestDifficulty.LEGENDARY:
        return 'text-purple-500';
      default:
        return 'text-gray-500';
    }
  }

  getDifficultyBadgeColor(difficulty: QuestDifficulty): string {
    switch (difficulty) {
      case QuestDifficulty.EASY:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case QuestDifficulty.MEDIUM:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case QuestDifficulty.HARD:
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case QuestDifficulty.LEGENDARY:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  getRewardIcon(rewardType: RewardType): string {
    switch (rewardType) {
      case RewardType.COINS:
        return '🪙';
      case RewardType.EXPERIENCE:
        return '⭐';
      case RewardType.BADGE:
        return '🏆';
      case RewardType.AVATAR_FRAME:
        return '🖼️';
      case RewardType.TITLE:
        return '👑';
      case RewardType.PREMIUM_DAYS:
        return '💎';
      default:
        return '🎁';
    }
  }

  getQuestIcon(iconName: string): string {
    const iconMap: { [key: string]: string } = {
      'book-open': '📖',
      'message-circle': '💬',
      'heart': '❤️',
      'star': '⭐',
      'compass': '🧭',
      'trophy': '🏆',
      'award': '🥇'
    };
    return iconMap[iconName] || '📋';
  }

  claimReward(quest: DailyQuest | WeeklyQuest): void {
    if (quest.status === QuestStatus.COMPLETED) {
      this.dailyQuestService.claimReward(quest.id).subscribe(success => {
        if (success) {
          console.log('Reward claimed successfully!');
          // Here you would typically update the quest status or show a success message
        }
      });
    }
  }

  getTimeRemaining(expiresAt: Date): string {
    const now = new Date();
    const timeLeft = expiresAt.getTime() - now.getTime();
    
    if (timeLeft <= 0) {
      return 'Đã hết hạn';
    }
    
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days} ngày`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  getExperiencePercentage(): number {
    if (!this.userStats) return 0;
    return (this.userStats.experience / this.userStats.nextLevelExp) * 100;
  }

  trackByQuestId(index: number, quest: DailyQuest | WeeklyQuest): string {
    return quest.id;
  }
}
