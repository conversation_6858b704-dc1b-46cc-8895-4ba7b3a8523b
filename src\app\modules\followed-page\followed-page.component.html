<ng-container>
  <app-breadcrumb
    class="z-10 my-2 container mx-auto flex"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Đang theo dõi', url: '' }
    ]"
  >
  </app-breadcrumb>
  <div
    class="text-center absolute top-1/2 left-1/2"
    *ngIf="isLoading; else loadedContent"
  >
    <app-spinner [sizeSpinner]="'40'"></app-spinner>
  </div>
  <ng-template #loadedContent>
    <div
      id="comics"
      class="md:container mx-auto"
      *ngIf="comics.length > 0; else empty"
    >
      <app-grid-comic
        [listComics]="comics"
        [nPreview]="28"
        [title]="'Đang theo dõi'"
        [_class]="
          'grid gap-[12px] grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 mx-3 lg:mx-0'
        "
        (clickEvent)="onUnFollowClick($event)"
        [eventName]="'H<PERSON>y theo dõi'"
      >
       <svg class="size-6"  width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">  <path stroke="none" d="M0 0h24v24H0z"/>  <path d="M12 20l-7 -7a4 4 0 0 1 6.5 -6a.9 .9 0 0 0 1 0a4 4 0 0 1 6.5 6l-7 7" /></svg>
      </app-grid-comic>
      <app-pagination
        [totalpage]="totalpage"
        (OnChange)="OnChangePage($event)"
        [currentPage]="currentPage"
      >
      </app-pagination>
    </div>
    <ng-template #empty>
      <div
        class="w-1/3 mt-20 lg:min-h-1/2 lg:w-40 flex justify-center items-center mx-auto"
      >
        <app-empty [message]="'Không có truyện đang theo dõi'"></app-empty>
      </div>
    </ng-template>
  </ng-template>
</ng-container>
